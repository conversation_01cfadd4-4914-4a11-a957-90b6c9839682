<template>
  <el-dialog
    title="数据汇集"
    :visible.sync="dialogVisible"
    :append-to-body="true"
    width="70%"
    :close-on-click-modal="true"
    class="custom-dialog"
    :show-close="false"
  >
    <el-table
      :data="tableData"
      style="width: 100%"
      header-cell-class-name="custom-header"
      cell-class-name="custom-cell"
      height="51vh"
    >
      <el-table-column prop="category_item_cn" :width="`${this.$autoFontSize(600)}`"   label="数据资源名称" />
      <el-table-column
        prop="data_num"
        label="数据量（条）"
      />
      <el-table-column prop="data_source_cn" label="数据源单位" />
    </el-table>
    <div style="text-align: center; margin-top: 20px">
      <el-pagination
        style="text-align: right"
        layout="prev, pager, next"
        :total="total"
        :page-size="10"
        :current-page.sync="currentPage"
      />
    </div>
  </el-dialog>
</template>

<script>
import { Dialog, Table, TableColumn, Pagination } from "element-ui";
import { productUpdatePage } from "@/api/article.js";
export default {
  components: {
    ElDialog: Dialog,
    ElTable: Table,
    ElTableColumn: TableColumn,
    ElPagination: Pagination,
  },
  data() {
    return {
      dialogVisible: false,
      currentPage: 1,
      total: "",
      tableData: [],
    };
  },
  props: {
    maxDt: {
      type: String,
      required: true,
    },
    isMaxDtReady: {
      type: Boolean,
      required: true,
    },
    //区域
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  mounted() {
    this.getTableData();
  },
  watch: {
    currentPage(newVal) {
      this.getTableData();
    },
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用其他接口
          this.getTableData();
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.getTableData(newVal);
      },
    },
  },
  methods: {
    openDialog() {
      this.dialogVisible = true;
    },
    getTableData() {
      productUpdatePage({
        indexName: "数据汇集明细",
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimPark: "ALL",
        dimIndustrychain: "ALL",
        dimNum: 1,
        dimCounty: this.selectedRegion || "ALL",
        page: this.currentPage,
        size: 10,
      }).then((res) => {
        this.tableData = res?.data?.data?.records || [];
        this.total = res?.data?.data?.total || "0";
      });
    },
  },
};
</script>

<style>
/* 弹框整体样式 */
.custom-dialog .el-dialog {
  padding: 20px;
  background: url("~@/assets/dp/dialogMax.png");
  background-size: 100% 100%;
  border-radius: 0;
  width: 60% !important;
  height: 66% !important;
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  right: 0;
  margin: auto;
}

/* 弹框标题栏样式 */
.el-table th.el-table__cell > .cell {
  display: flex !important;
}
.el-table th.el-table__cell .gutter {
  background-color: rgba(10, 29, 56, 0.5) !important;
}
/* 关闭按钮样式 */
.custom-dialog .el-dialog__headerbtn {
  position: absolute;
  top: 15px;
  right: 20px;
  padding: 0;
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  font-size: 16px;
}

.custom-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #fff !important;
  font-size: 40px;
}

.custom-dialog .el-dialog__headerbtn:hover .el-dialog__close {
  color: #409eff !important;
}
.el-table .el-table__cell.gutter {
  width: 0 !important;
  background-color: #0a1d38 !important;
}

.custom-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #fff;
}

/* 弹框内容区域样式 */
.custom-dialog .el-dialog__body {
}
/* 表格表头样式 */
.custom-header {
  padding-left: 60px !important;
  background: #164a7e !important;
  color: #41ccff !important;
  font-weight: bold !important;
  border-color: #3a6894 !important;
  text-align: center !important;
}

/* 表格单元格样式 */
.custom-cell {
  background: #113a5c !important;
  color: #fff !important;
  border-color: #3a6894 !important;
}

/* 表格背景透明 */
.el-table,
.el-table__header-wrapper,
.el-table__body-wrapper {
  background-color: transparent !important;
}

/* 添加表格滚动条样式 */
.el-table__body-wrapper {
  height: 80vh !important; /* 设置最大高度，超出时显示滚动条 */
  overflow-y: auto !important; /* 启用垂直滚动 */
}

/* 自定义滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 25px;
  background-color: rgba(10, 29, 56, 0.5);
}
.el-table::before {
  background-color: none !important;
}
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #168aff;
  border-radius: 4px;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: rgba(22, 74, 126, 0.3);
  border-radius: 4px;
}

/* 表格文字居中 */
.el-table th,
.el-table td {
  text-align: center;
  border-color: #3a6894 !important;
}

/* 表格边框颜色 */
.el-table--border,
.el-table--group {
  border-color: #3a6894 !important;
}

.el-table--border::after,
.el-table--group::after {
  background-color: #3a6894 !important;
}

/* 分页器样式 */
.custom-pagination {
  text-align: center;
  margin-top: 20px;
}

.custom-pagination .el-pagination {
  background-color: transparent;
}

.custom-pagination .el-pagination__total,
.custom-pagination .el-pagination__jump,
.custom-pagination .el-pagination__sizes {
  color: #fff !important;
}

.custom-pagination .btn-prev,
.custom-pagination .btn-next {
  background-color: #0a1d38 !important;
  color: #fff !important;
  border: none !important;
}

.custom-pagination .el-pager li {
  display: flex;
  background-color: #0a1d38 !important;
  color: #fff !important;
  border: none !important;
  margin: 0 2px;
}

/* 修改选中页面的高亮样式 */
.el-pager li.active {
  width: 47px;
  height: 47px;
  line-height: 47px;
  background: linear-gradient(180deg, #168aff 0%, #0066e0 100%) !important;
  box-shadow: inset 0px 0px 8px 0px rgba(255, 255, 255, 0.3),
    0px 0px 15px 0px rgba(22, 138, 255, 0.8) !important;
  border: 2px solid #3da3ff !important;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600 !important;
  font-size: 34px;
  color: #ffffff !important;
  text-shadow: 0px 0px 8px rgba(22, 138, 255, 0.8);
}

/* 选中页面的hover效果 */
.el-pager li.active:hover {
  background: linear-gradient(180deg, #1e90ff 0%, #0080ff 100%) !important;
  box-shadow: inset 0px 0px 10px 0px rgba(255, 255, 255, 0.4),
    0px 0px 20px 0px rgba(22, 138, 255, 1) !important;
}

/* 普通页面的hover效果 */
.el-pager li:hover:not(.active) {
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0.2) 0%,
    rgba(12, 74, 139, 0.7) 100%
  ) !important;
  box-shadow: inset 0px 0px 5px 0px #168aff;
  border: 1px solid rgba(22, 138, 255, 1);
  color: #92c5ff !important;
}

/* 确保表格内容垂直居中 */
.el-table .cell {
  padding-left: 60px;
  display: flex;
  align-items: center;
  justify-content: start;
  font-size: 34px;
  height: 3.3vh;
}
.el-pager li {
  width: 47px;
  height: 47px;
  line-height: 47px;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  ) !important;
  box-shadow: inset 0px 0px 3px 0px #168aff;
  border: 1px solid rgba(110, 160, 227, 0.8);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 34px;
  color: #67a7ff !important;
}
.btn-prev {
  width: 47px;
  height: 47px !important;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  ) !important;
  box-shadow: inset 0px 0px 3px 0px #168aff;
  border: 1px solid rgba(110, 160, 227, 0.8);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #67a7ff !important;
}
.el-icon-arrow-left {
  font-size: 34px !important;
}
.el-icon-arrow-right {
  font-size: 34px !important;
}
.btn-next {
  width: 47px;
  height: 47px !important;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  ) !important;
  box-shadow: inset 0px 0px 3px 0px #168aff;
  border: 1px solid rgba(110, 160, 227, 0.8);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 34px !important;
  color: #67a7ff !important;
}
</style>
