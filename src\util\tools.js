export const formatDate = (split='-') =>{
    let today = new Date();
    let yyyy = today.getFullYear()
    let mm = today.getMonth()+1 < 10  ? '0'+(today.getMonth() + 1) : today.getMonth()+1;
    let dd = today.getDate() < 10 ? '0'+today.getDate() : today.getDate()
    let date = yyyy + split + mm + split + dd
    return date
}
// 明年的今天  
export const formatNextYearDate = (split='-') =>{
    let today = new Date();
    let yyyy = today.getFullYear()+1
    let mm = today.getMonth()+1 < 10  ? '0'+(today.getMonth() + 1) : today.getMonth()+1;
    let dd = today.getDate() < 10 ? '0'+today.getDate() : today.getDate()
    let date = yyyy + split + mm + split + dd
    return date
}