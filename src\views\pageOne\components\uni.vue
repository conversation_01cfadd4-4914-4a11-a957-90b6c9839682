<template>
 <!--  废弃的模块 -->
  <div class="unified-platform">
    <div class="headerImg">统一开放平台</div>
    <div class="platform-summary-box">
    <div class="platform-summary">
      <div>
        累计放款金额：
        <span class="highlight">8989</span>亿
      </div>
      <div>
        累计放款笔数：
        <span class="highlight2">218998</span>笔
      </div>
    </div>
    <div class="platform-cards">
      <div
        v-for="(item, idx) in cardList"
        :key="item.name"
        class="platform-card"
        :class="{ active: idx === activeIndex }"
      >
        <div class="card-header">
          <img :src="item.icon" class="card-icon" />
          <span class="card-title">{{ item.name }}</span>
        </div>
        <div class="card-info-row horizontal">
          <div>
            <div class="info-label">累计融资金额</div>
            <div class="info-value">{{ item.amount }}亿</div>
          </div>
          <div class="info-divider"></div>
          <div>
            <div class="info-label">累计融资笔数</div>
            <div class="info-value">{{ item.count }}笔</div>
          </div>
        </div>
      </div>
    </div>
    <div class="card-switch">
      <span
        class="switch-btn"
        :class="{ disabled: activeIndex === 0 }"
        @click="switchCard(-1)"
      >&#60;</span>
      <span
        class="switch-btn"
        :class="{ disabled: activeIndex === cardList.length - 1 }"
        @click="switchCard(1)"
      >&#62;</span>
    </div>
  </div>
  </div>
</template>

<script>
export default {
  name: "UnifiedPlatform",
  data() {
    return {
      activeIndex: 0,
      cardList: [
        {
          name: "农贷通",
          icon: require("@/assets/serve/base.png"), // 替换为你的图标路径
          amount: 1527,
          count: 216542,
        },
        {
          name: "惠营贷",
          icon: require("@/assets/serve/base.png"), // 替换为你的图标路径
          amount: 1527,
          count: 216542,
        },
      ],
    };
  },
  methods: {
    switchCard(dir) {
      const next = this.activeIndex + dir;
      if (next >= 0 && next < this.cardList.length) {
        this.activeIndex = next;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.unified-platform {
  width: 940px;
  height: 27vh;
  background: linear-gradient(
    180deg,
    rgba(3, 29, 58, 0) 0%,
    rgba(0, 50, 107, 0.64) 100%
  );
  border: 1px solid;
  border-top: 0;
  border-image: linear-gradient(
      169deg,
      rgba(44, 110, 162, 0),
      rgba(47, 115, 169, 1)
    )
    2 2;
  border-radius: 10px;
  padding: 0;
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #FFFFFF;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0,175,255,0.68), 0px 2px 4px #000C1A;
    margin-bottom: 10px;
  }
  .platform-summary-box{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 20vh;
  }
  .platform-summary {
    display: flex;
    justify-content: center;
    gap: 60px;
    font-size: 28px;
    color: #fff;
    margin-bottom: 18px;
    .highlight, .highlight2 {
      font-size: 44px;
      font-family: OPPOSans;
      font-weight: bold;
      background: linear-gradient(180deg, #fff 0%, #ffce7c 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
    }
    .highlight2 {
      background: linear-gradient(180deg, #fff 0%, #7cebff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .platform-cards {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 10px;
    .platform-card {
      background: url("~@/assets/dp/one_bg.png") no-repeat center center;
      background-size: 100% 100%;
      border: 1px solid #1b6cff;
      border-radius: 8px;
      width: 41%;
      min-width: 320px;
      min-height: 229px;
      padding: 10px 0 16px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      transition: box-shadow 0.2s;
      &.active {

      }
      .card-header {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px 8px 0 0;
        .card-icon {
          width: 51px;
          height: 51px;
          margin-right: 8px;
        }
        .card-title {
          font-size: 35px;
          font-weight: bold;
          color: #fff;
        }
      }
      .card-info-row.horizontal {
        display: flex;
        justify-content: space-between;
        width: 90%;
        margin: 100px auto 0 auto;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          .info-label {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24px;
            color: #81B6FD;
            letter-spacing: 1px;
            text-align: center;
            font-style: normal;
          }
          .info-value {
            color: #fff;
            font-size: 28px;
            font-family: OPPOSans;
            font-weight: bold;
          }
        }
        .info-divider {
          width: 1px;
          height: 40px;
          background: linear-gradient(180deg, #7cebff 0%, #fff 100%);
          border-radius: 1px;
        }
      }
    }
  }
  .card-switch {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 4px;
    .switch-btn {
      font-size: 22px;
      color: #fff;
      background: #1790ff;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
      &.disabled {
        opacity: 0.4;
        pointer-events: none;
      }
    }
  }
}
</style>
