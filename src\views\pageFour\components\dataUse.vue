<template>
  <div class="dataUse">
    <div class="headerImg">
      <div class="lbClass">数据使用</div>
      <div class="rightClass">
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>
    <div class="bottomClass">
      <div class="data-use-grid">
        <div class="data-use-card" >
          <div class="card-title">数据调用次数</div>
          <div class="card-num">--<span class="card-unit">次</span></div>
        </div>
        <div class="data-use-card" >
          <div class="card-title">数据使用场景<br/>Top5</div>
          <ul class="card-list">
            <li>用户注册</li>
            <li>企业认证</li>
            <li>贷款申请</li>
            <li>智能金融顾问</li>
            <li>企业全生命周期服务</li>
          </ul>
        </div>
        <div class="data-use-card">
          <div class="card-title">高频调用数据<br/>Top5</div>
          <ul class="card-list">
            <li>社保数据</li>
            <li>公积金数据</li>
            <li>动产抵押抵押权人信息</li>
            <li>法院公告信息</li>
            <li>公司业务信息</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { productUpdate } from "@/api/article.js";
export default {
  name: "fkyhlb",
  components: {
    vueSeamlessScroll,
  },
  data() {
    return {
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      },
    };
  },
  mounted() {
/*     this.init(); */
  },
  methods: {
    init() {
      productUpdate({
        bizDate:localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 0,
        dimIndustryChain: "ALL",
        indexName: "累计放款金额",

      }).then((res) => {
      });
    },
    showConModal(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /picShow/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      if (data) {
        //点击事件处理逻辑
      }
    },
    handleScroll(e) {
      this.$refs.vueSeamlessScroll1.yPos =
        this.$refs.vueSeamlessScroll1.yPos - e.deltaY + 60;
      // 如果是正数 说明是往上滚
      if (this.$refs.vueSeamlessScroll1.yPos > 0) {
        this.$refs.vueSeamlessScroll1.yPos = 0;
        return;
      }
    },
  },
};
</script>
<style scope="scoped" lang="scss">
.dataUse {
  width: 940px;
  height: 26vh;
  display: flex;
  flex-direction: column;
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .rightClass {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: -80px;
      .djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }
      .imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }
  .bottomClass {
    width: 100%;
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);

    .data-use-grid {
      display: flex;
      width: 100%;
      justify-content: space-around;
      margin-top: 30px;
    }
    .data-use-card {
      height: 355px;
      width: 284.96px;
      background-repeat: no-repeat;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      background-image: url('../assets/useBg.png');
      background-size: 284.96px 355px;
      align-items: center;
      justify-content: space-between;
      padding: 13px  0;
      box-sizing: border-box;
    }
    .card-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #FFFFFF;
      letter-spacing: 1px;
      text-shadow: 0px 0px 19px #00D7FF, 0px 2px 3px rgba(0,0,0,0.5);
      text-align: center;
      font-style: normal;
    }
    .card-num {
      font-family: PingFangSC, PingFang SC;
      font-size: 44px;
      color: #23FFFC;
      line-height: 62px;
      letter-spacing: 2px;
      text-align: center;
      font-style: normal;
      padding-bottom: 100px;
      .card-unit {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #FFFFFF;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
      }
    }
    .card-list {
      list-style: none;
      padding: 0;
      margin: 0;
      text-align: center;
      li {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #23FFFC;
        line-height: 40px;
        letter-spacing: 1px;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
</style>
