<!-- 
  首页组件
  功能：展示金融数据大屏首页
  特点：
  1. 三栏布局（左中右）
  2. 支持地图切换（成都市/蓝色地图）
  3. 包含多个数据展示模块
  4. 响应式布局设计
-->
<template>
  <div class="bgblackClass3">
    <div class="dpClass">
      <!-- 头部路由导航 -->
      <HeaderRouter :maxDt="maxDt" :isMaxDtReady="isMaxDtReady" />
      <!-- 主体内容区域 -->
      <div class="pageContent">
        <!-- 左侧内容区域 -->
        <div class="pageLeft">
          <!-- 融资金融机构组件 -->
          <!-- <Rzjrjg
            style="margin-top: 9%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
            :selected-region="selectedRegion"
          /> -->
          <Rzjrjg
            style="margin-top: 9%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
          />
          <!-- 数据产品组件 -->
          <!-- <Sjcp
            style="margin-top: 5%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
            :selected-region="selectedRegion"
          /> -->
          <Sjcp
            style="margin-top: 5%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
          />
          <!-- 服务实体经济组件 -->
          <Fwstjj
            style="margin-top: 5%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
            :selected-region="selectedRegion"
          />
        </div>
        <!-- 中间内容区域 -->
        <div class="pageCenter-one">
          <!-- 头部数字统计 -->
          <div>
            <headerNum
              :selected-region="selectedRegion"
              :maxDt="maxDt"
              :isMaxDtReady="isMaxDtReady"
            />
          </div>
          <!-- 地图组件（根据flagFont切换不同地图） -->
          <div>
            <Map
              v-if="flagFont"
              ref="mapOne"
              :maxDt="maxDt"
              :isMaxDtReady="isMaxDtReady"
              @region-click="handleRegionClick"
            />
            <MapBlue
              v-else
              :maxDt="maxDt"
              :isMaxDtReady="isMaxDtReady"
              ref="MapBlue"
              @region-click="handleRegionClick"
            />
            <!-- 走访数据 -->
            <div class="zfdjClass" v-if="!flagFont">
              <VistData
                :selected-region="selectedRegion"
                :maxDt="maxDt"
                :isMaxDtReady="isMaxDtReady"
              />
            </div>
            <div class="qxfk-title" v-if="flagFont">区（市）县放款情况</div>
          </div>
        </div>
        <!-- 右侧内容区域 -->
        <div class="pageRight">
          <!-- 数据汇集组件 -->
          <!-- <Sjhj
            style="margin-top: 9%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
            :selected-region="selectedRegion"
          /> -->
          <Sjhj
            style="margin-top: 9%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
          />
          <!-- 数智应用组件 -->
          <!-- <Szyy
            style="margin-top: 5%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
            :selected-region="selectedRegion"
          /> -->
            <Szyy
            style="margin-top: 5%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
          />
          <!-- 放款用户列表组件 -->
          <Fkyhlb
            style="margin-top: 5%"
            :maxDt="maxDt"
            :isMaxDtReady="isMaxDtReady"
            :selected-region="selectedRegion"
          />
        </div>
      </div>
      <!-- 底部切换按钮 -->
      <div class="buttomClass1">
        <ChangeButtom
          @goPage="goPage"
          :flagFont="flagFont"
          :maxDt="maxDt"
          :isMaxDtReady="isMaxDtReady"
        />
      </div>
    </div>
  </div>
</template>

<script>
// 导入组件
import Fwstjj from "./components/fwstjj.vue";
import HeaderRouter from "../components/headerRouter";
import Rzjrjg from "./components/rzjrjg";
import Sjcp from "./components/sjcp";
import Sjhj from "./components/sjhj";
import Fkyhlb from "./components/fkyhlb";
import Szyy from "./components/szyy";
import ChangeButtom from "./components/changeButtom";
import HeaderNum from "./components/headerNum.vue";
import VistData from "./components/visitData";
import Map from "./components/map_base.vue";
import MapBlue from "./components/map_blue.vue";
import { queryMaxDt } from "@/api/article.js";

export default {
  name: "pageone",
  // 注册组件
  components: {
    HeaderRouter,
    Rzjrjg,
    Sjcp,
    Sjhj,
    Fkyhlb,
    Szyy,
    ChangeButtom,
    Fwstjj,
    HeaderNum,
    Map,
    MapBlue,
    VistData,
  },
  data() {
    return {
      showDialog: false, // 控制弹窗显示
      flagFont: true, // 控制地图切换
      timer: null, // 定时器
      maxDt: "", // 最大日期
      isMaxDtReady: false, // 数据是否准备就绪
      selectedRegion: "ALL", // 默认值
    };
  },
  created() {
    this.initData();
  },
  methods: {
    handleRegionClick(regionName) {
      this.selectedRegion = regionName;
      console.log("值", this.selectedRegion);
    },
    // 初始化数据
    async initData() {
      try {
        // 1. 首先获取 maxDt
        await this.getMaxDt();
        // 2. 设置标记，通知子组件可以开始调用其他接口
        this.isMaxDtReady = true;
      } catch (error) {
        console.error("初始化数据失败:", error);
      }
    },
    // 获取最大日期
    async getMaxDt() {
      try {
        const response = await queryMaxDt();
        this.maxDt = response.data.data;
        // 将maxDt存储到localStorage
        localStorage.setItem("maxDt", this.maxDt);
      } catch (error) {
        console.error("获取 maxDt 失败:", error);
      }
    },
    // 切换页面（地图）
    goPage(data) {
      this.flagFont = data;
      if (!data) {
        this.$refs.MapBlue?.backToChengdu();
      }
    },
    // 改变城市（地图）颜色
    changeCity(data) {
      this.flagFont = data;
    },
    // 处理返回成都市地图
    handleBackToChengdu() {
      if (this.$refs.MapBlue) {
        this.$refs.MapBlue.backToChengdu();
      } else {
        this.flagFont = true;
      }
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 主容器样式 */
.bgblackClass3 {
  width: 100vw;
  height: 100vh;
  // 设置背景图片
  background-image: url("~@/assets/dp/bgblack.jpg");
  background-repeat: no-repeat;
  background-size: 100vw 100vh;
  overflow: hidden;
  position: relative;

  /* 大屏容器样式 */
  .dpClass {
    width: 100vw;
    height: 100vh;
    // 设置背景图片
    background-image: url("~@/assets/dp/bg.png");
    background-repeat: no-repeat;
    background-size: 100vw 100vh;
    display: flex;
    flex-direction: column;

    /* 页面内容区域样式 */
    .pageContent {
      display: flex;
      margin: 0 100px;
      height: 100%;
      justify-content: space-between;

      /* 左侧内容区域样式 */
      .pageLeft {
        height: 97%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
      }

      /* 右侧内容区域样式 */
      .pageRight {
        height: 97%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
      }
    }

    /* 底部切换按钮容器样式 */
    .buttomClass1 {
      position: absolute;
      width: 100%;
      bottom: 0;
    }
  }

  /* 中间内容区域样式 */
  .pageCenter-one {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
  }
}
.zfdjClass {
  position: relative;
  bottom: 142px;
}
.qxfk-title {
  font-family: YouSheBiaoTiHei;
  font-size: 40px;
  color: #ffffff;
  cursor: pointer;
  line-height: 40px;
  text-align: center;
  font-style: normal;
  // margin-right: 134px;
  margin-top: 30px;
  position: relative;
  bottom: 148px;
}
</style>
