<!--
  园区分布柱状图弹窗组件
  功能：
  1. 展示园区分布数据的柱状图
  2. 支持自动轮播展示
  3. 支持加载状态显示
  4. 支持自适应字体大小
-->
<template>
  <!-- 弹窗遮罩层 -->
  <div v-if="visible" class="dialogBar-model" @click="handleDialogClose">
    <!-- 弹窗内容区 -->
    <div class="dialogBar-content" @click.stop>
      <!-- 弹窗头部 -->
      <div class="dialogBar-header-box">
        <div class="dialogBar-header">
          <div class="dialogBar-titleBox1">
            <span class="dialogBar-title">{{ title }}</span>
          </div>
        </div>
      </div>
      <!-- 弹窗主体 -->
      <div class="dialogBar-body">
        <!-- 图表容器 -->
        <div v-if="!loading" class="dialogBar-chart" ref="dialogChart"></div>
        <!-- 加载状态显示 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
          <div class="loading-text">加载中...</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";

export default {
  name: "DialogBarPark",
  // 组件属性定义
  props: {
    visible: { type: Boolean, default: false }, // 控制弹窗显示
    data: { type: Array, default: () => [] }, // 图表数据
    title: { type: String, default: "放款企业产业园区分布" }, // 弹窗标题
    colorStops: {
      type: Array,
      default: () => [
        // 柱状图渐变色配置
        { offset: 0, color: "#16DEE1 " },
        { offset: 1, color: "#034347" },
      ],
    },
    displayCount: { type: Number, default: 5 }, // 显示数据条数
    autoPlayInterval: { type: Number, default: 3000 }, // 自动播放间隔
    loading: { type: Boolean, default: false }, // 加载状态
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  data() {
    return {
      dialogChart: null, // echarts实例
      dialogAutoPlayTimer: null, // 自动播放定时器
      dialogCurrentIndex: 0, // 当前显示的数据索引
      backgroundData: Array(16).fill(10), // 背景数据
      
      // 所有数据
      allBanks: [], // 所有银行名称
      allValues: [], // 所有放款金额数据（单位：亿）
      allCounts: [], // 所有放款笔数数据

      // 当前显示的数据
      currentDisplayBanks: [], // 当前显示的银行名称
      currentDisplayValues: [], // 当前显示的放款金额
      currentDisplayCounts: [], // 当前显示的放款笔数
      
      scrollStep: 1, // 每次滚动的条数
    };
  },
  watch: {
    // 监听弹窗显示状态
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          if (!this.loading) {
            this.initDialogChart();
          }
        });
      } else {
        this.clearDialogChart();
      }
    },
    // 监听加载状态
    loading(val) {
      if (!val && this.visible) {
        this.$nextTick(() => {
          this.initDialogChart();
        });
      }
    },
    // 监听数据变化
    data: {
      handler() {
        if (this.visible && !this.loading && this.dialogChart) {
          this.$nextTick(() => {
            this.updateDialogChart();
          });
        }
      },
      deep: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.visible && !this.loading && this.dialogChart) {
          this.$nextTick(() => {
            this.updateDialogChart(newVal);
            this.updateEcharts(newVal);
          });
        }
      },
    },
  },
  computed: {
    values() {
      return this.data.map((item) => Number(item.value));
    },
    counts() {
      return this.data.map((item) => item.loanSum);
    },
    banks() {
      return this.data.map((item) => item.shortName);
    },
  },
  methods: {
    // 关闭弹窗
    handleDialogClose() {
      this.$emit("close");
      this.clearDialogChart();
    },
    // 清理图表资源
    clearDialogChart() {
      if (this.dialogAutoPlayTimer) {
        clearInterval(this.dialogAutoPlayTimer);
        this.dialogAutoPlayTimer = null;
      }
      if (this.dialogChart) {
        this.dialogChart.dispose();
        this.dialogChart = null;
      }
      this.dialogCurrentIndex = 0;
    },
    // 更新图表数据 - 新增方法
    updateDialogChart() {
      if (!this.dialogChart || !this.data.length) return;

      // 停止当前的自动播放
      if (this.dialogAutoPlayTimer) {
        clearTimeout(this.dialogAutoPlayTimer);
        this.dialogAutoPlayTimer = null;
      }

      // 重新开始轮播
      this.updateEcharts(
        this.dialogChart,
        0,
        this.displayCount,
        this.values,
        this.counts,
        this.banks
      );
    },
    // 初始化图表
    initDialogChart() {
      if (!this.$refs.dialogChart) return;
      console.log("initDialogChart", this.data);
      const container = this.$refs.dialogChart;
      // 确保容器尺寸有效
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        setTimeout(() => this.initDialogChart(), 100);
        return;
      }
      if (this.dialogChart) {
        this.dialogChart.dispose();
      }
      this.dialogChart = echarts.init(container);
      const offsetX = 10; // X轴偏移量
      const offsetY = 5; // Y轴偏移量

      // 绘制左侧面
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c0 = [shape.x, shape.y];
          const c1 = [shape.x - offsetX, shape.y - offsetY];
          const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
          const c3 = [xAxisPoint[0], xAxisPoint[1]];
          ctx
            .moveTo(c0[0], c0[1])
            .lineTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .closePath();
        },
      });

      // 绘制右侧面
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint;
          const c1 = [shape.x, shape.y];
          const c2 = [xAxisPoint[0], xAxisPoint[1]];
          const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
          const c4 = [shape.x + offsetX, shape.y - offsetY];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });

      // 绘制顶面
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0,
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y];
          const c2 = [shape.x + offsetX, shape.y - offsetY]; //右点
          const c3 = [shape.x, shape.y - offsetX];
          const c4 = [shape.x - offsetX, shape.y - offsetY];
          ctx
            .moveTo(c1[0], c1[1])
            .lineTo(c2[0], c2[1])
            .lineTo(c3[0], c3[1])
            .lineTo(c4[0], c4[1])
            .closePath();
        },
      });

      // 注册三个面图形
      echarts.graphic.registerShape("CubeLeft", CubeLeft);
      echarts.graphic.registerShape("CubeRight", CubeRight);
      echarts.graphic.registerShape("CubeTop", CubeTop);

      // 只有当数据存在时才初始化图表
      if (this.data.length > 0) {
        this.updateEcharts(
          this.dialogChart,
          0,
          this.displayCount,
          this.values,
          this.counts,
          this.banks
        );
      }

      window.addEventListener("resize", this.handleDialogResize);
    },
    // 处理窗口大小变化
    handleDialogResize() {
      this.dialogChart && this.dialogChart.resize();
    },
    // 更新图表数据
    updateEcharts(chart, startIndex, length, oldValues, oldCounts, oldBanks) {
      if (!chart || !oldValues || !oldValues.length) return;

      const primaryColor = "42,205,253"; // 主色调
      const VALUE = oldValues.slice(startIndex, length) || []; // 当前值数组
      const counts = oldCounts.slice(startIndex, length) || []; // 当前值数组
      const banks = oldBanks.slice(startIndex, length) || []; // 当前值数组

      // 图表配置项
      const option = {
        // 动画配置
        animation: true,
        animationDuration: 1000,
        animationEasing: "cubicOut",
        animationDelay: function (idx) {
          return idx * 100;
        },
        // 提示框配置
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: (params) => {
            if (!params || params.length === 0) return "";
            // 获取主系列数据（第一个系列）
            const mainParam = params[0];
            if (!mainParam) return "";

            const dataIndex = mainParam.dataIndex;
            // 直接从组件数据获取对应值
            const value = VALUE[dataIndex];
            const count = counts[dataIndex];

            return `<div style="text-align:center;">
              <span style="color:#ffce7c;font-size:16px;">${value}亿元</span><br/>
              <span style="color:#7cebff;font-size:16px;">${count}</span>
            </div>`;
          },
          backgroundColor: "rgba(10,29,56,0.95)",
          borderColor: "#0a4d8f",
          borderWidth: 1,
          extraCssText: "box-shadow:0 0 8px #0a4d8f;",
        },
        // 图表网格配置
        grid: {
          left: "0%",
          right: "0%",
          top: "17%",
          bottom: "2%",
          containLabel: true,
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: banks,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: "rgba(255,255,255,0.2)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(14),
            interval: 0,
            formatter: function (value) {
              return value.replace(/(.{4})/g, "\$1\n");
            },
          },
        },
        // Y轴配置
        yAxis: {
          show: false,
        },
        // 系列配置
        series: [
          // 主柱状图 - 正面
          {
            name: "main",
            tooltip: {
              show: false,
            },
            type: "bar",
            barWidth: this.$autoFontSize(20),
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(42,205,253, 0.6)", // 底部颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(42,205,253, 0.8)", // 中间颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(42,205,253, 1)", // 顶部颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: VALUE,
            animationDelay: function (idx) {
              return idx * 100;
            },
          },
          // 右侧面 - 深度效果
          {
            name: "right",
            tooltip: {
              show: false,
            },
            type: "bar",
            barWidth: this.$autoFontSize(10),
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  1,
                  0,
                  0,
                  [
                    {
                      offset: 0,
                      color: "rgba(42,205,253, 0.4)", // 底部颜色
                    },
                    {
                      offset: 0.6,
                      color: "rgba(42,205,253, 0.6)", // 中间颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(42,205,253, 0.8)", // 顶部颜色
                    },
                  ],
                  false
                ),
              },
            },
            barGap: 0,
            data: VALUE,
            animationDelay: function (idx) {
              return idx * 100 + 50;
            },
          },
          // 顶面 - 3D顶部效果
          {
            name: "top3d",
            tooltip: {
              show: false,
            },
            type: "pictorialBar",
            itemStyle: {
              borderWidth: 2,
              borderColor: "#000",
              color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
                {
                  offset: 0,
                  color: "rgba(42,205,253, 1)",
                },
                {
                  offset: 1,
                  color: "#419EFF",
                },
              ]),
            },
            symbol: "path://M 0,0 l 80,0 l -15,10 l -80,0 z",
            symbolSize: [this.$autoFontSize(33.8), this.$autoFontSize(9)],
            symbolOffset: [this.$autoFontSize(-6), this.$autoFontSize(-4)],
            symbolPosition: "end",
            data: VALUE,
            z: 3,
            animationDelay: function (idx) {
              return idx * 100 + 100;
            },
          },
          // 标签系列
          {
            type: "bar",
            label: {
              show: true,
              position: "top",
              padding: [0, 0, this.$autoFontSize(5), 0],
              formatter: (params) => {
                const value = oldValues[params.dataIndex]; // 使用传入的values数据
                const count = oldCounts[params.dataIndex]; // 使用传入的counts数据
                return `{gold|${value}亿元}\n{blue|${count}}`;
              },
              rich: {
                gold: {
                  color: "#ffce7c",
                  fontSize: this.$autoFontSize(14),
                  padding: [
                    0,
                    this.$autoFontSize(28),
                    this.$autoFontSize(5),
                    0,
                  ],
                  align: "center",
                },
                blue: {
                  color: "#7cebff",
                  padding: [0, this.$autoFontSize(28), 0, 0],
                  fontSize: this.$autoFontSize(14),
                  align: "center",
                },
              },
            },
            itemStyle: {
              color: "transparent",
            },
            tooltip: {},
            data: VALUE,
            animationDelay: function (idx) {
              return idx * 100 + 200;
            },
          },
        ],
      };

      chart.setOption(option);

      // 轮播逻辑
      if (oldValues && oldValues.length > 0) {
        const newOldValues = JSON.parse(JSON.stringify(oldValues));
        newOldValues.push(newOldValues.shift());

        const newOldCounts = JSON.parse(JSON.stringify(oldCounts));
        newOldCounts.push(newOldCounts.shift());

        const newOldBanks = JSON.parse(JSON.stringify(oldBanks));
        newOldBanks.push(newOldBanks.shift());

        if (this.dialogAutoPlayTimer) {
          clearTimeout(this.dialogAutoPlayTimer);
        }

        this.dialogAutoPlayTimer = setTimeout(() => {
          this.updateEcharts(
            chart,
            startIndex,
            length,
            newOldValues,
            newOldCounts,
            newOldBanks
          );
        }, this.autoPlayInterval);
      }
    },
  },
  beforeDestroy() {
    this.clearDialogChart();
    window.removeEventListener("resize", this.handleDialogResize);
  },
};
</script>

<style scoped lang="scss">
/* 弹窗遮罩层样式 */
.dialogBar-model {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 弹窗内容区样式 */
.dialogBar-content {
  background: url("~@/assets/dp/dialogMax.png");
  background-size: 100% 100%;
  border-radius: 12px;
  width: 60%;
  height: 60%;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 弹窗头部样式 */
.dialogBar-header-box {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.dialogBar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 标题样式 */
.dialogBar-titleBox1 {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 150px;
  .dialogBar-title {
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
    margin-left: -40px;
    width: 100%;
  }
}

/* 弹窗主体样式 */
.dialogBar-body {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 图表容器样式 */
.dialogBar-chart {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(0, 24, 48, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 加载状态容器样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 24, 48, 0.8);
  z-index: 1000;
}

/* 加载动画样式 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

/* 加载动画点样式 */
.dot {
  width: 20px;
  height: 20px;
  background: #16dee1;
  border-radius: 50%;
  animation: bounce 0.5s infinite alternate;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.4s;
  }
  &:nth-child(4) {
    animation-delay: 0.6s;
  }
  &:nth-child(5) {
    animation-delay: 0.8s;
  }
}

/* 加载文字样式 */
.loading-text {
  color: #16dee1;
  font-size: 42px;
  font-family: PingFangSC, PingFang SC;
}

/* 加载动画关键帧 */
@keyframes bounce {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-10px);
  }
}
</style>
