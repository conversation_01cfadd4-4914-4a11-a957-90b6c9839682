<!--
  放款用户详情弹窗组件
  功能：
  1. 展示放款用户的详细信息表格
  2. 支持分页查询
  3. 显示企业名称、金融机构、产品、放款金额等信息
  4. 提供自定义样式的表格和分页器
-->
<template>
  <!-- 弹窗组件 -->
  <el-dialog
    title="放款用户"
    :visible.sync="dialogVisible"
    :append-to-body="true"
    width="80%"
    :close-on-click-modal="true"
    class="custom-dialog"
    :show-close="false"
  >
    <!-- 表格组件 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      header-cell-class-name="custom-header"
      cell-class-name="custom-cell"
      height="51vh"
    >
      <!-- 企业名称列 -->
      <el-table-column
        prop="enterprisePrivacyName"
        label="企业名称"
        :width="$autoFontSize(250)"
      />
      <!-- 金融机构列 -->
      <el-table-column
        prop="financialInstitutionShortName"
        label="金融机构"
        :width="$autoFontSize(200)"
      />
      <!-- 产品列 -->
      <el-table-column
        prop="productName"
        label="产品"
        :width="$autoFontSize(350)"
      />
      <!-- 放款金额列 -->
      <el-table-column
        prop="loanAmount"
        label="放款金额（万元）"
        :width="$autoFontSize(200)"
      />
      <!-- 放款日期列 -->
      <el-table-column
        prop="loanTime"
        label="放款日期"
        :width="$autoFontSize(150)"
      />
      <!-- 担保类型列 -->
      <el-table-column
        prop="guaranteeType"
        label="担保类型"
        :width="$autoFontSize(150)"
      />
    </el-table>
    <!-- 分页器 -->
    <div style="text-align: center; margin-top: 20px">
      <el-pagination
        style="text-align: right"
        layout="prev, pager, next"
        :total="total"
        :page-size="10"
        :current-page.sync="currentPage"
      />
    </div>
  </el-dialog>
</template>

<script>
import { Dialog, Table, TableColumn, Pagination } from "element-ui";
import { productUpdatePage } from "@/api/article.js";

export default {
  components: {
    ElDialog: Dialog,
    ElTable: Table,
    ElTableColumn: TableColumn,
    ElPagination: Pagination,
  },
  props: {
    // 最大日期
    maxDt: {
      type: String,
      required: true,
    },
    // 最大日期是否准备就绪
    isMaxDtReady: {
      type: Boolean,
      required: true,
    },
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  data() {
    return {
      total: "", // 总数据条数
      dialogVisible: false, // 弹窗显示状态
      currentPage: 1, // 当前页码
      tableData: [], // 表格数据
    };
  },
  watch: {
    // 监听当前页码变化
    currentPage(newVal) {
      this.init();
    },
    // 监听最大日期准备状态
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用其他接口
          this.init();
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.init(newVal);
      },
    },
  },
  methods: {
    // 初始化数据
    init() {
      let obj = {
        indexName: "放款用户列表",
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimPark: "ALL",
        dimIndustrychain: "ALL",
        dimNum: 1,
        page: this.currentPage,
        dimCounty: this.selectedRegion || "ALL",
        size: 10,
      };
      productUpdatePage(obj).then((res) => {
        this.tableData = res.data?.data?.records || [];
        // 处理日期格式，只保留日期部分
        this.tableData = this.tableData.map((item) => {
          const newItem = { ...item };
          if (newItem.loanTime && newItem.loanTime.includes(" ")) {
            newItem.loanTime = newItem.loanTime.split(" ")[0];
          }
          return newItem;
        });
        this.total = res?.data?.data?.total || '0';
      });
    },
    // 打开弹窗
    openDialog() {
      this.dialogVisible = true;
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 弹框整体样式 */
.custom-dialog .el-dialog {
  padding: 20px;
  background: url("~@/assets/dp/dialogMax.png");
  background-size: 100% 100%;
  border-radius: 0;
  width: 70% !important;
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  right: 0;
  margin: auto;
}

/* 表格单元格内容居中 */
.el-table th.el-table__cell > .cell {
  text-align: center !important;
  display: flex !important;
}

/* 表格滚动条背景色 */
.el-table th.el-table__cell .gutter {
  background-color: rgba(10, 29, 56, 0.5) !important;
}

/* 弹窗头部样式 */
.custom-dialog .el-dialog__header {
  width: 940px !important;
  height: 89px !important;
}

/* 弹窗标题样式 */
.custom-dialog .el-dialog__title {
  display: block;
  color: #fff !important;
  width: 940px !important;
  height: 89px !important;
  letter-spacing: 2px;
  background-image: url("~@/assets/dp/headerbg.png");
  padding-left: 100px;
  font-family: YouSheBiaoTiHei;
  font-size: 42px !important;
  background-size: 940px 89px;
  background-repeat: no-repeat;
  line-height: 89px;
}

/* 表格滚动条样式 */
.el-table .el-table__cell.gutter {
  width: 0 !important;
  background-color: #0a1d38 !important;
}

/* 关闭按钮样式 */
.custom-dialog .el-dialog__headerbtn {
  position: absolute;
  top: 15px;
  right: 20px;
  padding: 0;
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  font-size: 16px;
}

/* 关闭按钮图标样式 */
.custom-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #fff !important;
  font-size: 40px;
}

/* 关闭按钮悬停效果 */
.custom-dialog .el-dialog__headerbtn:hover .el-dialog__close {
  color: #409eff !important;
}

/* 表格表头样式 */
.custom-header {
  padding-left: 60px !important;
  background: #164a7e !important;
  color: #41ccff !important;
  font-weight: bold !important;
  border-color: #3a6894 !important;
  height: 60px !important;
  line-height: 60px !important;
  text-align: center !important;
}

/* 表格单元格样式 */
.custom-cell {
  background: #113a5c !important;
  color: #fff !important;
  border-color: #3a6894 !important;
}

/* 表格背景透明 */
.el-table,
.el-table__header-wrapper,
.el-table__body-wrapper {
  background-color: transparent !important;
}

/* 表格滚动区域样式 */
.el-table__body-wrapper {
  height: 50vh !important;
  overflow-x: auto !important;
}

/* 自定义滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar {
  width: 25px;
  background-color: rgba(10, 29, 56, 0.5);
}

/* 移除表格底部边框 */
.el-table::before {
  background-color: none !important;
}

/* 滚动条滑块样式 */
.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #168aff;
  border-radius: 4px;
}

/* 滚动条轨道样式 */
.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: rgba(22, 74, 126, 0.3);
  border-radius: 4px;
}

/* 表格文字居中 */
.el-table th,
.el-table td {
  text-align: center;
  border-color: #3a6894 !important;
}

/* 表格边框样式 */
.el-table--border,
.el-table--group {
  border-color: #3a6894 !important;
}

/* 表格边框颜色 */
.el-table--border::after,
.el-table--group::after {
  background-color: #3a6894 !important;
}

/* 分页器样式 */
.custom-pagination {
  text-align: center;
  margin-top: 20px;
}

/* 分页器背景透明 */
.custom-pagination .el-pagination {
  background-color: transparent;
}

/* 分页器文字颜色 */
.custom-pagination .el-pagination__total,
.custom-pagination .el-pagination__jump,
.custom-pagination .el-pagination__sizes {
  color: #fff !important;
}

/* 分页器按钮样式 */
.custom-pagination .btn-prev,
.custom-pagination .btn-next {
  background-color: #0a1d38 !important;
  color: #fff !important;
  border: none !important;
}

/* 分页器页码样式 */
.custom-pagination .el-pager li {
  display: flex;
  background-color: #0a1d38;
  border: none !important;
  margin: 0 2px;
}

/* 分页器页码悬停效果 */
.custom-pagination .el-pager li:hover:not(.active) {
  color: #409eff;
}

/* 移除表格边框 */
.el-table::before {
  height: 0px !important;
}

/* 表格单元格内容样式 */
.el-table .cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34px;
  height: 3.3vh;
}

/* 分页器页码按钮样式 */
.el-pager li {
  width: 47px;
  height: 47px;
  line-height: 47px;
  box-shadow: inset 0px 0px 3px 0px #168aff;
  border: 1px solid rgba(110, 160, 227, 0.8);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 34px;
}

/* 上一页按钮样式 */
.btn-prev {
  width: 47px;
  height: 47px !important;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  ) !important;
  box-shadow: inset 0px 0px 3px 0px #168aff;
  border: 1px solid rgba(110, 160, 227, 0.8);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #67a7ff !important;
}

/* 上一页图标样式 */
.el-icon-arrow-left {
  font-size: 34px !important;
}

/* 下一页图标样式 */
.el-icon-arrow-right {
  font-size: 34px !important;
}

/* 下一页按钮样式 */
.btn-next {
  width: 47px;
  height: 47px !important;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  ) !important;
  box-shadow: inset 0px 0px 3px 0px #168aff;
  border: 1px solid rgba(110, 160, 227, 0.8);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 34px !important;
  color: #67a7ff !important;
}

/* 当前页码样式 */
.custom-pagination .el-pager li.active {
  background-color: #409eff !important;
  color: #fff !important;
}

/* 当前页码激活状态样式 */
.custom-pagination .el-pager li.active {
  background: #164a7e !important;
  color: #fff !important;
  border-radius: 2px;
}

/* 数字分页器激活状态样式 */
.number .active {
  background: #164a7e !important;
  color: #fff !important;
}
</style>
