<!-- 
  数据汇集组件
  功能：展示数据汇集相关的统计信息和详细数据列表
  特点：
  1. 展示累计汇集数据的大类、项数和数据量
  2. 支持数据列表的无缝滚动展示
  3. 提供查看更多功能
  4. 美观的数据展示界面
-->
<template>
  <div class="sjhjClass">
    <!-- 头部标题区域 -->
    <div class="headerImg">
      <div class="lbClass">数据融合</div>
      <!-- 右侧查看更多按钮 -->
      <div class="rightClass">
        <div class="djgdClass" @click="openDialogMore">点击查看更多</div>
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>
    <!-- 主体内容区域 -->
    <div class="bottomClass">
      <!-- 累计数据统计展示 -->
      <div class="ljsjClass">
        <div class="oneClass">累计汇集数据：</div>
        <div class="fourClass">{{ parseInt(classNum) }}</div>
        <div class="fiveClass">大类</div>
        <div class="twoClass">{{ parseInt(itemNum) }}</div>
        <div class="threeClass">项</div>
        <div class="twoClass">{{ dataNum }}</div>
        <div class="threeClass">亿条</div>
        <!-- <div class="twoClass">{{ 2345 }}</div>
        <div class="threeClass">字段</div> -->
      </div>
      <!-- 数据列表展示区域 -->
      <div class="box-con-list">
        <!-- 表头 -->
        <div class="tableHeader">
          <div class="table-name">
            <div>数据类型</div>
            <div>数据项名称</div>
            <div>数据量(条)</div>
          </div>
        </div>
        <!-- 数据列表，使用无缝滚动组件 -->
        <div v-if="tableList.length > 0" class="warp">
          <vue-seamless-scroll
            ref="vueSeamlessScroll1"
            :data="tableList"
            style="height: 100%; overflow: hidden"
            :class-option="classOption"
            @mousewheel.native="handleScroll"
          >
            <div v-for="(item, index) in tableList" :key="index">
              <div class="table-items">
                <div style="width: 35%">{{ item.category_cn }}</div>
                <div style="width: 42%">{{ item.category_item_cn }}</div>
                <div style="width: 13%">{{ item.data_num }}</div>
              </div>
            </div>
          </vue-seamless-scroll>
        </div>
        <!--     <div class="empty" v-else>暂无数据</div> -->
      </div>
    </div>
    <dialog-more
      :maxDt="maxDt"
      :isMaxDtReady="true"
      @close="showDialogMore = false"
      ref="dialogMoreRef"
    />
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { productUpdateList } from "@/api/article.js";
import dialogMore from "@/views/pageOne/components/dialogMore.vue";
export default {
  name: "sjhjFour",
  components: {
    vueSeamlessScroll,
    dialogMore,
  },
  data() {
    return {
      maxDt: localStorage.getItem("maxDt"),
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      },
      dataNum: "",
      classNum: "",
      tableList: [],
      itemNum: "",
      carList: [],
      showDialogMore: false,
    };
  },
  mounted() {
    this.getProduct();
  },
  methods: {
    async getProduct() {
      let obj = [
        {
          indexName: "累计汇集数据量",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "累计汇聚数据类别",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "数据明细",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "累计汇集数据项",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
      ];
      const res = await productUpdateList(obj);
      if (res.data.code == 200) {
        this.dataNum = res.data.data[0].indexValue;
        this.classNum = res.data.data[1].indexValue;
        this.itemNum = res.data.data[3].indexValue;
        this.tableList = JSON.parse(res.data.data[2].bizContent);
      }
    },
    showConModal(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /picShow/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      if (data) {
        //点击事件处理逻辑
      }
    },
    handleScroll(e) {
      this.$refs.vueSeamlessScroll1.yPos =
        this.$refs.vueSeamlessScroll1.yPos - e.deltaY + 60;
      // 如果是正数 说明是往上滚
      if (this.$refs.vueSeamlessScroll1.yPos > 0) {
        this.$refs.vueSeamlessScroll1.yPos = 0;
        return;
      }
    },
    openDialogMore() {
      this.$refs.dialogMoreRef.openDialog();
    },
  },
};
</script>
<style scope="scoped" lang="scss">
.sjhjClass {
  width: 940px;
  height: 25vh;
  display: flex;
  flex-direction: column;
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .lbClass {
      height: 80px;
      line-height: 80px;
    }
    .rightClass {
      display: flex;
      align-items: center;
      justify-content: center;
      .djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }
      .imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }
  .bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    .ljsjClass {
      display: flex;
      align-items: flex-end;
      height: 70px;
      padding-left: 40px;
      padding-top: 6px;
      margin-bottom: 26px;
      .oneClass {
        font-family: PingFangSC;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        letter-spacing: 1px;
        white-space: nowrap;
      }
      .twoClass {
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 46px;
        letter-spacing: 2px;
        // text-shadow: 0px 0px 4px rgba(255,185,49,0.47), 0px 0px 14px rgba(255,189,65,0.54), 0px 2px 4px rgba(0,0,0,0.5);
        background: linear-gradient(90deg, #ffffff 0%, #ffce7c 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        white-space: nowrap;
      }
      .threeClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        letter-spacing: 1px;
        margin-left: 6px;
        margin-right: 20px;
        white-space: nowrap;
      }
      .fourClass {
        letter-spacing: 2px;
        text-align: center;
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 44px;
        letter-spacing: 2px;
        // text-shadow: 0px 0px 4px rgba(255,185,49,0.47), 0px 0px 14px rgba(255,189,65,0.54), 0px 2px 4px rgba(0,0,0,0.5);
        background: linear-gradient(90deg, #ffffff 0%, #7cebff 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-left: 20px;
        white-space: nowrap;
        // margin-left: -6px;
      }
      .fiveClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        letter-spacing: 1px;
        margin-left: 6px;
        margin-right: 12px;
        white-space: nowrap;
      }
    }
    .box-con-list {
      width: 100%;

      .tableHeader {
        width: 100%;
        box-sizing: border-box;
        padding: 0 35px;
        .table-name {
          padding: 0 64px;
          box-sizing: border-box;
          display: flex;
          height: 80px;
          background: rgba(255, 255, 255, 0.09);
          font-weight: 500;
          font-size: 26px;
          color: #41ccff;
          letter-spacing: 1px;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
    .warp {
      height: 14vh;
      padding: 0 35px;
      .table-items {
        display: flex;
        padding: 0 64px;
        box-sizing: border-box;
        height: 80px;
        align-items: center;
        justify-content: space-between;
        font-weight: 400;
        font-size: 26px;
        color: #ffffff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.22);
      }
    }
  }
}
</style>
