/dist/
/node_modules/
/public/
*.d.ts
*.js.map
*.css
*.scss
*.less
*.styl
*.svg
*.png
*.jpg
*.jpeg
*.gif
*.ico
*.woff
*.woff2
*.ttf
*.eot
*.mp4
*.webm
*.ogg
*.mp3
*.wav
*.flac
*.aac
*.m4a
*.webp
*.pdf
*.zip
*.rar
*.7z
*.tar
*.gz
*.bz2
*.xz
*.swp
*.swo
*.swn
*.bak
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock
*.tsbuildinfo
*.vue.js
*.vue.js.map
*.vue.css
*.vue.css.map
*.vue.scss
*.vue.scss.map
*.vue.less
*.vue.less.map
*.vue.styl
*.vue.styl.map
*.vue.svg
*.vue.png
*.vue.jpg
*.vue.jpeg
*.vue.gif
*.vue.ico
*.vue.woff
*.vue.woff2
*.vue.ttf
*.vue.eot
*.vue.mp4
*.vue.webm
*.vue.ogg
*.vue.mp3
*.vue.wav
*.vue.flac
*.vue.aac
*.vue.m4a
*.vue.webp
*.vue.pdf
*.vue.zip
*.vue.rar
*.vue.7z
*.vue.tar
*.vue.gz
*.vue.bz2
*.vue.xz
*.vue.swp
*.vue.swo
*.vue.swn
*.vue.bak
*.vue.tmp
*.vue.temp
*.vue.log
*.vue.pid
*.vue.seed
*.vue.pid.lock
*.vue.tsbuildinfo 