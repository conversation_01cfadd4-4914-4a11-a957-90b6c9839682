<!--
  金融机构放款数据极值组件
  功能：
  1. 展示金融机构放款数据的极值信息
  2. 支持数据无缝滚动展示
  3. 提供详细数据的弹窗展示
  4. 展示单笔放款的最高/最低金额、最长期限、最短期限、最低利率等信息
-->
<template>
  <div class="LendingExtreme-sjhjClass">
    <!-- 头部标题区域 -->
    <div class="LendingExtreme-headerImg">
      <div class="LendingExtreme-lbClass">机构放款数据之最</div>

      <!-- 查看更多按钮 -->
      <div class="LendingExtreme-rightClass">
        <div class="djgdClass" @click="openDialogMore">点击查看更多</div>
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="LendingExtreme-bottomClass">
      <!-- 无缝滚动容器 -->
      <vue-seamless-scroll
        ref="vueSeamlessScroll1"
        :data="scollList"
        style="height: 100%; overflow: hidden"
        :class-option="classOption"
        @mousewheel.native="handleScroll"
      >
        <!-- 极值数据网格 -->
        <div class="LendingExtreme-extreme-grid">
          <!-- 极值数据卡片 -->
          <div
            class="LendingExtreme-extreme-card"
            v-for="(item, index) in extremeList"
            :key="item.bank + item.type + index"
          >
            <!-- 银行logo区域 -->
            <div class="LendingExtreme-extreme-logo">
              <div class="LendingExtreme-extreme-logo-bg"></div>
              <img
                :src="item.logo"
                :alt="item.bank"
                class="LendingExtreme-extreme-logo-img"
              />
              <!-- 极值类型 -->
              <div
                class="LendingExtreme-extreme-type"
                :style="{ color: item.valueColor }"
              >
                {{ item.type }}
              </div>
              <!-- 极值子类型 -->
              <div
                class="LendingExtreme-extreme-type2"
                :style="{ color: item.valueColor }"
              >
                {{ item.type2 }}
              </div>
            </div>
            <!-- 数值展示区域 -->
            <div
              class="LendingExtreme-extreme-content"
              style="margin-top: 30px"
            >
              <div class="LendingExtreme-extreme-value">
                <div class="LendingExtreme-extreme-bank">{{ item.bank }}</div>
                <span
                  class="LendingExtreme-value-number"
                  :style="{ color: item.valueColor }"
                  >{{ item.value }}</span
                >
                <span
                  class="LendingExtreme-value-unit"
                  :style="{ color: item.valueColor }"
                  >{{ item.unit }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </vue-seamless-scroll>
    </div>

    <!-- 详细数据弹窗 -->
    <el-dialog
      :visible.sync="detailDialogVisible"
      :title="detailDialogTitle"
      class="lending-dialog"
      :close-on-click-modal="true"
      :show-close="false"
      center
      width="40%"
      custom-class="custom-dialog"
    >
      <div class="dialog-content">
        <div class="dialog-grid">
          <!-- 弹窗中的极值数据卡片 -->
          <div
            class="dialog-card"
            v-for="(item, index) in extremeList"
            :key="index"
          >
            <!-- 银行logo区域 -->
            <div class="dialog-logo">
              <div class="dialog-logo-bg"></div>
              <img :src="item.logo" class="dialog-logo-img" />
              <!-- 极值类型 -->
              <div class="dialog-type" :style="{ color: item.valueColor }">
                {{ item.type }}
              </div>
              <!-- 极值子类型 -->
              <div class="dialog-type2" :style="{ color: item.valueColor }">
                {{ item.type2 }}
              </div>
            </div>
            <!-- 数值展示区域 -->
            <div class="dialog-content" style="margintop: 30px">
              <div class="dialog-bank">{{ item.bank }}</div>
              <div class="dialog-value">
                <span
                  class="dialog-value-number"
                  :style="{ color: item.valueColor }"
                  >{{ item.value }}</span
                >
                <span
                  class="dialog-value-unit"
                  :style="{ color: item.valueColor }"
                  >{{ item.unit }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { productUpdateList } from "@/api/article.js";
import dialogMore from "@/views/pageOne/components/dialogMore.vue";

export default {
  name: "LendingExtreme",
  components: {
    vueSeamlessScroll,
    dialogMore,
  },
  props: {
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  watch: {
    // isMaxDtReady: {
    //   handler(newVal) {
    //     if (newVal) {
    //       // 只有当 maxDt 准备好后才调用其他接口
    //       this.getrzjrjg();
    //     }
    //   },
    //   immediate: true,
    // },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.featchData(newVal);
      },
    },
  },
  data() {
    return {
      // detailDialogTitle: "金融机构放款数据极值",
      detailDialogTitle: "机构放款数据之最",
      detailDialogVisible: false,
      detailDialogList: [],
      // 无缝滚动配置
      classOption: {
        step: 0.1, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 40, // 单步运动停止的高度
        singleWidth: 0, // 单步运动停止的宽度
        waitTime: 1000, // 单步运动停止的时间
      },
      // 极值数据列表
      extremeList: [
        {
          bank: "建设银行",
          logo: require("../assets/name.png"),
          type: "单笔放款",
          type2: "最高金额",
          value: "",
          valueColor: "#7cebff",
          valueText: "0px 2px 0px #00DAFF, 0px 2px 4px rgba(0,0,0,0.5)",
          unit: "万",
        },
        {
          bank: "成都银行",
          logo: require("../assets/name.png"),
          type: "单笔放款",
          type2: "最低金额",
          value: "",
          valueColor: "#7cebff",
          valueText: "0px 2px 0px #00DAFF, 0px 2px 4px rgba(0,0,0,0.5)",
          unit: "万",
        },
        {
          bank: "中国银行",
          logo: require("../assets/name.png"),
          type: "单笔放款",
          type2: "最长期限",
          value: "",
          valueColor: "#ffce7c",
          valueText: "0px 2px 0px #FFD641, 0px 2px 4px rgba(0,0,0,0.5)",
          unit: "月",
        },
        {
          bank: "成都农商银行",
          logo: require("../assets/name.png"),
          type: "单笔放款",
          type2: "最短期限",
          value: "",
          valueColor: "#ffce7c",
          valueText: "0px 2px 0px #FFD641, 0px 2px 4px rgba(0,0,0,0.5)",
          unit: "月",
        },
        {
          bank: "农业银行",
          logo: require("../assets/name.png"),
          type: "单笔放款",
          type2: "最低利率",
          value: "",
          valueColor: "#41FFC3",
          valueText: "0px 2px 0px #41FFC3, 0px 2px 4px rgba(0,0,0,0.5)",
          unit: "%",
        },
      ],
      scollList: [], // 滚动数据列表
    };
  },
  mounted() {
    this.featchData();
  },
  methods: {
    featchData() {
      // 获取极值数据
      productUpdateList([
        {
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          // dimNum: 1,
          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
          dimIndustryChain: "ALL",
          indexName: "单笔放款最高金额",
        },
        {
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          // dimNum: 1,
          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
          dimIndustryChain: "ALL",
          indexName: "单笔放款最低金额",
        },
        {
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          // dimNum: 1,
          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
          dimIndustryChain: "ALL",
          indexName: "单笔贷款最长期限",
        },
        {
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          // dimNum: 1,
          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
          dimIndustryChain: "ALL",
          indexName: "单笔贷款最短期限",
        },
        {
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          // dimNum: 1,
          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
          dimIndustryChain: "ALL",
          indexName: "单笔放款最低利率",
        },
      ])
        .then((res) => {
          const list = res.data?.data || [];

          this.extremeList.forEach((item, index) => {
            // 1. 防止越界访问
            const apiData = list[index] || {};

            // 2. 安全解析 JSON
            let bizContent = [];
            try {
              bizContent = JSON.parse(apiData.bizContent || "[]");
            } catch (e) {
              console.error("解析bizContent失败:", e);
            }

            // 3. 获取第一个有效数据项
            const contentItem = bizContent[0] || {};

            // 4. 安全获取数值
            const rawValue =
              contentItem.loanAmt ||
              contentItem.limitDate ||
              contentItem.endRate;
            item.value = parseFloat(rawValue) || 0; // 无效数值默认0

            // 5. 安全获取银行名称
            item.bank = contentItem.shortName || "未知银行";
          });

          this.scollList = [...this.extremeList];
        })
        .catch((error) => {
          console.error("接口请求失败:", error);
        });
    },
    // 显示内容模态框
    showConModal(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /picShow/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      if (data) {
        // 点击事件处理逻辑
      }
    },
    // 处理滚动事件
    handleScroll(e) {
      this.$refs.vueSeamlessScroll1.yPos =
        this.$refs.vueSeamlessScroll1.yPos - e.deltaY + 60;
      // 如果是正数 说明是往上滚
      if (this.$refs.vueSeamlessScroll1.yPos > 0) {
        this.$refs.vueSeamlessScroll1.yPos = 0;
        return;
      }
    },
    // 打开更多详情弹窗
    openDialogMore() {
      this.detailDialogVisible = true;
    },
    // 获取数值
    getNumber(value) {
      if (value.includes("年")) {
        return value.replace("年", "");
      }
      if (value.includes("万")) {
        return value.replace("万", "");
      }
      if (value.includes("%")) {
        return value.replace("%", "");
      }
      return value;
    },
    // 获取单位
    getUnit(value) {
      if (value.includes("年")) {
        return "年";
      }
      if (value.includes("万")) {
        return "万";
      }
      if (value.includes("%")) {
        return "%";
      }
      return "";
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 组件容器 */
.LendingExtreme-sjhjClass {
  width: 940px;
  height: 25vh;
  display: flex;
  flex-direction: column;

  /* 头部标题区域 */
  .LendingExtreme-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    /* 标题文本 */
    .LendingExtreme-lbClass {
      height: 80px;
      line-height: 80px;
    }

    /* 右侧按钮区域 */
    .LendingExtreme-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      /* 查看更多按钮 */
      .djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      /* 箭头图标 */
      .imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 主体内容区域 */
  .LendingExtreme-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    overflow: hidden;
  }
}

/* 极值数据网格 */
.LendingExtreme-extreme-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 74px;
  justify-content: space-between;
  height: 30vh;

  /* 极值数据卡片 */
  .LendingExtreme-extreme-card {
    width: 350px;
    margin-bottom: 0;
    border-radius: 16px;
    display: flex;
    align-items: center;
    position: relative;

    /* 银行logo区域 */
    .LendingExtreme-extreme-logo {
      position: relative;
      width: 94px;
      height: 148px;
      margin-top: 12px;
      margin-bottom: 6px;

      .LendingExtreme-extreme-logo-img {
        position: absolute;
        width: 176.94px;
        height: 148px;
      }
    }

    /* 银行名称 */
    .LendingExtreme-extreme-bank {
      margin-left: 00px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      letter-spacing: 1px;
      text-align: left !important;
      font-style: normal;
      width: 200px;
      text-align: center;
    }

    /* 极值类型 */
    .LendingExtreme-extreme-type {
      width: 200px;
      position: absolute;
      top: -20px;
      left: 24px;
      font-family: AlimamaShuHeiTi;
      font-weight: bold;
      font-size: 31px;
      line-height: 40px;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }

    /* 极值子类型 */
    .LendingExtreme-extreme-type2 {
      width: 200px;
      position: absolute;
      top: 30px;
      left: 24px;
      font-family: AlimamaShuHeiTi;
      font-weight: bold;
      font-size: 31px;
      line-height: 40px;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }

    /* 数值展示区域 */
    .LendingExtreme-extreme-value {
      font-family: PingFangSC, PingFang SC;
      font-size: 28px;
      margin-top: -80px;
      margin-left: 200px;
      text-shadow: 0 0 8px #0a7bbd33;

      /* 数值 */
      .LendingExtreme-value-number {
        font-size: 44px;
      }

      /* 单位 */
      .LendingExtreme-value-unit {
        font-weight: normal;
        font-size: 28px;
        margin-left: 4px;
      }
    }
  }
}
</style>

<style lang="scss">
/* 弹框样式 */
.lending-dialog {
  /* 弹框主体 */
  .el-dialog {
    background-size: 100% 100%;
    border: 2px solid;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    margin: 0 auto !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  /* 弹框标题 */
  .el-dialog__title {
    letter-spacing: 6px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    background: url("~@/assets/dp/headerbg.png");
    background-size: 940px 89px;
    background-repeat: no-repeat;
    width: 940px;
    height: 89px;
    text-align: left;
    font-family: YouSheBiaoTiHei;
    color: #fff !important;
    font-size: 42px;
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 120px;
    margin-top: 20px;
  }

  /* 弹框容器 */
  .el-dialog {
    width: 940px;
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    margin: 0 !important;
    transform: translate(-50%, -50%) !important;
    background: url("~@/assets/dp/dialog.png");
    background-size: 100% 100%;
  }
}

/* 弹框内容区域 */
.dialog-content {
  .dialog-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 30px 60px;
    height: 40vh;
    overflow-y: auto;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 8px;
      background: rgba(0, 0, 0, 0.1);
    }

    &::-webkit-scrollbar-thumb {
      background: #195b8a;
      border-radius: 4px;
    }

    /* 弹框中的卡片 */
    .dialog-card {
      width: 100%;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      border-radius: 12px;
      padding: 15px;

      /* 极值类型 */
      .dialog-type {
        width: 200px;
        position: absolute;
        top: -15px;
        left: 30px;
        font-family: AlimamaShuHeiTi;
        font-weight: bold;
        font-size: 31px;
        color: #00daff;
        line-height: 36px;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
      }

      /* 极值子类型 */
      .dialog-type2 {
        width: 200px;
        position: absolute;
        top: 25px;
        left: 30px;
        font-family: AlimamaShuHeiTi;
        font-weight: bold;
        font-size: 31px;
        color: #ffffff;
        line-height: 36px;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
      }

      /* 银行logo区域 */
      .dialog-logo {
        position: relative;
        width: 180px;
        height: 140px;
        margin-top: 12px;
        margin-bottom: 6px;
        display: flex;
        justify-content: center;

        .dialog-logo-img {
          width: 180px;
          height: 140px;
        }
      }

      /* 银行名称 */
      .dialog-bank {
        width: 300px;
        text-align: left !important;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        margin-left: 30px;
        margin-top: -90px;
        color: #ffffff;
        letter-spacing: 1px;
        font-style: normal;
      }

      /* 数值展示区域 */
      .dialog-content {
        .dialog-value {
          font-family: OPPOSans, OPPOSans;
          font-size: 40px;
          line-height: 1.1;
          margin-top: 2px;
          text-shadow: 0 0 8px #0a7bbd33;
          display: flex;
          align-items: baseline;
          margin-left: 20px;

          /* 数值 */
          .dialog-value-number {
            margin-left: 10px;
            font-size: 40px;
          }

          /* 单位 */
          .dialog-value-unit {
            font-weight: normal;
            font-size: 26px;
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
