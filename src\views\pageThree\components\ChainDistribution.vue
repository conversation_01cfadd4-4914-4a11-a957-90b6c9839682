<!--
  放款企业重点产业链分布组件
  功能：
  1. 展示放款资金投放的重点产业链分布
  2. 提供产业链数据的柱状图展示
  3. 支持数据自动滚动效果
  4. 提供详细数据的弹窗展示
  5. 支持产业链名称的tooltip提示
-->
<template>
  <div class="ChainDistribution-fwstjjClass">
    <!-- 头部标题区域 -->
    <div class="ChainDistribution-headerImg">
      <div class="ChainDistribution-lbClass">放款企业重点产业链分布</div>
      <div class="ChainDistribution-rightClass">
        <!-- 查看更多按钮 -->
        <div class="ChainDistribution-djgdClass" @click="handleClick">点击查看更多</div>
        <div>
          <img
            src="@/assets/dp/rightT.png"
            class="ChainDistribution-imgRight"
          />
        </div>
      </div>
    </div>
    <!-- 主体内容区域 -->
    <div class="ChainDistribution-bottomClass">
      <!-- 产业链总数展示 -->
      <div class="ChainDistribution-header">
        放款资金投放重点产业链：<span class="ChainDistribution-total">{{16}}</span> 个
      </div>
      <!-- 柱状图容器 -->
      <div class="ChainDistribution-industryBar_box">
        <div class="ChainDistribution-industryBar" ref="industryBar">
          <!-- 产业链名称tooltip -->
          <el-tooltip
            v-for="(item, index) in originalData"
            :key="index"
            :content="item.name"
            placement="right"
            effect="dark"
            :popper-class="'custom-tooltip'"
          >
            <div class="tooltip-trigger" :style="{ 
              top: index * 40 + 'px',
              left: '0',
              width: '150px',
              height: '30px'
            }"></div>
          </el-tooltip>
        </div>
      </div>
    </div>
    <!-- 详细数据弹窗 -->
    <DialogBar
      :visible="visible"
      @close="handleClose"
      title="放款企业重点产业链分布"
      :chartData="originalData"
      :chart-width="1000"
      :chart-height="468"
      :colorStops="colorStops"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import DialogBar from "../../components/dialogBar.vue";

export default {
  name: "ChainDistribution",
  components: {
    DialogBar
  },
  data() {
    return {
      visible: false, // 弹窗显示状态
      // 渐变色配置
      colorStops: [
        { offset: 0, color: "#FFC941" },
        { offset: 1, color: "#FF892B" },
      ],
      myChart: null, // echarts实例
      scrollTimer: null, // 滚动定时器
      currentIndex: 0, // 当前滚动索引
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
      originalData: [], // 原始数据
      // 产业链列表
      paramsList: [
        "工业互联网",
        "轨道交通",
        "人工智能与机器人（含车载智能控制系统）",
        "生态环保",
        "新材料",
        "新型显示",
        "低空经济",
        "卫星互联网与卫星应用",
        "集成电路",
        "智能终端",
        "汽车（新能源汽车）",
        "高端软件与操作系统",
        "大飞机制造与服务",
        "文创业（含数字文创）",
        "新能源",
        "工业无人机",
        "航空发动机",
        "高端医疗器械",
        "氢能"
      ]
    };
  },
  mounted() {
    this.initData();
  },
  beforeDestroy() {
    // 清理定时器和图表实例
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    // 打开弹窗
    handleClick() {
      this.visible = true;
    },
    // 关闭弹窗
    handleClose() {
      this.visible = false;
    },
    // 初始化数据
    async initData() {
      // 构建产业链金额查询参数
      let obj = this.paramsList.map(item => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 4,
        dimIndustryChain: item,
        indexName: "放款企业重点产业链金额",
      }));

      // 构建产业链笔数查询参数
      let obj2 = this.paramsList.map(item => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 4,
        dimIndustryChain: item,
        indexName: "放款企业重点产业链笔数",
      }));

      // 合并查询参数
      let obj3 = obj.concat(obj2);
      try {
        // 获取数据
        const res = await productUpdateList(obj3);
        let list = res.data.data;
        // 分离金额和笔数数据
        let list1 = list.filter(item => item.indexName === "放款企业重点产业链金额");
        let list2 = list.filter(item => item.indexName === "放款企业重点产业链笔数");
        
        // 处理数据格式
        list1.map((item, index) => {
          item.value = item.indexValue - 0;
          item.name = this.paramsList[index];
          item.count = list2.find(d => d.dimIndustryChain === item.dimIndustryChain).indexValue - 0;
        });

        this.originalData = list1;
        this.originalData.sort((a, b) => a.value - b.value);
        
        await this.$nextTick();
        this.initBar();
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },
    // 初始化柱状图
    initBar() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      const chartRef = this.$refs.industryBar;
      if (!chartRef) {
        console.warn('Chart container not found, retrying...');
        setTimeout(() => this.initBar(), 10000);
        return;
      }

      this.myChart = echarts.init(chartRef);
      const data = [...this.originalData].sort((a, b) => a.value - b.value);
      let maxValue = Math.max(...data.map((d) => Number(d.value)));
      maxValue = maxValue * 1.5;

      // 配置图表选项
      this.myChart.setOption({
        grid: { left: this.$autoFontSize(150), right: this.$autoFontSize(20), top: this.$autoFontSize(10), bottom: this.$autoFontSize(30) },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            if (data.name && data.name.length > 9) {
              return data.name;
            }
            return '';
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: this.$autoFontSize(14)
          },
          extraCssText: 'padding: 8px 12px;'
        },
        xAxis: {
          type: "value",
          show: false,
          max: maxValue, 
        },
        yAxis: {
          type: "category",
          data: this.originalData.map((item) => item.name),
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: "#fff",
            padding: [this.$autoFontSize(5), 0, 0, 0],
            fontSize: this.$autoFontSize(12),
            formatter: function(value) {
              if (value.length > 9) {
                return value.substring(0, 9) + '...';
              }
              return value;
            },
            rich: {
              a: {
                color: '#fff',
                fontSize: this.$autoFontSize(12),
                lineHeight: this.$autoFontSize(20)
              }
            }
          },
        },
        series: [
          // 背景条
          {
            type: "bar",
            data: data.map(() => maxValue),
            barWidth: this.$autoFontSize(22),
            itemStyle: {
              color: "rgba(63, 169, 245, 0.2)",
              borderRadius: 0,
            },
            barGap: "-80%",
            z: 1,
          },
          // 主数据条
          {
            type: "bar",
            data: data.map((item) => item.value),
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: "#FFC941" },
                  { offset: 1, color: "#FF892B" },
                ],
              },
              borderRadius: 0,
            },
            label: {
              show: true,
              position: "right",
              formatter: (params) => {
                const d = data[params.dataIndex];
                return `${d.value}亿元/${d.count}笔`;
              },
              color: "#fff",
              fontSize: this.$autoFontSize(14),
            },
          },
          // 白色标记点
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "rect",
            symbolSize: [this.$autoFontSize(4), this.$autoFontSize(16)],
            symbolOffset: [0, 2],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: data.map((item, idx) => [Number(item.value), idx]),
          },
        ],
      });

      // 添加鼠标悬停事件处理
      this.myChart.on('mouseover', { seriesIndex: 0 }, (params) => {
        const option = this.myChart.getOption();
        const data = option.yAxis[0].data;
        const index = params.dataIndex;
        const value = data[index];
        
        if (value && value.length > 9) {
          this.myChart.setOption({
            yAxis: {
              axisLabel: {
                rich: {
                  name: {
                    opacity: 0
                  },
                  full: {
                    opacity: 1
                  }
                }
              }
            }
          });
        }
      });

      this.myChart.on('mouseout', { seriesIndex: 0 }, () => {
        this.myChart.setOption({
          yAxis: {
            axisLabel: {
              rich: {
                name: {
                  opacity: 1
                },
                full: {
                  opacity: 0
                }
              }
            }
          }
        });
      });

      // 启动自动滚动
      this.startAutoScroll(data.length);

      // 监听窗口大小变化
      window.addEventListener("resize", () => this.myChart.resize());
    },
    // 启动自动滚动
    startAutoScroll(totalItems) {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }
      this.scrollTimer = setInterval(() => {
        const option = this.myChart.getOption();
        const currentData = option.yAxis[0].data;
        
        // 更新当前索引
        this.currentIndex = (this.currentIndex + this.scrollStep) % totalItems;

        // 重新排序数据
        const newData = [...currentData];
        for (let i = 0; i < this.scrollStep; i++) {
          newData.unshift(newData.pop())
        }
        
        // 更新原始数据
        this.originalData = newData.map((n) => {
          if (this.originalData.find((d) => d.name == n)) {
            const item = this.originalData.find((d) => d.name == n)
            return item
          }
        });

        // 重新计算最大值
        let maxValue = Math.max(...this.originalData.map((d) => Number(d.value)));
        maxValue = maxValue * 1.5;

        // 更新图表配置
        this.myChart.setOption({
          yAxis: {
            data: newData,
          },
          xAxis: {
            max: maxValue,
          },
          series: [
            // 背景条
            {
              data: newData.map(() => maxValue),
            },
            // 主数据
            {
              data: newData.map((name) => {
                const item = this.originalData.find((d) => d.name === name);
                return item ? item.value : 0;
              }),   
              label: {
                show: true,
                position: "right",
                formatter: (params) => {
                  const d = this.originalData[params.dataIndex];
                  return `${d.value}亿元/${d.count}笔`;
                },
              },
            },
            // 白色标记点
            {
              data: newData.map((name, idx) => {
                const item = this.originalData.find((d) => d.name === name);
                return [item ? Number(item.value) : 0, idx];
              }),
            },
          ],
        });
      }, this.scrollSpeed);
    },
  },
};
</script>

<style scoped lang="scss">
/* 组件容器 */
.ChainDistribution-fwstjjClass {
  width: 950px;
  height: 25vh;
  display: flex;
  flex-direction: column;

  /* 头部标题区域 */
  .ChainDistribution-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    /* 标题文本 */
    .ChainDistribution-lbClass {
      height: 80px;
      line-height: 80px;
    }

    /* 右侧按钮区域 */
    .ChainDistribution-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      /* 查看更多按钮 */
      .ChainDistribution-djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      /* 箭头图标 */
      .ChainDistribution-imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 柱状图容器 */
  .ChainDistribution-industryBar_box {
    height: 1200px;
  }

  .ChainDistribution-industryBar {
    width: 920px;
    height: 1200px;
    position: relative;

    /* 提示框触发器 */
    .tooltip-trigger {
      position: absolute;
      cursor: pointer;
      z-index: 10;
      background: transparent;
    }
  }

  /* 主体内容区域 */
  .ChainDistribution-bottomClass {
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    overflow: hidden;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 8px;
      background: #0a2e4a;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #2b9cff 0%, #398fff 100%);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #0a2e4a;
      border-radius: 4px;
    }

    /* 产业链总数标题 */
    .ChainDistribution-header {
      line-height: 60px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      margin-left: 40px;
      width: 90%;
    }

    /* 总数数字样式 */
    .ChainDistribution-total {
      font-family: OPPOSans;
      font-weight: normal;
      font-size: 44px;
      color: #ffffff;
      letter-spacing: 2px;
      text-align: center;
      font-style: normal;
      background: linear-gradient(180deg, #ffffff 0%, #ffce7c 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: bold;
    }
  }
}

/* 自定义tooltip样式 */
:deep(.custom-tooltip) {
  background: rgba(0, 0, 0, 0.8) !important;
  border: none !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  color: #fff !important;
  z-index: 9999 !important;
}
</style>
