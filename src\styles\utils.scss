// 使用 @forward 导出所有变量和混合器
@forward 'variables';
@forward 'mixins';

// 清除浮动
.clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本溢出省略号
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// flex 布局
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 常用间距
.mt-10 { margin-top: 10px; }
.mb-10 { margin-bottom: 10px; }
.ml-10 { margin-left: 10px; }
.mr-10 { margin-right: 10px; } 