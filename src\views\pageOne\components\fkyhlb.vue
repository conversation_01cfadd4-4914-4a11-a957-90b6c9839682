<!-- 
  放款用户列表组件
  功能：展示放款用户信息，包括企业名称、金融机构和放款金额
  特点：
  1. 支持无缝滚动展示数据
  2. 提供查看更多功能
  3. 响应式设计
  4. 美观的表格布局
  5. 支持鼠标滚轮控制
-->
<template>
  <div class="fkyhlbClass3">
    <!-- 头部区域：标题和查看更多按钮 -->
    <div class="headerImg">
      <div class="lbClass">放款用户</div>
      <div class="rightClass">
        <div class="djgdClass" @click="openMoreDialog">点击查看更多</div>
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>
    <!-- 主体内容区域 -->
    <div class="bottomClass">
      <div class="box-con-list">
        <!-- 表格头部 -->
        <div class="tableHeader">
          <div class="table-name">
            <div>企业名称</div>
            <div>金融机构</div>
            <div>放款金额（万元）</div>
          </div>
        </div>
        <!-- 表格内容区域：使用无缝滚动组件 -->
        <div class="warp">
          <vue-seamless-scroll
            ref="vueSeamlessScroll1"
            :data="carList"
            style="height: 100%; overflow: hidden"
            :class-option="classOption"
            @mousewheel.native="handleScroll"
          >
            <!-- 循环渲染表格行 -->
            <div v-for="(item, index) in carList" :key="index">
              <div class="table-items">
                <div style="width: 45%">{{ item.enterprisePrivacyName }}</div>
                <div style="width: 35%; text-align: left">
                  {{ item.financialInstitutionShortName }}
                </div>
                <div style="width: 15.3%">{{ item.loanAmount }}</div>
              </div>
            </div>
          </vue-seamless-scroll>
        </div>
        <!-- 查看更多对话框组件 -->
        <dialogMore1
          ref="dialogMoreRef1"
          :maxDt="maxDt"
          :isMaxDtReady="isMaxDtReady"
          :selectedRegion="selectedRegion"
        />
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import dialogMore1 from "./dialogMore1.vue";
import { productUpdate } from "@/api/article.js";

export default {
  name: "fkyhlb",
  components: {
    vueSeamlessScroll,
    dialogMore1,
  },
  data() {
    return {
      // 无缝滚动配置选项
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量
        hoverStop: true, // 是否开启鼠标悬停停止
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 50, // 单步运动停止的高度
        singleWidth: 0, // 单步运动停止的宽度
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      },
      // 放款用户列表数据
      carList: [],
    };
  },
  // 组件属性定义
  props: {
    maxDt: {
      type: String,
      required: true,
    },
    isMaxDtReady: {
      type: Boolean,
      required: true,
    },
    //区域
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  // 监听属性变化
  watch: {
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用接口获取数据
          this.getfkyhlb();
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.getfkyhlb(newVal);
      },
    },
  },
  mounted() {
    // 组件挂载时的初始化逻辑
  },
  methods: {
    // 打开查看更多对话框
    openMoreDialog() {
      this.$refs.dialogMoreRef1.openDialog();
    },
    // 获取放款用户列表数据
    async getfkyhlb() {
      let obj = {
        indexName: "放款用户列表",
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustryChain: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        dimNum: 1,
      };
      try {
        const res = await productUpdate(obj);
        if (res.data?.code === 200) {
          // 添加数组空值判断
          if (res.data.data?.length > 0) {
            const bizContent = res.data.data[0].bizContent;
            if (bizContent) {
              this.carList = JSON.parse(bizContent);
            } else {
              // console.warn('bizContent 字段不存在');
              this.carList = [];
            }
          } else {
            // console.warn("接口返回空数据集");
            this.carList = [];
          }
        } else {
          console.error("接口异常:", res.data?.msg);
        }
      } catch (error) {
        console.error("请求失败:", error);
        this.carList = [];
      }
    },
    // 显示内容模态框
    showConModal(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /picShow/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      if (data) {
        //点击事件处理逻辑
      }
    },
    // 处理鼠标滚轮事件
    handleScroll(e) {
      this.$refs.vueSeamlessScroll1.yPos =
        this.$refs.vueSeamlessScroll1.yPos - e.deltaY + 60;
      // 如果是正数说明是往上滚，限制滚动位置
      if (this.$refs.vueSeamlessScroll1.yPos > 0) {
        this.$refs.vueSeamlessScroll1.yPos = 0;
        return;
      }
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 组件主容器样式 */
.fkyhlbClass3 {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    // 添加文字阴影效果
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    /* 标题样式 */
    .lbClass {
      height: 80px;
      line-height: 80px;
    }

    /* 右侧查看更多按钮样式 */
    .rightClass {
      display: flex;
      align-items: center;
      justify-content: center;
      .djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }
      .imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 底部内容区域样式 */
  .bottomClass {
    width: 100%;
    flex: 1;
    // 设置渐变背景
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);

    /* 表格容器样式 */
    .box-con-list {
      width: 100%;
      .tableHeader {
        width: 100%;
        box-sizing: border-box;
        padding: 0 35px;
        padding-top: 30px;
        .table-name {
          padding: 0 64px;
          box-sizing: border-box;
          display: flex;
          height: 80px;
          background: rgba(255, 255, 255, 0.09);
          font-weight: 500;
          font-size: 26px;
          color: #41ccff;
          letter-spacing: 1px;
          align-items: center;
          justify-content: space-between;
          // 设置列宽
          div:nth-child(1) {
            width: 40%;
          }
          div:nth-child(2) {
            width: 20%;
          }
          div:nth-child(2) {
            width: 20%;
          }
        }
      }
    }

    /* 表格内容区域样式 */
    .warp {
      height: 17vh;
      padding: 0 35px;
      .table-items {
        display: flex;
        padding: 0 64px;
        box-sizing: border-box;
        height: 80px;
        align-items: center;
        font-weight: 400;
        font-size: 26px;
        color: #ffffff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.22);
      }
    }
  }
}
</style>
