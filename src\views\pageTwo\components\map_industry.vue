<!-- 
  成都市区域地图组件
  功能：展示成都市各区县的金融机构放款情况
  特点：
  1. 使用ECharts实现地图可视化
  2. 支持区域高亮和交互
  3. 自定义tooltip展示详细数据
  4. 支持地图缩放和漫游
  5. 响应式设计，支持窗口大小变化
-->
<template>
  <div class="mapClass2">
    <!-- 主地图容器 -->
    <div ref="chartRef" style="width: 100%; height: 100%" v-if="flag"></div>
    <!-- 备用地图容器 -->
    <div ref="chartRef2" style="width: 100%; height: 100%" v-else></div>
    <!--     <div class="shuangliu-tooltip" v-if="showShuangliuTooltip">
      <div class="tkClass">
        <div class="tkName">锦江区</div>
        <div class="tkBody">
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              累计认证企业:{{ shuangliuTooltipData.enterprises }}家
            </div>
          </div>
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              放款金额:{{ shuangliuTooltipData.loanAmount }}亿
            </div>
          </div>
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              放款户数:{{ shuangliuTooltipData.loanHouseholds }}户
            </div>
          </div>
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              平均利率:{{ shuangliuTooltipData.averageRate }}%
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import geoJsonData from "../../pageOne/img/cds.json";

export default {
  name: "map_base",
  data() {
    return {
      twoMap: null,
      flag: true,
      rotationAngle: 10,
      cdList: [], // 成都市数据列表
      chartInstance: null, // 主地图实例
      chartInstance2: null, // 备用地图实例
      mapName: "",
      cityName: "",
      // 区域坐标点集合
      districtMapDot: {
        点1: [104.45, 30.55], // 西北
        点2: [104.5, 30.35], // 西南
        点3: [104.6, 30.6], // 北部
        点4: [104.75, 30.5], // 东北
        点5: [104.8, 30.4], // 东部
        点6: [104.7, 30.3], // 东南
        点7: [104.55, 30.25], // 南部
        点8: [104.65, 30.45], // 中部偏东
      },
      centerDot: [104.547, 30.413], // 简阳市中心点
      showCustomTooltip: false, // 是否显示自定义提示框
      tooltipContent: "", // 提示框内容
      tooltipPosition: { x: 0, y: 0 }, // 提示框位置
      randomData: {}, // 随机生成的数据缓存
      showShuangliuTooltip: false, // 是否显示双流区提示框
      dimCounty: "锦江区", // 当前选中的区县
      // 锦江区默认数据
      shuangliuTooltipData: {
        name: "锦江区",
        dimCounty: "锦江区",
        enterprises: 7883,
        loanAmount: 20.51,
        loanHouseholds: 924,
        averageRate: 4.31,
      },
    };
  },
  props: {},
  mounted() {
    this.initChart();
    this.getmap();
    // 初始化时显示锦江区的弹框
    setTimeout(() => {
      this.showJinjiangTooltip({
        name: "锦江区",
        componentType: "series",
        seriesType: "map",
      });
      if (this.chartInstance) {
        this.chartInstance.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          name: "锦江区",
        });
        this.chartInstance.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          name: "锦江区",
        });
      }
    }, 500);
  },
  methods: {
    // 获取地图数据
    async getmap() {
      let obj = [
        {
          indexName: "金融机构区域放款情况",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 1,
        },
      ];
      await productUpdateList(obj).then((res) => {
        this.cdList = res.data.data;
        // 数据加载完成后，确保显示锦江区数据
        this.$nextTick(() => {
          this.showJinjiangTooltip({
            name: "锦江区",
            componentType: "series",
            seriesType: "map",
          });
          if (this.chartInstance) {
            this.chartInstance.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              name: "锦江区",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: "锦江区",
            });
          }
        });
      });
    },
    // 显示双流区弹框
    showShuangliuPopup() {
      this.showShuangliuTooltip = true;
    },
    // 隐藏双流区弹框
    hideShuangliuPopup() {
      this.showShuangliuTooltip = false;
    },
    // 显示锦江区提示框
    showJinjiangTooltip(params) {
      const districtData = this.getDistrictData(params.name);
      if (this.chartInstance) {
        this.chartInstance.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          name: params.name,
        });
      }
    },
    // 返回成都市视图
    backToChengdu() {
      this.flag = true;
      this.twoMap = null;
    },
    // 生成随机数
    generateRandomNumber(min = 5000, max = 8000) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    // 获取区域数据
    getDistrictData(districtName) {
      // 如果该区域没有数据，则生成随机数据并缓存
      if (!this.randomData[districtName]) {
        this.randomData[districtName] = {
          enterprises: this.generateRandomNumber(5000, 8000),
          loanAmount: (Math.random() * 20 + 5).toFixed(2),
          loanHouseholds: this.generateRandomNumber(800, 1500),
          averageRate: (Math.random() * 2 + 3).toFixed(2),
        };
      }
      return this.randomData[districtName];
    },
    // 初始化地图
    async initChart() {
      if (!this.$refs.chartRef) return;
      this.chartInstance = null;
      if (this.chartInstance) {
        this.chartInstance.dispose();
        this.chartInstance = null;
      }
      if (this.chartInstance2) {
        this.chartInstance2.dispose();
        this.chartInstance2 = null;
      }
      try {
        // 注册地图
        echarts.registerMap(this.mapName, geoJsonData);
        // 初始化 ECharts 实例
        this.chartInstance = echarts.init(this.$refs.chartRef);

        // 配置地图选项
        const option = {
          // 地理坐标系组件
          geo: [
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false, // 禁止缩放
              zoom: 1.2, // 默认显示级别
              layoutSize: "95%",
              layoutCenter: ["50%", "50%"],
              selectedMode: true,
              // 标签配置
              label: {
                show: true,
                color: "#fff",
                fontSize: this.$autoFontSize(15),
                fontWeight: "bold",
                position: "right",
                offset: [0, 0],
                verticalAlign: "middle",
                align: "right",
              },
              // 地图区域样式
              itemStyle: {
                areaColor: {
                  type: "linear-gradient",
                  x: 0,
                  y: 200,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(37,108,190,0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(15,169,195,0.3)",
                    },
                  ],
                  global: true,
                },
                borderColor: "#4ecee6",
                borderWidth: 1,
              },
              // 高亮状态样式
              emphasis: {
                itemStyle: {
                  areaColor: {
                    type: "linear-gradient",
                    x: 0,
                    y: 300,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: "rgba(37,108,190,0.8)",
                      },
                      {
                        offset: 1,
                        color: "rgba(15,169,195,0.8)",
                      },
                    ],
                    global: true,
                  },
                  borderWidth: 2,
                  shadowBlur: 20,
                  shadowColor: "rgba(0,218,255,0.5)",
                },
                label: {
                  show: true,
                  color: "#fff",
                  fontSize: this.$autoFontSize(15),
                  fontWeight: "bold",
                },
              },
              zlevel: 3,
            },
            // 第二层地图（用于边框效果）
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false,
              zoom: 1.2,
              layoutSize: "95%",
              layoutCenter: ["50%", "50%"],
              itemStyle: {
                borderColor: "rgba(192,245,249,.6)",
                borderWidth: 2,
                shadowColor: "#2C99F6",
                shadowOffsetY: 0,
                shadowBlur: 120,
                areaColor: "rgba(29,85,139,.2)",
              },
              zlevel: 2,
              silent: true,
            },
            // 第三层地图（用于背景效果）
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false,
              zoom: 1.2,
              layoutSize: "95%",
              layoutCenter: ["50%", "51.5%"],
              itemStyle: {
                areaColor: "rgba(0,27,95,0.4)",
                borderColor: "#004db5",
                borderWidth: 1,
              },
              zlevel: 1,
              silent: true,
            },
          ],
          // 提示框配置
          tooltip: {
            show: true,
            trigger: "item",
            formatter: (params) => {
              const city = params.name.trim() || "锦江区";

              // 获取该区域的银行数据
              let banks = this.cdList.filter((item) => {
                return item.dimCounty == city;
              });

              // 处理无数据情况
              if (!banks || banks.length === 0) {
                return `
                  <div class="industry-tkClass">
                    <div class="industry-tkName">${city}</div>
                    <div class="industry-tkBody" style="padding: 10px 0 10px 20px;">
                      <div style="color:#fff;font-size:${this.$autoFontSize(
                        14
                      )}px;">暂无数据</div>
                    </div>
                  </div>
                `;
              }

              // 解析银行数据
              try {
                banks = JSON.parse(banks[0].bizContent);
              } catch (error) {
                console.error("解析数据失败:", error);
                return `
                  <div class="industry-tkClass">
                    <div class="industry-tkName">${city}</div>
                    <div class="industry-tkBody" style="padding: 10px 0 10px 20px;">
                      <div style="color:#fff;font-size:${this.$autoFontSize(
                        14
                      )}px;">数据格式错误</div>
                    </div>
                  </div>
                `;
              }

              // 验证数据格式
              if (!Array.isArray(banks)) {
                return `
                  <div class="industry-tkClass">
                    <div class="industry-tkName">${city}</div>
                    <div class="industry-tkBody" style="padding: 10px 0 10px 20px;">
                      <div style="color:#fff;font-size:${this.$autoFontSize(
                        14
                      )}px;">数据格式错误</div>
                    </div>
                  </div>
                `;
              }

              // 构建提示框HTML
              let html = `
                <div class="industry-tkClass">
                  <div class="industry-tkName">${city}</div>
                  <div class="industry-tkBody" style="padding: 10px 0 10px 20px;">
              `;
              // 遍历银行数据
              banks.forEach((bank) => {
                html += `
                  <div style="margin-bottom: ${this.$autoFontSize(
                    0
                  )}px;display:flex"class="industry-tkItemClass">
                     <div  class="industry-cir"></div>
                    <div style="color:#fff;font-size:${this.$autoFontSize(
                      14
                    )}px;margin-left:${this.$autoFontSize(
                  14
                )}px;;"class="industry-cirRight">${bank.shortName}：</div>
                    <div style="color:#fff;font-size:${this.$autoFontSize(
                      14
                    )}px;margin-right:${this.$autoFontSize(
                  14
                )}px"class="industry-cirRight">${bank.loanAmt}</div>
                    <div style="color:#fff;font-size:${this.$autoFontSize(
                      14
                    )}px;margin-right:${this.$autoFontSize(
                  14
                )}px"class="industry-cirRight">${bank.loanSum}</div>
                    <div style="color:#fff;font-size:${this.$autoFontSize(
                      14
                    )}px;margin-right:${this.$autoFontSize(
                  14
                )}px"class="industry-cirRight">${bank.loanRate}</div>
                  </div>
                `;
              });
              html += `</div></div>`;
              return html;
            },
            backgroundColor: "rgba(0,0,0,0.7)",
            borderColor: "#fff",
            borderWidth: 1,
            padding: 0,
            marginTop: -20,
          },
          // 地图系列配置
          series: [
            {
              type: "map",
              map: this.mapName,
              geoIndex: 0,
              roam: false,
              selectedMode: false,
              // 地图样式
              itemStyle: {
                borderColor: "#2ab8ff",
                borderWidth: 1.5,
                areaColor: "#12235c",
              },
              // 高亮状态样式
              emphasis: {
                itemStyle: {
                  areaColor: "rgba(224, 247, 255, 0.1)",
                  borderWidth: 4,
                  shadowBlur: 30,
                },
              },
              // 标签配置
              label: {
                show: true,
                fontSize: this.$autoFontSize(15),
                formatter: (params) => {
                  if (params.name === "崇州市") {
                    return "{dot|•} {text|" + params.name + "}";
                  }
                  if (params.name === "锦江区") {
                    return "{text|" + params.name + "}";
                  }
                  return params.name;
                },
                rich: {
                  dot: {
                    color: "#fff",
                    fontSize: 20,
                    padding: [0, 0, 0, 0],
                  },
                  text: {
                    color: "#fff",
                    fontSize: 12,
                    padding: [0, 0, 0, 0],
                  },
                },
              },
              // 标签布局配置
              labelLayout: {
                hideOverlap: true,
                draggable: true,
                x: (params) => {
                  if (params.name === "崇州市") {
                    return params.x + 40;
                  }
                  return params.x;
                },
              },
            },
          ],
        };

        // 记录最后悬停的区域
        let lastHoveredArea = "锦江区";

        // 鼠标移出地图时的事件处理
        this.chartInstance.getZr().on("globalout", () => {
          this.dimCounty = lastHoveredArea;
          // 取消所有高亮
          this.chartInstance.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
          });
          // 高亮最后悬停的区域
          this.chartInstance.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            name: lastHoveredArea,
          });
          // 显示该区域的提示框
          setTimeout(() => {
            this.showJinjiangTooltip({
              name: lastHoveredArea,
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
          }, 100);
        });

        // 鼠标移入区域时的事件处理
        this.chartInstance.on("mouseover", (params) => {
          if (params.name) {
            lastHoveredArea = params.name;
            // 取消所有高亮
            this.chartInstance.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
            });
            // 高亮当前区域
            this.chartInstance.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
            // 显示当前区域的提示框
            this.showJinjiangTooltip({
              name: lastHoveredArea,
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
          }
        });
          // 点击事件处理
          this.chartInstance.on("click", (params) => {
            this.$emit("region-click", params.name);
            if (params.name === "锦江区") {
              this.showShuangliuPopup();
            }
          });

        // 设置地图配置
        this.chartInstance.setOption(option);

        // 初始化时显示锦江区数据
        setTimeout(() => {
          this.showJinjiangTooltip({
            name: "锦江区",
            componentType: "series",
            seriesType: "map",
          });
          this.chartInstance.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            name: "锦江区",
          });
          this.chartInstance.dispatchAction({
            type: "showTip",
            seriesIndex: 0,
            name: "锦江区",
          });
        }, 300);

        // 设置默认高亮区域
        this.dimCounty = "锦江区";
        lastHoveredArea = "锦江区";
      } catch (error) {
        console.error("加载 GeoJSON 数据失败:", error);
      }
    },
  },
  watch: {
    flag(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$emit("changeCity", true);
          this.initChart();
        });
      }
    },
  },
};
</script>
<style scope="scoped" lang="scss">
.mapClass2 {
  width: 1600px;
  height: 1200px;
  position: absolute;
  top: 7%;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  margin: auto;
}

.industry-tkClass {
  width: 690px;

  background: linear-gradient(
    180deg,
    rgba(30, 15, 1, 0.6) 0%,
    rgba(176, 88, 0, 0.27) 100%
  );
  border: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(251, 230, 176, 1),
      rgba(246, 197, 120, 0)
    )
    2 2;
  backdrop-filter: blur(4px);
  position: absolute;
  top: -100px;
  left: 0;
  z-index: 2000;
  padding-top: 17px;
  box-sizing: border-box;
  .industry-tkName {
    width: 80%;
    height: 54px;
    line-height: 54px;
    background: linear-gradient(
      270deg,
      rgba(255, 194, 0, 0) 0%,
      rgba(255, 142, 0, 0.71) 100%
    );
    font-weight: 500;
    font-size: 34px;
    color: #ffffff;
    letter-spacing: 1px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    padding-left: 40px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
  }
  .industry-tkBody {
    width: 100%;
    padding: 20px 0 25px 0px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    .industry-tkItemClass {
      display: flex;
      align-items: center;
      .industry-cir {
        width: 6px;
        height: 6px;
        background: #ffffff;
        box-shadow: 0px 0px 13px 0px #ff9c00, 0px 0px 8px 0px #ff9000;
      }
      .industry-cirRight {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #ffffff;
        line-height: 52px;
        letter-spacing: 1px;
        margin-left: 15px;
      }
    }
  }
}
</style>
