import request from '@/router/axios.js'



//单个指标查询
export function productUpdate(data){
    return request({
        method:'post',
        url:'api/general/data/view/queryMetric',
        data,
    })
}
// 获取更新最近时间
export function queryMaxDt(data){
    return request({
        method:'get',
        url:'api/general/data/view/queryMaxDt',
    })
}
//查询翻页数据
export function productUpdatePage(data){
    return request({
        method:'post',
        url:'api/general/data/view/queryMetricPage',
        data,
    })
}
//多个指标查询
export function productUpdateList(data){
    return request({
        method:'post',
        url:'api/general/data/view/queryMetricList',
        data,
    })
}
//查询折线图数据
export function queryLineChartData(data){   
    return request({
        method:'post',
        url:'api/general/data/view/queryLineChartData',
        data,
    })
}
//实时查询单个最上面5个指标数据
export function  queryRealtimeMetric(data){   
    return request({
        method:'post',
        url:'api/general/data/view/queryRealtimeMetric',
        data,
    })
}
//实时查询多个个最上面5个指标数据
export function  queryRealtimeMetricList(data){   
    return request({
        method:'post',
        url:'api/general/data/view/queryRealtimeMetricList',
        data,
    })
}
//查询天气城市代码
export function  queryWeatherCityCode(data){   
    return request({
        method:'get',
        url:`api/general/data/view/queryWeatherCityCode?queryCity=${data}`,
    })
}
//新增员工出勤
/* export function attendancerecordAdd(data){
    return request({
        method:'post',
        url:'/api/attendancerecord/attendancerecordAdd',
        data
    })
} */
/*  export function SelectuserInfoRequest(data){
    return request({
        isToken:false,
        authorization: true,
        method:'post',
        url:'/api/blade-user/unAuth/selectScreenUserInfo',
        data
    })
}  */
// 获取用户信息用于登录
export function SelectuserInfoRequest(data){
    return request({
        isToken: false,
        authorization: true,
        method: 'post',
        url: '/api/blade-user/unAuth/selectScreenUserInfo',
        data
    })
} 