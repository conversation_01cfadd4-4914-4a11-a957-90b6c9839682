import {validatenull} from "./validate";
//表单序列化
export const serialize = (data) => {
  let list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join("&");
};
// 获取当前时间的yyyyMMdd格式字符串
export const getyyyyMMddHHMMSS = () => {
  let nowDate = new Date();
  let getMonth = nowDate.getMonth();
  let getDate = nowDate.getDate();
  let getHour = nowDate.getHours();
  let getMinute = nowDate.getMinutes();
  let getSecond = nowDate.getSeconds();
  let year = nowDate.getFullYear();
  let month = getMonth + 1 < 10 ? "0" + (getMonth + 1) : getMonth + 1;
  let day = getDate < 10 ? "0" + getDate : getDate;
  let hour = getHour < 10 ? "0" + getHour : getHour;
  let minute = getMinute < 10 ? "0" + getMinute : getMinute;
  let second = getSecond < 10 ? "0" + getSecond : getSecond;
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

export const getObjType = (obj) => {
  var toString = Object.prototype.toString;
  var map = {
    "[object Boolean]": "boolean",
    "[object Number]": "number",
    "[object String]": "string",
    "[object Function]": "function",
    "[object Array]": "array",
    "[object Date]": "date",
    "[object RegExp]": "regExp",
    "[object Undefined]": "undefined",
    "[object Null]": "null",
    "[object Object]": "object",
  };
  if (obj instanceof Element) {
    return "element";
  }
  return map[toString.call(obj)];
};
export const getViewDom = () => {
  return window.document
    .getElementById("avue-view")
    .getElementsByClassName("el-scrollbar__wrap")[0];
};
/**
 * 对象深拷贝
 */
export const deepClone = (data) => {
  var type = getObjType(data);
  var obj;
  if (type === "array") {
    obj = [];
  } else if (type === "object") {
    obj = {};
  } else {
    //不再具有下一层次
    return data;
  }
  if (type === "array") {
    for (var i = 0, len = data.length; i < len; i++) {
      obj.push(deepClone(data[i]));
    }
  } else if (type === "object") {
    for (var key in data) {
      obj[key] = deepClone(data[key]);
    }
  }
  return obj;
};
/**
 * 设置灰度模式
 */
export const toggleGrayMode = (status) => {
  if (status) {
    document.body.className = document.body.className + " grayMode";
  } else {
    document.body.className = document.body.className.replace(" grayMode", "");
  }
};
/**
 * 设置主题
 */
export const setTheme = (name) => {
  document.body.className = name;
};

/**
 * 加密处理
 */
export const encryption = (params) => {
  let {data, type, param, key} = params;
  let result = JSON.parse(JSON.stringify(data));
  if (type == "Base64") {
    param.forEach((ele) => {
      result[ele] = btoa(result[ele]);
    });
  } else if (type == "Aes") {
    param.forEach((ele) => {
      result[ele] = window.CryptoJS.AES.encrypt(result[ele], key).toString();
    });
  }
  return result;
};

/**
 * 浏览器判断是否全屏
 */
export const fullscreenToggel = () => {
  if (fullscreenEnable()) {
    exitFullScreen();
  } else {
    reqFullScreen();
  }
};
/**
 * esc监听全屏
 */
export const listenfullscreen = (callback) => {
  function listen() {
    callback();
  }

  document.addEventListener("fullscreenchange", function () {
    listen();
  });
  document.addEventListener("mozfullscreenchange", function () {
    listen();
  });
  document.addEventListener("webkitfullscreenchange", function () {
    listen();
  });
  document.addEventListener("msfullscreenchange", function () {
    listen();
  });
};
/**
 * 浏览器判断是否全屏
 */
export const fullscreenEnable = () => {
  var isFullscreen =
    document.isFullScreen ||
    document.mozIsFullScreen ||
    document.webkitIsFullScreen;
  return isFullscreen;
};

/**
 * 浏览器全屏
 */
export const reqFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.documentElement.requestFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.documentElement.webkitRequestFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.documentElement.mozRequestFullScreen();
  }
};
/**
 * 浏览器退出全屏
 */
export const exitFullScreen = () => {
  if (document.documentElement.requestFullScreen) {
    document.exitFullScreen();
  } else if (document.documentElement.webkitRequestFullScreen) {
    document.webkitCancelFullScreen();
  } else if (document.documentElement.mozRequestFullScreen) {
    document.mozCancelFullScreen();
  }
};
/**
 * 递归寻找子类的父类
 */

export const findParent = (menu, id) => {
  for (let i = 0; i < menu.length; i++) {
    if (menu[i].children.length != 0) {
      for (let j = 0; j < menu[i].children.length; j++) {
        if (menu[i].children[j].id == id) {
          return menu[i];
        } else {
          if (menu[i].children[j].children.length != 0) {
            return findParent(menu[i].children[j].children, id);
          }
        }
      }
    }
  }
};
/**
 * 判断2个对象属性和值是否相等
 */

/**
 * 动态插入css
 */

export const loadStyle = (url) => {
  const link = document.createElement("link");
  link.type = "text/css";
  link.rel = "stylesheet";
  link.href = url;
  const head = document.getElementsByTagName("head")[0];
  head.appendChild(link);
};
/**
 * 判断路由是否相等
 */
export const diff = (obj1, obj2) => {
  delete obj1.close;
  var o1 = obj1 instanceof Object;
  var o2 = obj2 instanceof Object;
  if (!o1 || !o2) {
    /*  判断不是对象  */
    return obj1 === obj2;
  }

  if (Object.keys(obj1).length !== Object.keys(obj2).length) {
    return false;
    //Object.keys() 返回一个由对象的自身可枚举属性(key值)组成的数组,例如：数组返回下表：let arr = ["a", "b", "c"];console.log(Object.keys(arr))->0,1,2;
  }

  for (var attr in obj1) {
    var t1 = obj1[attr] instanceof Object;
    var t2 = obj2[attr] instanceof Object;
    if (t1 && t2) {
      return diff(obj1[attr], obj2[attr]);
    } else if (obj1[attr] !== obj2[attr]) {
      return false;
    }
  }
  return true;
};
/**
 * 根据字典的value显示label
 */
export const findByvalue = (dic, value) => {
  let result = "";
  if (validatenull(dic)) return value;
  if (
    typeof value == "string" ||
    typeof value == "number" ||
    typeof value == "boolean"
  ) {
    let index = 0;
    index = findArray(dic, value);
    if (index != -1) {
      result = dic[index].label;
    } else {
      result = value;
    }
  } else if (value instanceof Array) {
    result = [];
    let index = 0;
    value.forEach((ele) => {
      index = findArray(dic, ele);
      if (index != -1) {
        result.push(dic[index].label);
      } else {
        result.push(value);
      }
    });
    result = result.toString();
  }
  return result;
};
/**
 * 根据字典的value查找对应的index
 */
export const findArray = (dic, value) => {
  for (let i = 0; i < dic.length; i++) {
    if (dic[i].value == value) {
      return i;
    }
  }
  return -1;
};
/**
 * 生成随机len位数字
 */
export const randomLenNum = (len, date) => {
  let random = "";
  random = Math.ceil(Math.random() * 100000000000000)
    .toString()
    .substr(0, len ? len : 4);
  if (date) random = random + Date.now();
  return random;
};
/**
 * 打开小窗口
 */
export const openWindow = (url, title, w, h) => {
  // Fixes dual-screen position                            Most browsers       Firefox
  const dualScreenLeft =
    window.screenLeft !== undefined ? window.screenLeft : screen.left;
  const dualScreenTop =
    window.screenTop !== undefined ? window.screenTop : screen.top;

  const width = window.innerWidth
    ? window.innerWidth
    : document.documentElement.clientWidth
      ? document.documentElement.clientWidth
      : screen.width;
  const height = window.innerHeight
    ? window.innerHeight
    : document.documentElement.clientHeight
      ? document.documentElement.clientHeight
      : screen.height;

  const left = width / 2 - w / 2 + dualScreenLeft;
  const top = height / 2 - h / 2 + dualScreenTop;
  const newWindow = window.open(
    url,
    title,
    "toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=yes, copyhistory=no, width=" +
    w +
    ", height=" +
    h +
    ", top=" +
    top +
    ", left=" +
    left
  );

  // Puts focus on the newWindow
  if (window.focus) {
    newWindow.focus();
  }
};

/**
 * 获取顶部地址栏地址
 */
export const getTopUrl = () => {
  return window.location.href.split("/#/")[0];
};

/**
 * 获取url参数
 * @param name 参数名
 */
export const getQueryString = (name) => {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(decodeURI(r[2]));
  return null;
};

/**
 * 下载文件
 * @param {String} path - 文件地址
 * @param {String} name - 文件名,eg: test.png
 */
export const downloadFileBlob = (path, name) => {
  const xhr = new XMLHttpRequest();
  xhr.open("get", path);
  xhr.responseType = "blob";
  xhr.send();
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      // 如果是IE10及以上，不支持download属性，采用msSaveOrOpenBlob方法，但是IE10以下也不支持msSaveOrOpenBlob
      if ("msSaveOrOpenBlob" in navigator) {
        navigator.msSaveOrOpenBlob(this.response, name);
        return;
      }
      const url = URL.createObjectURL(this.response);
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = url;
      a.download = name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };
};

/**
 * 下载文件
 * @param {String} path - 文件地址
 * @param {String} name - 文件名,eg: test.png
 */
export const downloadFileBase64 = (path, name) => {
  const xhr = new XMLHttpRequest();
  xhr.open("get", path);
  xhr.responseType = "blob";
  xhr.send();
  xhr.onload = function () {
    if (this.status === 200 || this.status === 304) {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(this.response);
      fileReader.onload = function () {
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = this.result;
        a.download = name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      };
    }
  };
};

/**
 * 下载excel
 * @param {blob} fileArrayBuffer 文件流
 * @param {String} filename 文件名称
 */
export const downloadXls = (fileArrayBuffer, filename) => {
  let data = new Blob([fileArrayBuffer], {
    type: "application/vnd.ms-excel,charset=utf-8",
  });
  if (typeof window.chrome !== "undefined") {
    // Chrome
    var link = document.createElement("a");
    link.href = window.URL.createObjectURL(data);
    link.download = filename;
    link.click();
  } else if (typeof window.navigator.msSaveBlob !== "undefined") {
    // IE
    var blob = new Blob([data], {type: "application/force-download"});
    window.navigator.msSaveBlob(blob, filename);
  } else {
    // Firefox
    var file = new File([data], filename, {
      type: "application/force-download",
    });
    window.open(URL.createObjectURL(file));
  }
};

/**
 * 补零至指定位数
 * @param {number} num
 * @param {number} len
 * @returns
 */
export const formatZero = (num, len) => {
  if (String(num).length >= len) {
    return num;
  }
  return (Array(len).join(0) + num).slice(-len);
};

// 正整数
export const positiveIntegerReg = /^[1-9]\d*$/;

// 11位手机号
export const phoneReg = /^1\d{10}$/;

// 密码 8-16 数字、字母、下划线
export const psdReg = /^[a-zA-Z0-9_]{8,16}$/;

// 比较日期大小
export const compareDate = (date1, date2) => {
  var oData1 = new Date(date1);
  var oDate2 = new Date(date2);
  return oData1.getTime() <= oDate2.getTime();
};

// 获取当前时间的yyyyMMdd格式字符串
export const getyyyyMMdd = () => {
  let nowDate = new Date();
  let getMonth = nowDate.getMonth();
  let getDate = nowDate.getDate();
  let year = nowDate.getFullYear();
  let month = getMonth + 1 < 10 ? "0" + (getMonth + 1) : getMonth + 1;
  let day = getDate < 10 ? "0" + getDate : getDate;
  return `${year}-${month}-${day}`;
};

/**
 * 比较两个日期之间相差的天数
 * @param {*} timestamp1
 * @param {*} timestamp2
 * @returns 相差天数
 */
export const calcDate = (timestamp1, timestamp2) =>
  (timestamp2 * 1 - timestamp1 * 1) / (1000 * 60 * 60 * 24);

/**
 * 验证字符串str中是否至少包含大写字母、小写字母、数字、特殊字符中的四种
 * @param {*} str
 * @returns
 */
export const testPsd = (str) => {
  const l = /[a-z]/.test(str); //小写
  const u = /[A-Z]/.test(str); //大写
  const n = /[0-9]/.test(str); //数字
  const s = /[\u0020-\u002F\u003A-\u0040\u005B-\u0060\u007B-\u007E]/.test(str); //特殊字符
  return (l || u) && n && s;
};
let newPassword = null
export const validatePsd = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请输入密码"));
  } else if (!testPsd(value.trim())) {
    callback(new Error("密码需包含数字+字母+特殊故字符"));
  } else {
    newPassword = value
    callback();
  }
};
export const validatePsd2 = (rule, value, callback) => {
  if (!testPsd(value.trim())) {
    callback(new Error("密码需包含数字+字母+特殊故字符"));
  } else if (newPassword === '') {
    callback(new Error('请先输入新密码再确认密码！'))
  } else if (value !== newPassword) {
    callback(new Error("两次密码输入不一致！"));
  } else {
    callback();
  }
};

export const getMonthRange = (startDate, endDate) => {
  let payDue = 0
  const date1 = new Date(startDate)
  const date2 = new Date(endDate)
  const month1 = date1.getFullYear() * 12 + date1.getMonth() + 1
  const month2 = date2.getFullYear() * 12 + date2.getMonth() + 1
  const monthRange = month2 - month1 ? month2 - month1 : 0
  const day1 = date1.getDate()
  const day2 = date2.getDate()
  if (!startDate || !endDate) return ''
  if (monthRange) {
    if (day1 > day2) {
      payDue = monthRange - 1
    } else {
      payDue = monthRange
    }
  } else {
    payDue = 0
  }
  return payDue
}

/*
* 小数精度问题
* */
export const numAdd = (arg1, arg2) => {
  let sum = 0;
  let x1, x2, l1, l2;
  if (arg1.toString().includes('.')) {
    x1 = arg1.toString().split('.')[1] * 1
    l1 = arg1.toString().split('.')[1].length
  } else {
    x1 = 0
    l1 = 0
  }
  if (arg2.toString().includes('.')) {
    x2 = arg2.toString().split('.')[1] * 1
    l2 = arg2.toString().split('.')[1].length
  } else {
    x2 = 0
    l2 = 0
  }
  sum += parseInt(arg1)
  sum += parseInt(arg2)
  let x = (x1 * (Math.pow(10, Math.max(l1, l2) - l1)) + x2 * (Math.pow(10, Math.max(l1, l2) - l2))) / Math.pow(10, Math.max(l1, l2))
  sum += parseInt(x.toString());
  if (x.toString().includes('.')) {
    sum += '.' + x.toString().split('.')[1]
  }
  return sum;


  /*
  * let r1, r2, m;
  try {
    r1 = arg1.toString().split('.')[1].length
  } catch (e) {
    r1 = 0
  }
  try {
    r2 = arg2.toString().split('.')[1].length
  } catch (e) {
    r2 = 0
  }
  m = Math.pow(10, Math.max(r1, r2))
  return (arg1 * m + arg2 * m) / m;*/
}
/**
* 处理列表数据用于合并某些行
* @param {*} list
* @returns 处理之后的列表数据
*/
export const dealMergeTableData = (list) =>{
  let count = 1; //用于记录列数
  const records = list.slice()
  records.reverse().map((item, index) => {
      const laterItem = records[index + 1]; // 先拿后一项
      if (laterItem !== undefined) {
        if (laterItem.financeId !== item.financeId) {
          item.count = count; // 记录列数
          count = 1;
        } else {
          count++;
        }
      } else {
        item.count = count;
      }
    });
    return records.reverse()
}
