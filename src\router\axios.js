/**
 * 全站HTTP请求配置
 * 
 * 主要功能：
 * 1. 配置axios默认参数
 * 2. 请求拦截器处理
 * 3. 响应拦截器处理
 * 4. 参数签名处理
 * 
 * 配置说明：
 * - isSerialize: 是否开启form表单提交
 * - isToken: 是否需要token
 * - authorization: 是否需要基础认证
 * - text: 是否为text请求
 */
import axios from "axios";
import store from "@/store/";
import { serialize } from "@/util/util";
import { getToken } from "@/util/auth";
import { Message } from "element-ui";
import { Base64 } from "js-base64";
import NProgress from "nprogress";
import { v4 } from "uuid";
import crypto from "crypto-js";
import "nprogress/nprogress.css";

// 配置axios默认参数
axios.defaults.timeout = 60000; // 设置默认超时时间为60秒
// 配置状态码验证，允许200-500的状态码
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500;
};
// 允许跨域请求携带cookie
axios.defaults.withCredentials = true;
// 配置基础URL
axios.defaults.baseURL = process.env.VUE_APP_API_BASE_URL;  

// 配置NProgress进度条
NProgress.configure({
  showSpinner: false, // 不显示加载圈
});

/**
 * 请求拦截器
 * 主要功能：
 * 1. 添加认证信息
 * 2. 添加token
 * 3. 添加签名信息
 * 4. 处理请求参数
 */
axios.interceptors.request.use(
  (config) => {
    // 开启进度条
    NProgress.start();
    
    // 处理认证信息
    const authorization = config.authorization === false;
    if (!authorization) {
      // 设置基础认证信息
      config.headers["Authorization"] ='Basic c2FiZXI6c2FiZXJfc2VjcmV0'
      // 处理第三方登录的特殊认证
      if(config.params && config.params.grant_type === 'third_login') {
        const clientId = "e0e3471cb822458d8bbb0d11a96b5eee";
        const clientSecret = "72021d91306a4c8f88144a872fe4e8fd";
        config.headers["Authorization"] = `Basic ${Base64.encode(
          `${clientId}:${clientSecret}`
        )}`;
      }
    }

    // 处理token
    const meta = config.meta || {};
    const isToken = meta.isToken === false;
    if (getToken() && !isToken) {
      config.headers["Blade-Auth"] = "bearer " + getToken();
    }

    // 设置Content-Type
    if (config.text === true) {
      config.headers["Content-Type"] = "text/plain";
    }
    config.headers["Content-Type"] = "application/json;charset=UTF-8";

    // 添加签名信息
    const timestamp = Date.now();
    const nonce = v4();
    const param = getParam(config)||"";
    config.headers["timestamp"] = timestamp;
    config.headers["nonce"] = nonce;
    config.headers["signature"] = crypto.SHA1(timestamp + nonce + param);
    config.headers["app"] = 'WEB'; // 平台标识
    config.headers["platform"] = 'ZFPT'; // 平台类型

    // 处理序列化请求
    if (config.method === "post" && meta.isSerialize === true) {
      config.data = serialize(config.data);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器
 * 主要功能：
 * 1. 处理blob响应
 * 2. 处理错误状态码
 * 3. 处理401未授权
 * 4. 统一错误提示
 */
axios.interceptors.response.use(
  (res) => {
    // 关闭进度条
    NProgress.done();
    
    // 处理blob响应
    const { responseType } = res.request;
    if (responseType.toUpperCase() === "BLOB") {
      if (res.headers["content-type"] === "application/json;charset=UTF-8") {
        const reader = new FileReader();
        let parseObj = null;
        reader.readAsText(res.data, "utf-8");
        return new Promise((resolve, reject) => {
          reader.onload = function () {
            parseObj = JSON.parse(reader.result);
            reject();
          };
        });
      } else {
        return res.data;
      }
    } else {
      // 处理状态码
      const status = res.data.code || res.status;
      const statusWhiteList =  [];
      const message = res.data.msg || res.data.error_description || "未知错误";
      
      // 处理白名单状态码
      if (statusWhiteList.includes(status)) return Promise.reject(res);
      
      // 处理401未授权
      if (status === 401)
        store.dispatch("FedLogOut").then(() => {
          let timer = setTimeout(() => {
            clearTimeout(timer)
            store.dispatch('GoLoginPage', '/homepage')
          }, 1000)
        });
      
      // 处理非200状态码
      if (status !== 200) {
        let hiddenMessage = false
        // 特定接口不显示错误消息
        if (res.config.url.indexOf('corReqApply/pubRzReq') > -1 || 
            res.config.url.indexOf('corReqApply/modRzReq') > -1 || 
            res.config.url.indexOf('aliFace/describeFaceVerify') > -1) {
          hiddenMessage = true
        }
        if (!hiddenMessage) {
          Message({
            message: message,
            type: "error",
          });
        }
        return Promise.reject(new Error(message));
      }
      return res;
    }
  },
  (error) => {
    NProgress.done();
    return Promise.reject(new Error(error));
  }
);

/**
 * 获取请求参数
 * @param {Object} config - axios配置对象
 * @returns {String} 排序后的参数字符串
 */
let getParam = function (config) {
  let url = config.url;
  let paramStr = ''
  const params = {};
  
  // 处理URL中的查询参数
  if (url.indexOf("?") > 0) {
    const paramArr = url.slice(url.indexOf("?") + 1).split("&");
    paramArr.map((param) => {
      const [key, val] = param.split("=");
      params[key] = String(val);
    });
  }
  
  // 处理config.params
  if (config.params) {
    let keys = Object.keys(config.params);
    for (let key of keys) {
      if (config.params[key] !== undefined) {
        params[key] = String(config.params[key])
      }
    }
  }
  
  // 处理config.data
  if (config.data) {
    if (config.data instanceof FormData ) {
      config.data.forEach((value, key) => {
        if (!(value instanceof File)) {
          params[key] = value
        }
      })
    } else if (typeof config.data === 'object') {
      let keys = Object.keys(config.data);
      for (let key of keys) {
        if (config.data[key] !== undefined) {
          params[key] = config.data[key]
        }
      }
    }
  }
  
  // 生成排序后的参数字符串
  if (JSON.stringify(params) !== "{}") {
    paramStr = JSON.stringify(params);
    paramStr = sortString(paramStr);
  }
  return paramStr
}

/**
 * 字符串排序函数
 * @param {String} str - 需要排序的字符串
 * @returns {String} 排序后的字符串
 */
function sortString(str) {
  var charArr = str.split('');
  charArr.sort(function(a, b) {
    return a.charCodeAt(0) - b.charCodeAt(0);
  });
  var sortedStr = charArr.join('');
  return sortedStr;
}

export default axios;
