<!-- 
  数智应用组件
  功能：展示数智应用相关数据，包括智能金融顾问和模型应用两个模块
  特点：
  1. 支持模块切换展示
  2. 展示智能金融顾问使用数据
  3. 展示模型应用相关数据
  4. 响应式设计
  5. 美观的渐变效果
-->
<template>
  <div class="szyyClassOne">
    <!-- 头部区域：标题和模块切换按钮 -->
    <div class="headerImg">
      <div>数智应用</div>
      <div class="rightClass">
        <!-- 模型应用按钮 -->
        <div
          :class="[flag ? 'djgdClassOne2' : 'djgdClassOne1']"
          @click="flag = false"
        >
          模型应用
        </div>
        <!-- 智能金融顾问按钮 -->
        <div
          :class="[flag ? 'djgdClassOne1' : 'djgdClassOne2', 'djgdClassOne']"
          @click="flag = true"
        >
          智能金融顾问：蓉融
        </div>
        <div></div>
      </div>
    </div>
    <!-- 主体内容区域 -->
    <div class="bottomClass">
      <!-- 智能金融顾问模块 -->
      <div class="flagOneClass" v-if="flag">
        <!-- 蓉融形象展示 -->
        <div class="xm"><img src="@/assets/dp/xm.png" class="xmClass" /></div>
        <!-- 数据展示区域 -->
        <div class="duihuaClass">
          <!-- 访问用户数 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">访问用户数：</div>
            <div class="fwyhsNumClass">{{ parseInt(rrObj.qaNum) }}</div>
            <div class="dwClass">次</div>
          </div>
          <!-- 功能使用次数 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">功能使用次数：</div>
            <div class="fwyhsNumClass">{{ parseInt(rrObj.sessionNum) }}</div>
            <div class="dwClass">次</div>
          </div>
          <!-- 平均问答次数 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">平均问答次数：</div>
            <div class="fwyhsNumClass">{{ rrObj.userAcccess }}</div>
            <div class="dwClass">次</div>
          </div>
          <!-- 平均单次使用时长 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">平均单次使用时长：</div>
            <div class="fwyhsNumClass">{{ rrObj.useNum }}</div>
            <div class="dwClass">分钟</div>
          </div>
        </div>
      </div>
      <!-- 模型应用模块 -->
      <div class="flagTwoClass" v-if="!flag">
        <!-- 信用融资联合建模产品 -->
        <div class="twoItem">
          <div class="leftClass">
            <img src="@/assets/dp/lk.png" class="leftImgClass" />
          </div>
          <div class="rightFlagClass">
            <div class="rightName">开发信用融资联合建模产品</div>
            <div class="rightNum">
              <div class="oneClass">联合</div>
              <div class="twoClass">{{ listData[0]?.indexValue || "" }}</div>
              <div class="oneClass">家银行，预授信联合建模产品</div>
              <div class="twoClass">{{ listData[1]?.indexValue || "" }}</div>
              <div class="threeClass">款</div>
            </div>
          </div>
        </div>
        <!-- 预授信白名单服务 -->
        <div class="twoItem marginClass">
          <div class="leftClass">
            <img src="@/assets/dp/lvk.png" class="leftImgClass" />
          </div>
          <div class="rightFlagClass2">
            <div class="rightName">创新预授信白名单服务模式</div>
            <div class="rightNum">
              <div class="oneClass">联合</div>
              <div class="twoClass">{{ listData[2]?.indexValue || "" }}</div>
              <div class="oneClass">家银行，首批白名单企业</div>
              <div class="twoClass">{{ listData[3]?.indexValue || "" }}</div>
              <div class="threeClass">万户</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { productUpdateList, queryRealtimeMetricList } from "@/api/article.js";

export default {
  name: "szyy",
  components: {
    vueSeamlessScroll,
  },
  // 组件属性定义
  props: {
    maxDt: {
      type: String,
      required: true,
    },
    isMaxDtReady: {
      type: Boolean,
      required: true,
    },
    //区域
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  // 监听属性变化
  watch: {
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用接口获取数据
          this.getszyy();
          this.getszyy1();
          this.getszyy2();
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.getszyy(newVal);
        this.getszyy1(newVal);
        this.getszyy2(newVal);
      },
    },
  },
  data() {
    return {
      flag: false, // 控制显示模块的标识
      listData: [], // 模型应用数据列表
      // 智能金融顾问数据对象
      rrObj: {
        qaNum: "", // 访问用户数
        sessionNum: "", // 功能使用次数
        userAcccess: "", // 平均问答次数
        useNum: "", // 平均单次使用时长
      },
      RealtimeQuery: "",
      RealtimeAvg: "",
    };
  },
  mounted() {},
  methods: {
    // 获取模型应用数据
    async getszyy() {
      let param = [
        {
          indexName: "开发信用融资联合建模产品银行数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "开发联合建模产品数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "创新预授信白名单模式银行数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "创新预授信白名单模式企业户数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
      ];
      const res = await productUpdateList(param);
      if (res.data.code == 200) {
        this.listData = res?.data?.data || [];
      }
    },
    // 获取智能金融顾问基础数据
    async getszyy1() {
      try {
        let obj = [
          {
            indexName: "访问用户数",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimIndustryChain: "ALL",
            dimNum: 1,
          },
          {
            indexName: "功能使用次数",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimIndustryChain: "ALL",
            dimNum: 1,
          },
        ];
        const res = await productUpdateList(obj);
        if (res.data?.code == 200) {
          const data = res.data.data || [];
          // 安全访问数组元素
          this.rrObj.qaNum = Math.max(0, Number(data[0]?.indexValue || 0));
          this.rrObj.sessionNum = Math.max(0, Number(data[1]?.indexValue || 0));

          // 加强计算逻辑
          const validQaNum = this.rrObj.qaNum > 0 ? this.rrObj.qaNum : 1; // 避免除以0
          const rawValue = this.rrObj.sessionNum / validQaNum;
          this.rrObj.userAcccess = (isNaN(rawValue) ? 0 : rawValue).toFixed(2);
        } else {
          this.resetRrData();
        }
      } catch (error) {
        console.error("获取金融顾问数据失败:", error);
        this.resetRrData();
      }
    },
    // 获取智能金融顾问实时数据
    async getszyy2() {
      try {
        let obj = [
          {
            indexName: "问答次数",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimIndustryChain: "ALL",
            dimNum: 1,
          },
          {
            indexName: "平均单次使用时长",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimIndustryChain: "ALL",
            dimNum: 1,
          },
        ];
        const res = await queryRealtimeMetricList(obj);
        if (res.data?.code == 200) {
          const data = res.data.data || [];
          // 安全访问数据
          const duration = data[1]?.indexValue || data[0]?.indexValue || "0"; // 兼容不同数据结构
          this.rrObj.useNum = parseFloat(duration).toFixed(1);
        } else {
          this.rrObj.useNum = "0.0";
        }
      } catch (error) {
        console.error("获取实时数据失败:", error);
        this.rrObj.useNum = "0.0";
      }
    },
    // 重置金融顾问数据
    resetRrData() {
      this.rrObj = {
        qaNum: 0,
        sessionNum: 0,
        userAcccess: "0.00",
        useNum: "0.0",
      };
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 组件主容器样式 */
.szyyClassOne {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 光效样式 */
  .guangClass {
    width: 220px;
    height: 12px;
    background: radial-gradient(397% 76% at 50% 50%, #97caeb 0%, #048ee6 100%);
    filter: blur(4.338461538461543px);
    position: absolute;
    bottom: 0;
  }

  /* 头部样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    // 添加文字阴影效果
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    /* 右侧按钮组样式 */
    .rightClass {
      display: flex;
      .djgdClassOne {
        position: relative;
        right: -1px;
      }
      // 激活状态按钮样式
      .djgdClassOne1 {
        width: 300px;
        height: 56px;
        background: linear-gradient(
          180deg,
          #052a53 0%,
          #033d7b 63%,
          #0047a8 100%
        );
        border: 1px solid #1790ff;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #ffffff;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
      }
      // 未激活状态按钮样式
      .djgdClassOne2 {
        width: 300px;
        height: 56px;
        background: linear-gradient(
          180deg,
          rgba(5, 38, 83, 0) 0%,
          rgba(12, 74, 139, 0.49) 100%
        );
        box-shadow: inset 0px 0px 14px 0px #168aff;
        border: 2px solid rgba(110, 160, 227, 0.8);
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #67a7ff;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
      }
    }
  }

  /* 底部内容区域样式 */
  .bottomClass {
    width: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-around;
    // 设置渐变背景
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);

    /* 智能金融顾问模块样式 */
    .flagOneClass {
      display: flex;
      align-items: center;
      justify-content: space-around;

      // 蓉融形象样式
      .xm {
        padding-top: 20px;
        .xmClass {
          width: 264px;
          height: 336px;
        }
      }
      // 数据展示区域样式
      .duihuaClass {
        width: 510px;
        height: 336px;
        background-image: url("~@/assets/dp/duihua.png");
        background-repeat: no-repeat;
        background-size: 510px 336px;
        padding: 35px 0 30px 50px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
        // 数据项样式
        .duihuaItem {
          display: flex;
          align-items: flex-end;
          .fwyhsClass {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28px;
            color: #ffffff;
            line-height: 40px;
          }
          .fwyhsNumClass {
            font-family: OPPOSans, OPPOSans;
            font-weight: normal;
            font-size: 36px;
            letter-spacing: 2px;
            color: #00f6ff;
          }
          .dwClass {
            margin-left: 5px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28px;
            color: #ffffff;
          }
        }
      }
    }

    /* 模型应用模块样式 */
    .flagTwoClass {
      padding-left: 85px;
      padding-top: 30px;
      .marginClass {
        margin-top: 40px;
      }
      // 项目样式
      .twoItem {
        display: flex;
        .leftClass {
          margin-right: 30px;
          margin-top: 15px;
          .leftImgClass {
            width: 215px;
            height: 142px;
          }
        }
        // 信用融资联合建模产品样式
        .rightFlagClass {
          .rightName {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            -webkit-text-fill-color: transparent;
            background: linear-gradient(180deg, #ffffff 0%, #85beff 100%);
            background-clip: text;
          }
          .rightNum {
            width: 540px;
            height: 72px;
            background-image: url("~@/assets/dp/lkhf.png");
            background-repeat: no-repeat;
            background-size: 540px 72px;
            margin-top: 24px;
            padding-left: 32px;
            display: flex;
            align-items: center;
            .oneClass {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: #8fc4ff;
            }
            .twoClass {
              font-family: OPPOSans, OPPOSans;
              font-weight: normal;
              font-size: 46px;
              letter-spacing: 2px;
              font-weight: 600;
              color: #fff;
            }
            .threeClass {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: #ffffff;
            }
          }
        }
        // 预授信白名单服务样式
        .rightFlagClass2 {
          .rightName {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            -webkit-text-fill-color: transparent;
            background: linear-gradient(180deg, #ffffff 0%, #85efff 100%);
            background-clip: text;
          }
          .rightNum {
            width: 540px;
            height: 72px;
            background-image: url("~@/assets/dp/lvkhf.png");
            background-repeat: no-repeat;
            background-size: 540px 72px;
            margin-top: 24px;
            padding-left: 32px;
            display: flex;
            align-items: center;
            .oneClass {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: rgba(180, 255, 254, 0.84);
            }
            .twoClass {
              font-family: OPPOSans, OPPOSans;
              font-weight: normal;
              font-size: 46px;
              letter-spacing: 2px;
              color: #fff;
            }
            .threeClass {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
</style>
