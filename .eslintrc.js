module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es6: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  parserOptions: {
    parser: 'babel-eslint',
    sourceType: 'module'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    // 缩进
    'indent': ['error', 2],
    // 引号
    'quotes': ['error', 'single'],
    // 分号
    'semi': ['error', 'never'],
    // 空格
    'space-before-function-paren': ['error', 'never'],
    // 对象属性引号
    'quote-props': ['error', 'as-needed'],
    // 箭头函数参数括号
    'arrow-parens': ['error', 'as-needed'],
    // 对象末尾逗号
    'comma-dangle': ['error', 'never'],
    // 最大行长度
    'max-len': ['error', { 'code': 120 }],
    // 未使用的变量
    'no-unused-vars': ['warn'],
    // 强制使用 === 和 !==
    'eqeqeq': ['error', 'always'],
    // 禁止使用 var
    'no-var': 'error',
    // 优先使用 const
    'prefer-const': 'warn',
    // 禁止使用 alert
    'no-alert': 'error',
    // 禁止使用 eval
    'no-eval': 'error',
    // 禁止使用 with
    'no-with': 'error',
    // 禁止使用 void
    'no-void': 'error',
    // 禁止使用 arguments.caller 或 arguments.callee
    'no-caller': 'error',
    // 禁止使用 new Function
    'no-new-func': 'error',
    // 禁止使用 new Object
    'no-new-object': 'error',
    // 禁止使用 new String
    'no-new-wrappers': 'error',
    // 禁止使用 new Boolean
    'no-new-boolean': 'error',
    // 禁止使用 new Number
    'no-new-number': 'error',
    // 禁止使用 new Array
    'no-array-constructor': 'error',
    // 禁止使用 new RegExp
    'no-regex-spaces': 'error',
    // 禁止使用 new Symbol
    'no-new-symbol': 'error',
    // 禁止使用 new Promise
    'no-new-promise': 'error',
    // 禁止使用 new Date
    'no-new-date': 'error',
    // 禁止使用 new Error
    'no-new-error': 'error',
    // 禁止使用 new Map
    'no-new-map': 'error',
    // 禁止使用 new Set
    'no-new-set': 'error',
    // 禁止使用 new WeakMap
    'no-new-weakmap': 'error',
    // 禁止使用 new WeakSet
    'no-new-weakset': 'error',
    // 禁止使用 new Int8Array
    'no-new-int8array': 'error',
    // 禁止使用 new Uint8Array
    'no-new-uint8array': 'error',
    // 禁止使用 new Uint8ClampedArray
    'no-new-uint8clampedarray': 'error',
    // 禁止使用 new Int16Array
    'no-new-int16array': 'error',
    // 禁止使用 new Uint16Array
    'no-new-uint16array': 'error',
    // 禁止使用 new Int32Array
    'no-new-int32array': 'error',
    // 禁止使用 new Uint32Array
    'no-new-uint32array': 'error',
    // 禁止使用 new Float32Array
    'no-new-float32array': 'error',
    // 禁止使用 new Float64Array
    'no-new-float64array': 'error',
    // 禁止使用 new BigInt64Array
    'no-new-bigint64array': 'error',
    // 禁止使用 new BigUint64Array
    'no-new-biguint64array': 'error',
    // 禁止使用 new ArrayBuffer
    'no-new-arraybuffer': 'error',
    // 禁止使用 new SharedArrayBuffer
    'no-new-sharedarraybuffer': 'error',
    // 禁止使用 new DataView
    'no-new-dataview': 'error',
    // 禁止使用 new Proxy
    'no-new-proxy': 'error',
    // 禁止使用 new Reflect
    'no-new-reflect': 'error',
    // 禁止使用 new Intl
    'no-new-intl': 'error',
    // 禁止使用 new URL
    'no-new-url': 'error',
    // 禁止使用 new URLSearchParams
    'no-new-urlsearchparams': 'error',
    // 禁止使用 new FormData
    'no-new-formdata': 'error',
    // 禁止使用 new Headers
    'no-new-headers': 'error',
    // 禁止使用 new Request
    'no-new-request': 'error',
    // 禁止使用 new Response
    'no-new-response': 'error',
    // 禁止使用 new Blob
    'no-new-blob': 'error',
    // 禁止使用 new File
    'no-new-file': 'error',
    // 禁止使用 new FileReader
    'no-new-filereader': 'error',
    // 禁止使用 new FileList
    'no-new-filelist': 'error',
    // 禁止使用 new FileSystemDirectoryEntry
    'no-new-filesystemdirectoryentry': 'error',
    // 禁止使用 new FileSystemFileEntry
    'no-new-filesystemfileentry': 'error',
    // 禁止使用 new FileSystemEntry
    'no-new-filesystementry': 'error',
    // 禁止使用 new FileSystem
    'no-new-filesystem': 'error',
    // 禁止使用 new FileSystemDirectoryReader
    'no-new-filesystemdirectoryreader': 'error',
    // 禁止使用 new FileSystemDirectoryHandle
    'no-new-filesystemdirectoryhandle': 'error',
    // 禁止使用 new FileSystemFileHandle
    'no-new-filesystemfilehandle': 'error',
    // 禁止使用 new FileSystemHandle
    'no-new-filesystemhandle': 'error',
    // 禁止使用 new FileSystemWritableFileStream
    'no-new-filesystemwritablefilestream': 'error',
    // 禁止使用 new FileSystemSyncAccessHandle
    'no-new-filesystemsyncaccesshandle': 'error',
    // 禁止使用 new FileSystemDirectoryHandle
    'no-new-filesystemdirectoryhandle': 'error',
    // 禁止使用 new FileSystemFileHandle
    'no-new-filesystemfilehandle': 'error',
    // 禁止使用 new FileSystemHandle
    'no-new-filesystemhandle': 'error',
    // 禁止使用 new FileSystemWritableFileStream
    'no-new-filesystemwritablefilestream': 'error',
    // 禁止使用 new FileSystemSyncAccessHandle
    'no-new-filesystemsyncaccesshandle': 'error'
  }
} 