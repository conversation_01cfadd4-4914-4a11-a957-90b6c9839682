<!-- 
  小微企业放款情况组件
  功能：展示小微企业的放款相关数据
  特点：
  1. 展示融资总额和户数
  2. 展示放款金额占比
  3. 展示户均放款金额
  4. 展示户均放款利率
  5. 美观的数据卡片展示
-->
<template>
  <div class="rzjrjgClass3">
    <!-- 头部标题区域 -->
    <div class="headerImg">小微企业放款情况</div>
    <!-- 主体内容区域 -->
    <div class="bottomClass2">
      <!-- 融资总额信息展示 -->
      <div class="dataInfo">
        <div class="totalInfo">
          <span>融资总额：</span>
          <span class="highlight">{{loanStatusList[0]?.indexValue || 0}}</span>
          <span>亿元</span>
          <span class="highlight1">{{loanStatusList[1]?.indexValue || 0}}</span>
          <span>户</span>
        </div>
      </div>
      <!-- 数据卡片标题 -->
      <div class="title">
        <div class="titleOne">放款金额占比</div>
        <div class="titleTwo">户均放款金额</div>
        <div class="titleThree">户均放款利率</div>
      </div>
      <!-- 数据卡片展示区域 -->
      <div class="dataCards">
        <!-- 放款金额占比卡片 -->
        <div
          class="dataCard one"
          :style="{
            backgroundImage: 'url(' + require('../assets/circle1.png') + ')',
            backgroundSize: '100% 100%',
          }"
        >
          <div class="cardValue blue">{{loanStatusList[2]?.indexValue || 0}}%</div>
          <div class="cardBg blue"></div>
        </div>
        <!-- 户均放款金额卡片 -->
        <div
          class="dataCard two"
          :style="{
            backgroundImage: 'url(' + require('../assets/circle2.png') + ')',
            backgroundSize: '100% 100%',
          }"
        >
          <div class="cardValue cyan">{{loanStatusList[3]?.indexValue || 0}}<span>万</span></div>
          <div class="cardBg cyan"></div>
        </div>
        <!-- 户均放款利率卡片 -->
        <div
          class="dataCard three"
          :style="{
            backgroundImage: 'url(' + require('../assets/circle3.png') + ')',
            backgroundSize: '100% 100%',
          }"
        >
          <div class="cardValue green">{{loanStatusList[4]?.indexValue || 0}}%</div>
          <div class="cardBg green"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { productUpdateList } from "@/api/article.js";

export default {
  name: "rzjrjgFour",
  props:{
        selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  data() {
    return {
      loanStatusList: [], // 放款数据列表
    };
  },
  mounted() {
    this.init();
  },
     watch: {
    // isMaxDtReady: {
    //   handler(newVal) {
    //     if (newVal) {
    //       // 只有当 maxDt 准备好后才调用其他接口
    //       this.getrzjrjg();
    //     }
    //   },
    //   immediate: true,
    // },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.init(newVal);
      },
    },
  },
  methods: {
    // 初始化数据
    init() {
      // 获取小微企业放款相关数据
      productUpdateList([{
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: 'ALL',
        // dimCounty: "ALL",
         dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        // dimNum: 0,
          dimNum: this.selectedRegion === "ALL" ? 0 : 2,
        dimIndustryChain: 'ALL',
        indexName: "小微企业放款金额"},{
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: 'ALL',
        // dimCounty: "ALL",
        dimPark: "ALL",
        // dimNum: 0,
        dimIndustryChain: 'ALL',
        indexName: "小微企业放款户数"},
        {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: 'ALL',
        // dimCounty: "ALL",
         dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        // dimNum: 0,
         dimNum: this.selectedRegion === "ALL" ? 0 : 2,
        dimIndustryChain: 'ALL',
          indexName: "小微企业放款金额占比"},{
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: 'ALL',
          // dimCounty: "ALL",
          dimPark: "ALL",
          // dimNum: 0,
          dimIndustryChain: 'ALL',
          indexName: "小微企业户均放款金额",},{
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: 'ALL',
          // dimCounty: "ALL",
          dimPark: "ALL",
          // dimNum: 0,
          dimIndustryChain: 'ALL',
          indexName: "小微企业户均放款利率",}
      ]).then((res) => {
        if (res.data && res.data.data) {
          this.loanStatusList = res.data.data;
        }
      }).catch(error => {
        console.error('获取数据失败:', error);
      });
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 组件主容器样式 */
.rzjrjgClass3 {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部标题样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image: url("~@/assets/dp/headerbg.png");
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    position: relative;
    .icon {
      width: 24px;
      height: 24px;
      background-color: #0066ff;
      border-radius: 4px;
      margin-right: 10px;
      box-shadow: 0 0 10px rgba(0, 102, 255, 0.8);
    }
  }

  /* 底部内容区域样式 */
  .bottomClass2 {
    width: 100%;
    height: 22vh;
    // 设置渐变背景
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
  }

  /* 标题样式 */
  .title {
    padding: 20px 40px;
    display: flex;
    // 放款金额占比标题
    .titleOne {
      width: 256px;
      height: 3.5vh;
      background: rgba(9, 10, 10, 0.17);
      border: 1px solid rgba(0, 114, 255, 0.89);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #ffffff;
      line-height: 3.5vh;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
    }
    // 户均放款金额标题
    .titleTwo {
      width: 256px;
      height: 3.5vh;
      background: rgba(56, 207, 255, 0.17);
      border: 1px solid rgba(0, 198, 255, 0.89);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #ffffff;
      line-height: 3.5vh;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      margin-left: 40px;
    }
    // 户均放款利率标题
    .titleThree {
      width: 256px;
      height: 3.5vh;
      background: rgba(56, 255, 172, 0.17);
      border: 1px solid rgba(0, 255, 225, 0.89);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 26px;
      color: #ffffff;
      line-height: 3.5vh;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      margin-left: 40px;
    }
  }

  /* 数据信息样式 */
  .dataInfo {
    padding: 20px 40px;
    // 总额信息样式
    .totalInfo {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      line-height: 40px;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      // 金额高亮样式
      .highlight {
        font-family: OPPOSans, OPPOSans;
        font-weight: bolder;
        font-size: 44px;
        color: #ffffff;
        letter-spacing: 2px;
        text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
        // 设置文字渐变效果
        background: linear-gradient(180deg, #ffffff 0%, #ffce7c 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      // 户数高亮样式
      .highlight1 {
        margin-left: 20px;
        font-family: OPPOSans, OPPOSans;
        font-weight: bolder;
        font-size: 44px;
        color: #ffffff;
        letter-spacing: 2px;
        text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
        // 设置文字渐变效果
        background: linear-gradient(180deg, #ffffff 0%, #7cebff 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  /* 数据卡片样式 */
  .dataCards {
    display: flex;
    justify-content: space-between;
    padding: 0 40px;
    margin-top: 1vh;

    .dataCard {
      width: 256px;
      height: 8vh;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      .cardTitle {
        font-size: 20px;
        color: #ffffff;
        margin-bottom: 15px;
        z-index: 2;
      }
      // 卡片背景样式
      .one {
        background: url("../assets/circle1.png") no-repeat;
        background-size: 100% 100%;
      }
      .two {
        background: url("../assets/circle2.png") no-repeat;
        background-size: 100% 100%;
      }
      .three {
        background: url("../assets/circle3.png") no-repeat;
        background-size: 100% 100%;
      }
      // 卡片数值样式
      .cardValue {
        font-family: "DIN Alternate", sans-serif;
        font-size: 42px;
        font-weight: bold;
        z-index: 2;
        span {
          font-size: 20px;
        }
        // 蓝色数值样式
        &.blue {
          color: #398fff;
          text-shadow: 0 0 10px rgba(57, 143, 255, 0.8);
          background: linear-gradient(180deg, #ffffff 0%, #50b0fc 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        // 青色数值样式
        &.cyan {
          color: #00ccff;
          text-shadow: 0 0 10px rgba(0, 204, 255, 0.8);
          background: linear-gradient(180deg, #ffffff 0%, #50defc 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        // 绿色数值样式
        &.green {
          color: #00fdda;
          text-shadow: 0 0 10px rgba(0, 253, 218, 0.8);
          background: linear-gradient(180deg, #ffffff 0%, #50fccd 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      // 卡片背景光效
      .cardBg {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 40%;
        z-index: 1;
        opacity: 0.2;

        // 蓝色光效
        &.blue {
          background: radial-gradient(
            ellipse at center bottom,
            #398fff 0%,
            transparent 70%
          );
        }
        // 青色光效
        &.cyan {
          background: radial-gradient(
            ellipse at center bottom,
            #00ccff 0%,
            transparent 70%
          );
        }
        // 绿色光效
        &.green {
          background: radial-gradient(
            ellipse at center bottom,
            #00fdda 0%,
            transparent 70%
          );
        }
      }
    }
  }
}
</style>
