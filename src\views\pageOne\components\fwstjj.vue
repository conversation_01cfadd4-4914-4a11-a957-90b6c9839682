<!--
  服务实体经济组件
  功能：
  1. 展示产业园区和重点产业链的覆盖情况
  2. 支持切换显示产业园区和重点产业链数据
  3. 提供数据详情弹窗展示
  4. 展示放款覆盖的统计信息
-->
<template>
  <div class="fwstjjClass">
    <!-- 头部区域：标题和切换按钮 -->
    <div class="headerImg">
      <div class="lbClass">服务实体经济</div>
      <div class="rightClass">
        <!-- 产业园区切换按钮 -->
        <div
          :class="[flag ? 'djgdClassOne2' : 'djgdClassOne1']"
          @click="serveClick(false)"
        >
          覆盖产业园区
        </div>
        <!-- 重点产业链切换按钮 -->
        <div
          :class="[flag ? 'djgdClassOne1' : 'djgdClassOne2', 'djgdClassOne']"
          @click="serveClick(true)"
        >
          覆盖重点产业链
        </div>
        <div></div>
      </div>
    </div>
    <!-- 底部内容区域：展示统计数据 -->
    <div class="bottomClass">
      <div class="fkfgClass">- 放款覆盖 -</div>

      <div class="centerClass">
        <!-- 产业园区数据卡片 -->
        <div class="itemClass">
          <div class="itemLeft">
            <img src="@/assets/dp/lvsecyyq.png" class="lvsecyyq" />
          </div>
          <div class="itemRight">
            <div class="rightName">产业园区</div>
            <div class="rightNum">
              <div class="numClass">52</div>
              <div class="dw">个</div>
            </div>
          </div>
        </div>
        <!-- 重点产业链数据卡片 -->
        <div class="itemClass">
          <div class="itemLeft">
            <img src="@/assets/dp/lszdcyl.png" class="lvsecyyq" />
          </div>
          <div class="itemRight">
            <div class="rightName">重点产业链</div>
            <div class="rightNum">
              <div class="numClass2">16</div>
              <div class="dw">条</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 重点产业链分布弹窗 -->
    <DialogBar
      :visible="dialogVisibleChain"
      @close="handleDialogClose('chain')"
      title="放款企业重点产业链分布"
      :chartData="chainList"
      :chart-width="1000"
      :chart-height="468"
      :colorStops="colorStopsChain"
    />
    <!-- 产业园区分布弹窗 -->
    <dialog-bar-park
      :visible="dialogVisiblePark"
      :data="parkList"
      :title="'放款企业产业园区分布'"
      :color-stops="colorStopsPark"
      :display-count="12"
      :loading="loading"
      @close="handleDialogClose('park')"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import DialogBar from "../../components/dialogBar.vue";
import DialogBarPark from "../../components/dialogBar-park.vue";

export default {
  name: "fwstjj",
  components: {
    DialogBar,
    DialogBarPark,
  },
  data() {
    return {
      number1: "",
      number2: "",
      dialogVisible: false,
      dialogVisibleChain: false, // 控制重点产业链弹窗显示
      dialogVisiblePark: false, // 控制产业园区弹窗显示
      parkList: [], // 产业园区数据列表
      chainList: [], // 重点产业链数据列表
      // 产业园区图表渐变色配置
      colorStopsPark: [
        { offset: 0, color: "#40BEFD" },
        { offset: 1, color: "#032A47" },
      ],
      // 重点产业链图表渐变色配置
      colorStopsChain: [
        { offset: 0, color: "#FFC941" },
        { offset: 1, color: "#FF892B" },
      ],
      // 重点产业链列表
      paramsList: [
        "工业互联网",
        "轨道交通",
        "人工智能与机器人（含车载智能控制系统）",
        "生态环保",
        "新材料",
        "新型显示",
        "低空经济",
        "卫星互联网与卫星应用",
        "集成电路",
        "智能终端",
        "汽车（新能源汽车）",
        "高端软件与操作系统",
        "大飞机制造与服务",
        "文创业（含数字文创）",
        "新能源",
        "工业无人机",
        "航空发动机",
        "高端医疗器械",
        "氢能",
      ],
      // 产业园区列表
      parkList1: [
        { name: "温江国家农业科技园区", amount: "3.8", count: 4562 },
        { name: "国宾都市文旅产业园", amount: "2.9", count: 3289 },
        { name: "四川成都新津经济开发区", amount: "2.3", count: 2756 },
        { name: "荷花池商务商贸产业园", amount: "1.8", count: 2156 },
        { name: "四川成都成华经济开发区", amount: "1.5", count: 1892 },
        { name: "成都—阿坝工业园区", amount: "1.2", count: 1568 },
        { name: "四川天府新兴经济开发区（拟筹）", amount: "0.9", count: 1234 },
        { name: "华西转化医学产业园", amount: "0.8", count: 1123 },
        { name: "四川成都武侯经济开发区", amount: "0.7", count: 987 },
        { name: "四川成都锦江经济开发区", amount: "0.6", count: 856 },
        { name: "成都国际铁路港经济技术开发区", amount: "0.5", count: 765 },
        { name: "四川金堂经济开发区", amount: "0.4", count: 654 },
        { name: "天府果荟国家现代农业产业园", amount: "0.3", count: 543 },
        { name: "成都音乐文创园", amount: "0.2", count: 432 },
        { name: "四川邛崃经济开发区", amount: "0.1", count: 321 },
        { name: "四川成都郫都高新技术产业园区", amount: "3.8", count: 4562 },
        { name: "四川彭州经济开发区", amount: "2.9", count: 3289 },
        { name: "城北（香城）新消费活力区", amount: "2.3", count: 2756 },
        {
          name: "四川天府国际空港经济开发区（在筹）",
          amount: "1.8",
          count: 2156,
        },
        { name: "成都高新技术产业开发区", amount: "1.5", count: 1892 },
        { name: "成都新材料产业化工园区", amount: "1.2", count: 1568 },
        { name: "天府中央商务区", amount: "0.9", count: 1234 },
        { name: "成都经济技术开发区", amount: "0.8", count: 1123 },
        { name: "四川成都温江高新技术产业园区", amount: "0.7", count: 987 },
        { name: "四川都江堰经济开发区", amount: "0.6", count: 856 },
        { name: "成都熊猫国际旅游度假区", amount: "0.5", count: 765 },
        { name: "四川成都新都高新技术产业园区", amount: "0.4", count: 654 },
        { name: "少城国际文创谷", amount: "0.3", count: 543 },
        { name: "四川崇州经济开发区", amount: "0.2", count: 432 },
        { name: "简阳临空经济产业园", amount: "0.1", count: 321 },
        { name: "四川成都金牛高新技术产业园区", amount: "3.8", count: 4562 },
        { name: "天府数字农旅产业园", amount: "2.9", count: 3289 },
        { name: "四川成都双流经济开发区", amount: "2.3", count: 2756 },
        { name: "都江堰现代文旅融合园区", amount: "1.8", count: 2156 },
        { name: "四川成都青羊经济开发区", amount: "1.5", count: 1892 },
        { name: "东郊记忆艺术区", amount: "1.2", count: 1568 },
        { name: "成都影视城", amount: "0.9", count: 1234 },
        { name: "街子古镇群旅游度假区", amount: "0.8", count: 1123 },
        { name: "四川简阳经济开发区", amount: "0.7", count: 987 },
        { name: "四川蒲江经济开发区", amount: "0.6", count: 856 },
        { name: "四川大邑经济开发区", amount: "0.5", count: 765 },
        { name: "龙门山旅游度假区", amount: "0.4", count: 654 },
        { name: "天府蔬香农业产业园", amount: "0.3", count: 543 },
        { name: "中国天府农业博览园", amount: "0.2", count: 432 },
        { name: "天府现代种业园", amount: "0.1", count: 321 },
        { name: "西岭冰雪·安仁文博国际文化旅游区", amount: "0.1", count: 321 },
        { name: "天府菌都农业产业园", amount: "0.1", count: 321 },
        { name: "天府粮仓国家现代农业产业园", amount: "0.1", count: 321 },
        { name: "天府奥体公园", amount: "0.1", count: 321 },
      ],
      flag: false, // 控制切换按钮状态
      loading: true, // 加载状态
    };
  },
  props: {
    maxDt: {
      type: String,
      required: true,
    },
    //区域
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  watch: {
    // 监听最大日期准备状态
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用其他接口
          this.getfwstjj();
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.initData(newVal);
        this.getParkList(newVal);
      },
    },
  },
  mounted() {
    //1秒后调用
    setTimeout(() => {
      this.initData();
      this.getParkList();
    }, 500);
  },
  beforeDestroy() {
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    // 切换显示产业园区/重点产业链
    serveClick(isChain) {
      this.flag = isChain;
      if (isChain) {
        this.dialogVisibleChain = true;
        this.dialogVisiblePark = false;
      } else {
        this.loading = true;
        this.dialogVisiblePark = true;
        this.dialogVisibleChain = false;
        // 如果已经有数据，立即关闭loading
        if (this.parkList && this.parkList.length > 0) {
          this.loading = false;
        }
      }
    },
    // 处理弹窗关闭事件
    handleDialogClose(type) {
      if (type === "chain") {
        this.dialogVisibleChain = false;
      } else if (type === "park") {
        this.dialogVisiblePark = false;
      }
    },
    // 打开更多详情弹窗
    openMoreDialog() {
      this.dialogVisible = true;
    },
    // 初始化重点产业链数据
    async initData() {
      let obj = this.paramsList.map((item) => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimNum: 4,
        dimIndustryChain: item,
        indexName: "放款企业重点产业链金额",
      }));

      let obj2 = this.paramsList.map((item) => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimNum: 4,
        dimIndustryChain: item,
        indexName: "放款企业重点产业链笔数",
      }));

      let obj3 = obj.concat(obj2);
      // console.log("obj3", obj3);
      try {
        const res = await productUpdateList(obj3);
        let list = res.data.data;
        let list1 = list.filter(
          (item) => item.indexName === "放款企业重点产业链金额"
        );
        let list2 = list.filter(
          (item) => item.indexName === "放款企业重点产业链笔数"
        );

        list1.map((item, index) => {
          item.value = item.indexValue - 0;
          item.name = this.paramsList[index];
          item.count =
            list2.find((d) => d.dimIndustryChain === item.dimIndustryChain)
              .indexValue - 0;
        });

        this.chainList = list1;
        this.chainList.sort((a, b) => a.value - b.value);

        await this.$nextTick();
      } catch (error) {
        console.error("获取数据失败:", error);
      }
    },
    // 获取产业园区列表数据
    async getParkList() {
      this.loading = true;
      try {
        const selectedRegion = this.selectedRegion || "ALL";
        const cacheKey = `parkList_${selectedRegion}`;

        const cachedData = localStorage.getItem(cacheKey);
        if (cachedData) {
          this.parkList = JSON.parse(cachedData);
          this.loading = false;
          return;
        }

        const obj = this.parkList1.map((item) => ({
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: selectedRegion,
          dimPark: item.name, // 确保此处与后端字段一致
          dimNum: 3,
          dimIndustryChain: "ALL",
          indexName: "放款企业产业园区金额",
        }));

        const obj2 = this.parkList1.map((item) => ({
          ...obj[0],
          indexName: "放款企业产业园区笔数",
        }));

        const res = await productUpdateList([...obj, ...obj2]);

        const amountArr = res.data.data
          .filter((item) => item.indexName === "放款企业产业园区金额")
          .map((item) => ({
            ...item,
            amount: Number(item.indexValue),
            name: item.dimPark,
          }));

        const countArr = res.data.data
          .filter((item) => item.indexName === "放款企业产业园区笔数")
          .map((item) => ({
            ...item,
            count: Number(item.indexValue),
            name: item.dimPark,
          }));

        const mergedData = amountArr.map((item) => {
          const countItem = countArr.find((c) => c.name === item.name);
          return {
            ...item,
            count: countItem ? countItem.count : 0,
          };
        });

        this.parkList = mergedData.sort((a, b) => b.amount - a.amount);
        localStorage.setItem(cacheKey, JSON.stringify(this.parkList));
      } catch (error) {
        console.error("数据获取失败:", error);
      } finally {
        this.loading = false;
      }
    },
    async getParkList() {
      this.loading = true;
      try {
        // 先尝试从localStorage获取数据
        const cachedData = localStorage.getItem("parkList");
        if (cachedData) {
          this.parkList = JSON.parse(cachedData);
          this.loading = false;
          return;
        }

        let obj = this.parkList1.map((item) => ({
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: item.name,
          dimNum: 3,
          dimIndustryChain: "ALL",
          indexName: "放款企业产业园区金额",
        }));
        let obj2 = this.parkList1.map((item) => ({
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: item.name,
          dimNum: 3,
          dimIndustryChain: "ALL",
          indexName: "放款企业产业园区笔数",
        }));
        let obj3 = obj.concat(obj2);

        const res = await productUpdateList(obj3);
        console.log("值", res);
        const amountArr = res.data.data
          .filter((item) => item.indexName === "放款企业产业园区金额")
          .map((item) => ({
            ...item,
            value: item.indexValue - 0,
            amount: item.indexValue - 0,
            name: item.dimPark,
            indexName: item.indexName,
          }));

        const countArr = res.data.data
          .filter((item) => item.indexName === "放款企业产业园区笔数")
          .map((item) => ({
            ...item,
            value: item.indexValue - 0,
            name: item.dimPark,
            indexName: item.indexName,
          }));

        const mergedData = amountArr.map((item) => {
          const countItem = countArr.find((count) => count.name === item.name);
          return {
            ...item,
            count: countItem ? countItem.value : 0,
          };
        });

        this.parkList = mergedData;
        this.parkList.sort((a, b) => b.amount - a.amount);
        localStorage.setItem("parkList", JSON.stringify(this.parkList));
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        this.loading = false;
      }
    },
    // 获取重点产业链列表数据
    async getChainList() {
      let obj = this.paramsList.map((item) => ({
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimNum: 3,
        dimIndustryChain: item,
        indexName: "放款企业重点产业链金额",
      }));
      let obj2 = this.paramsList.map((item) => ({
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimNum: 3,
        dimIndustryChain: item,
        indexName: "放款企业重点产业链笔数",
      }));
      let obj3 = obj.concat(obj2);

      const res = await productUpdateList(obj3);
      if (res.data.code === 200) {
        // 过滤出金额数据
        const amountArr = res.data.data
          .filter((item) => item.indexName === "放款企业重点产业链金额")
          .map((item) => ({
            ...item,
            value: item.indexValue - 0,
            amount: item.indexValue - 0,
            name: item.dimIndustryChain,
            indexName: item.indexName,
          }));

        // 过滤出笔数数据
        const countArr = res.data.data
          .filter((item) => item.indexName === "放款企业重点产业链笔数")
          .map((item) => ({
            ...item,
            value: item.indexValue - 0,
            name: item.dimIndustryChain,
            indexName: item.indexName,
          }));

        // 合并金额和笔数数据
        const mergedData = amountArr.map((item) => {
          const countItem = countArr.find((count) => count.name === item.name);
          return {
            ...item,
            count: countItem ? countItem.value : 0,
          };
        });

        this.chainList = mergedData.sort((a, b) => b.amount - a.amount);
      }
    },
  },
};
</script>
<style scope="scoped" lang="scss">
.fwstjjClass {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .lbClass {
      height: 80px;
      line-height: 80px;
    }
    .rightClass {
      display: flex;
      .djgdClassOne {
        position: relative;
        right: -1px;
      }
      .djgdClassOne1 {
        width: 300px;
        height: 56px;
        background: linear-gradient(
          180deg,
          #052a53 0%,
          #033d7b 63%,
          #0047a8 100%
        );
        border: 1px solid #1790ff;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #ffffff;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
      }
      .djgdClassOne2 {
        width: 250px;
        height: 56px;
        background: linear-gradient(
          180deg,
          rgba(5, 38, 83, 0) 0%,
          rgba(12, 74, 139, 0.49) 100%
        );
        box-shadow: inset 0px 0px 14px 0px #168aff;
        border: 2px solid rgba(110, 160, 227, 0.8);
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #67a7ff;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
      }
    }
  }
  .bottomClass {
    width: 100%;
    height: calc(27vh - 80px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    .fkfgClass {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 36px;
      color: #77ceff;
      letter-spacing: 1px;
      text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
      text-align: center;
      padding-top: 8px;
    }
    .centerClass {
      margin-top: -100px !important;
      display: flex;
      justify-content: space-between;
      width: 940px;
      .itemClass {
        width: 50%;
        text-align: center;
        display: flex;
        justify-content: center;
        height: 228px;
        .itemLeft {
          .lvsecyyq {
            width: 224px;
            height: 228px;
          }
        }
        .itemRight {
          height: 208px;
          flex-direction: column;
          display: flex;
          align-items: left;
          justify-content: center;
          .rightName {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28px;
            color: #ffffff;
          }
          .rightNum {
            display: flex;
            align-items: baseline;
            .numClass {
              font-family: YouSheBiaoTiHei;
              font-size: 63px;
              -webkit-text-fill-color: transparent;
              letter-spacing: 3px;
              background: linear-gradient(180deg, #ffffff 0%, #2cfce9 100%);
              background-clip: text;
            }
            .numClass2 {
              font-family: YouSheBiaoTiHei;
              font-size: 63px;
              color: #ffffff;
              -webkit-text-fill-color: transparent;
              letter-spacing: 3px;
              background: linear-gradient(180deg, #ffffff 0%, #50b0fc 100%);
              background-clip: text;
            }
            .dw {
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 29px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}
</style>
