<!--
  放款金额担保构成饼图弹窗组件
  功能：
  1. 展示不同担保类型的放款金额占比
  2. 提供饼图可视化展示
  3. 显示每种担保类型的金额和笔数
  4. 支持图表交互和提示
-->
<template>
  <div v-if="visible" class="dialog-mask" @click="handleMaskClick">
    <!-- 弹窗内容区域 -->
    <div class="dialog-content" @click.stop>
      <!-- 头部区域 -->
      <div class="header-box">
        <!-- 标题区域 -->
        <div class="dialog-header">
          <div class="titleBox">
            <span class="title">放款金额担保构成</span>
          </div>
        </div>
        <!-- 标签组区域 -->
        <div class="tab-group"></div>
      </div>
      <!-- 图表主体区域 -->
      <div class="dialog-body">
        <div ref="chartRef" class="chart"></div>
      </div>
      <!-- 底部插槽区域 -->
      <div class="dialog-footer">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdate } from "@/api/article.js";

export default {
  name: "EchartsDialogLoop",
  props: {
    // 控制弹窗显示
    visible: { type: Boolean, default: false },
    // 弹窗标题
    title: { type: String, default: "循环弹框" },
    // 图表数据
    chartData: {
      type: Object,
      default: () => {
        return {
          xAxisData: [],
          seriesData: [],
        };
      },
    },
  },
  data() {
    return {
      maxDt: localStorage.getItem("maxDt"), // 最大日期
      chart: null, // ECharts实例
      chartList: [], // 图表数据列表
      // 饼图数据配置
      pieData: [
        { value: 0, name: "信用", amount: 0, count: 0, itemStyle: { color: "#3A8BFF", fontSize: this.$autoFontSize(15) } },
        { value: 0, name: "保证", amount: 0, count: 0, itemStyle: { color: "#1DE6D7", fontSize: this.$autoFontSize(15) } },
        { value: 0, name: "质押", amount: 0, count: 0, itemStyle: { color: "#1DE65D", fontSize: this.$autoFontSize(15) } },
        { value: 0, name: "抵押", amount: 0, count: 0, itemStyle: { color: "#FFB800", fontSize: this.$autoFontSize(15) } },
        { value: 0, name: "", amount: 0, count: 0, itemStyle: { color: "#FF6B81", fontSize: this.$autoFontSize(15) } }
      ],
    };
  },

  methods: {
    // 初始化数据
    async init() {
      // 获取放款金额数据
      const res = await productUpdate({
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 0,
        dimIndustryChain: "ALL",
        indexName: "累计放款金额",
      });
      
      // 处理返回的数据
      if (res?.data?.data?.[0]?.bizContent) {
        try {
          this.chartList = JSON.parse(res.data.data[0].bizContent);
          // 更新饼图数据
          for (let i = 0; i < this.chartList.length; i++) {
            if (this.chartList[i]) {
              // 提取金额和笔数
              const amount = this.chartList[i].atmSum.match(/\d+\.\d+/)[0] - 0;
              const count = this.chartList[i].atmNum.match(/\d+/)[0] - 0;
              // 更新饼图数据项
              this.pieData[i].name = this.chartList[i].guaType;
              this.pieData[i].amount = this.chartList[i].atmSum.match(/\d+\.\d+/)[0] - 0;
              this.pieData[i].count = count;
              this.pieData[i].value = this.chartList[i].atmSum.match(/\d+\.\d+/)[0] - 0;
              this.initChart();
            }
          }
        } catch (error) {
          console.error('Error parsing chart data:', error);
        }
      } else {
        console.warn('No chart data received from API');
      }
    },

    // 关闭弹窗
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
    },

    // 处理遮罩层点击
    handleMaskClick() {
      this.close();
    },

    // 初始化图表
    initChart() {
      if (!this.$refs.chartRef) return;
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef);
      }

      // 生成富文本配置
      const rich = {
        name: { color: '#fff', fontSize: this.$autoFontSize(15) },
        unit: { color: '#fff', fontSize: this.$autoFontSize(15) }
      };
      // 为每个数据项生成富文本样式
      this.pieData.forEach((item, idx) => {
        rich['amount' + idx] = { color: item.itemStyle.color, fontSize: this.$autoFontSize(15) };
        rich['count' + idx] = { color: item.itemStyle.color, fontSize: this.$autoFontSize(15) };
      });

      // 图表配置项
      const option = {
        // 动画配置
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicInOut',
        animationThreshold: 2000,
        // 提示框配置
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `
              ${params.name}<br/>
              <span style="color:${params.color}">${params.data.amount}亿</span>
              <span style="color:${params.color}">${params.data.count}笔</span><br/>
              占比: ${params.percent}%
            `;
          }
        },
        // 图例配置
        legend: {
          orient: 'vertical',
          right: '10%',
          top: 'center',
          itemWidth: 24,
          itemHeight: 24,
          itemGap: 24,
          textStyle: {
            fontSize: this.$autoFontSize(14),
            rich: rich
          },
          // 自定义图例文本格式
          formatter: (name) => {
            const idx = this.pieData.findIndex(i => i.name === name);
            const item = this.pieData[idx];
            if (item) {
              return `{name|${name}}  {amount${idx}|${item.amount}}{unit|亿} {count${idx}|${item.count}}{unit|笔}`;
            }
            return name;
          }
        },
        // 系列配置
        series: [
          {
            type: 'pie',
            radius: ['38%', '60%'], // 环形图内外半径
            center: ['30%', '50%'], // 图表中心位置
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            // 高亮配置
            emphasis: {
              scale: true,
              scaleSize: 5,
              label: {
                show: true,
                fontSize: 18,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            // 动画配置
            animationType: 'expansion',
            animationTypeUpdate: 'transition',
            animationDelay: function (idx) {
              return idx * 100;
            },
            data: this.pieData
          }
        ]
      };
      this.chart.setOption(option);
      this.chart.resize(); // 自适应容器大小
    },
  },
  mounted() {
    if (this.visible) {
      this.init();
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
};
</script>

<style scoped lang="scss">
/* 遮罩层 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 50%;
  right: 0;
  bottom: 0;
  background: rgba(0, 24, 48, 0.1);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 标题区域 */
.titleBox {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  margin-top: 18px;
  .title {
    padding-left: 50px;
    width: 314px;
    height: 89px;
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    margin-left: 50px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
  }
}

/* 弹窗内容区域 */
.dialog-content {
  background: url("~@/assets/dp/dialog.png");
  background-size: 100% 100%;
  border-radius: 12px;
  min-width: 1600px;
  min-height: 900px;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
}

/* 头部区域 */
.header-box {
  display: flex;
}

/* 弹窗头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 标签组 */
.tab-group {
  display: flex;
  margin-left: 300px;
  margin-right: 30px;
  margin-top: 30px;
  height: 36px;
  position: relative;
}

/* 标签按钮 */
.tab-btn {
  font-family: PingFangSC;
  font-weight: 400;
  font-size: 34px;
  line-height: 47px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  width: 140px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  color: #67a7ff;
  border: 1px solid #4ea6ff;
  cursor: pointer;
  margin-left: -1px;
  transition: background 0.2s, color 0.2s;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  );
  box-shadow: inset 0px 0px 18px 0px #168aff;
  border: 2px solid rgba(110, 160, 227, 0.8);
}

/* 激活状态的标签按钮 */
.tab-btn.active {
  background: #2176c7;
  color: #fff;
  border: 1px solid #2176c7;
  z-index: 1;
}

/* 关闭按钮 */
.dialog-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 60px;
  position: absolute;
  top: -100px;
  right: 0;
  padding: 0;
  cursor: pointer;
}

/* 图表主体区域 */
.dialog-body {
  padding: 10px 24px 0 24px;
}

/* 图表容器 */
.chart {
  width: 100%;
  height: 800px;
}

/* 底部区域 */
.dialog-footer {
  padding: 10px 24px 18px 24px;
  text-align: right;
}
</style>
