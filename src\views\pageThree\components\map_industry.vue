<template>
  <div class="mapClass3">
    <div ref="chartRef" style="width: 100%; height: 100%" v-if="flag"></div>
    <div ref="chartRef2" style="width: 100%; height: 100%" v-else></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import geoJsonData from "../../pageOne/img/cds.json";
export default {
  name: "map_base",
  data() {
    return {
      twoMap: null,
      flag: true,
      rotationAngle: 10,
      cdList: [],
      chartInstance: null,
      chartInstance2: null,
      mapName: "",
      cityName: "",
      districtMapDot: {
        点1: [104.45, 30.55], // 西北
        点2: [104.5, 30.35], // 西南
        点3: [104.6, 30.6], // 北部
        点4: [104.75, 30.5], // 东北
        点5: [104.8, 30.4], // 东部
        点6: [104.7, 30.3], // 东南
        点7: [104.55, 30.25], // 南部
        点8: [104.65, 30.45], // 中部偏东
      }, //区的点坐标集合
      centerDot: [104.547, 30.413], // 简阳市中心点
      showCustomTooltip: false,
      tooltipContent: "",
      tooltipPosition: { x: 0, y: 0 },
      randomData: {},
      showShuangliuTooltip: false,
      dimCounty: "锦江区",
      shuangliuTooltipData: {
        name: "锦江区",
        dimCounty: "锦江区",
        enterprises: 7883,
        loanAmount: 20.51,
        loanHouseholds: 924,
        averageRate: 4.31,
      },
    };
  },
  mounted() {
    this.initChart();
    this.getmap();
    // 初始化时显示锦江区的数据
    setTimeout(() => {
      if (this.chartInstance) {
        this.chartInstance.dispatchAction({
          type: "highlight",
          seriesIndex: 0,
          name: "锦江区",
        });
        this.chartInstance.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          name: "锦江区",
        });
      }
    }, 500);
  },
  methods: {
    async getmap() {
      let obj = [
        {
          indexName: "放款行业TOP5放款金额",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 2,
        },
        {
          indexName: "放款行业TOP5放款笔数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 2,
        },
        {
          indexName: "放款行业TOP5平均放款利率",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 2,
        },
      ];
      await productUpdateList(obj).then((res) => {
        this.cdList = res.data.data;
        // 数据加载完成后，显示锦江区数据
        this.$nextTick(() => {
          if (this.chartInstance) {
            this.chartInstance.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              name: "锦江区",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: "锦江区",
            });
          }
        });
      });
    },
    // 添加显示双流区弹框的方法
    showShuangliuPopup() {
      this.showShuangliuTooltip = true;
    },

    // 隐藏双流区弹框的方法
    hideShuangliuPopup() {
      this.showShuangliuTooltip = false;
    },

    showJinjiangTooltip(params) {
      // 这里直接调用 tooltip 的 formatter 逻辑
      const districtData = this.getDistrictData(params.name);
      // 通过 ECharts 的 dispatchAction 显示 tooltip
      if (this.chartInstance) {
        this.chartInstance.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          name: params.name,
        });
      }
    },
    backToChengdu() {
      this.flag = true;
      this.twoMap = null;
      // 这里会自动触发watch，重新加载成都市地图
    },
    generateRandomNumber(min = 5000, max = 8000) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    // 获取区域数据
    getDistrictData(districtName) {
      // 如果该区域没有数据，则生成随机数据并缓存
      if (!this.randomData[districtName]) {
        this.randomData[districtName] = {
          enterprises: this.generateRandomNumber(5000, 8000),
          loanAmount: (Math.random() * 20 + 5).toFixed(2),
          loanHouseholds: this.generateRandomNumber(800, 1500),
          averageRate: (Math.random() * 2 + 3).toFixed(2),
        };
      }
      return this.randomData[districtName];
    },
    async initChart() {
      if (!this.$refs.chartRef) return;
      this.chartInstance = null;
      if (this.chartInstance) {
        this.chartInstance.dispose();
        this.chartInstance = null;
      }
      if (this.chartInstance2) {
        this.chartInstance2.dispose();
        this.chartInstance2 = null;
      }
      try {
        // 注册地图
        echarts.registerMap(this.mapName, geoJsonData);
        // 初始化 ECharts 实例
        this.chartInstance = echarts.init(this.$refs.chartRef);
        // 配置地图
        // 生成所有飞线和点数据
        // 需要在地图上显示的点
        const scatterData = [];
        const option = {
          geo: [
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false, // 是否允许缩放
              zoom: 1.2, // 默认显示级别
              layoutSize: "95%",
              layoutCenter: ["50%", "50%"],
              selectedMode: true,
              label: {
                show: true,
                color: "#fff",
                fontSize: this.$autoFontSize(12),
                fontWeight: "bold",
                position: "right",
                offset: [0, 0],
                verticalAlign: "middle",
                align: "right",
              },
              itemStyle: {
                areaColor: {
                  type: "linear-gradient",
                  x: 0,
                  y: 200,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(37,108,190,0.3)", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(15,169,195,0.3)", // 50% 处的颜色
                    },
                  ],
                  global: true, // 缺省为 false
                },
                borderColor: "#4ecee6",
                borderWidth: 1,
              },
              emphasis: {
                itemStyle: {
                  areaColor: {
                    type: "linear-gradient",
                    x: 0,
                    y: 300,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: "rgba(37,108,190,0.8)",
                      },
                      {
                        offset: 1,
                        color: "rgba(15,169,195,0.8)",
                      },
                    ],
                    global: true,
                  },
                  borderWidth: 2,
                  shadowBlur: 20,
                  shadowColor: "rgba(0,218,255,0.5)",
                },
                label: {
                  show: true,
                  color: "#fff",
                  fontSize: this.$autoFontSize(15),
                  fontWeight: "bold",
                },
              },
              zlevel: 3,
            },
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false, // 是否允许缩放
              zoom: 1.2, // 默认显示级别
              layoutSize: "95%",
              layoutCenter: ["50%", "50%"],
              itemStyle: {
                borderColor: "rgba(192,245,249,.6)",
                borderWidth: 2,
                shadowColor: "#2C99F6",
                shadowOffsetY: 0,
                shadowBlur: 120,
                areaColor: "rgba(29,85,139,.2)",
              },
              zlevel: 2,
              silent: true,
            },
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false, // 是否允许缩放
              zoom: 1.2, // 默认显示级别
              layoutSize: "95%",
              layoutCenter: ["50%", "51.5%"],
              itemStyle: {
                // areaColor: '#005DDC',
                areaColor: "rgba(0,27,95,0.4)",
                borderColor: "#004db5",
                borderWidth: 1,
              },
              zlevel: 1,
              silent: true,
            },
          ],
          tooltip: {
            show: true,
            trigger: "item",
            formatter: (params) => {
              const city = params.name.trim() || "锦江区";

              // 获取该城市的数据
              let cityData = this.cdList.find(
                (item) => item.dimCounty === city
              );
              if (!cityData) {
                return `
                  <div class="tkClass">
                    <div class="tkName">${city}</div>
                    <div class="tkBody" style="padding: 10px 0 10px 20px;">
                      <div style="color:#fff;">暂无数据</div>
                    </div>
                  </div>
                `;
              }

              try {
                let banks = JSON.parse(cityData.bizContent);
                if (!Array.isArray(banks)) {
                  throw new Error("数据格式错误");
                }

                let html = `
                  <div class="tkClass2">
                    <div class="tkName">${city}</div>
                    <div class="tkBody" style="padding: 10px 0 10px 20px;">
                `;
                banks.forEach((bank) => {
                  html += `
                    <div style="display:flex;"class="tkItemClass">
                     <div   style='margin-top:10px' class="cir"></div>
                      <div style="color:#fff;font-size:${this.$autoFontSize(
                        14
                      )}px;margin-left:${this.$autoFontSize(14)}px;">${
                    bank.industry
                  }：</div>
                      <div style="color:#fff;font-size:${this.$autoFontSize(
                        14
                      )}px;" class="cirRight">${bank.loan}亿元</div>
                    </div>
                  `;
                });
                html += `</div></div>`;
                return html;
              } catch (error) {
                console.error("解析数据失败:", error);
                return `
                  <div class="tkClass">
                    <div class="tkName">${city}</div>
                    <div class="tkBody" style="padding: 10px 0 10px 20px;">
                      <div style="color:#fff;">数据格式错误</div>
                    </div>
                  </div>
                `;
              }
            },
            backgroundColor: "rgba(0,0,0,0.7)",
            borderColor: "#fff",
            borderWidth: 1,
            padding: 10,
            marginTop: -30,
          },
          series: [
            {
              type: "map",
              map: this.mapName,
              geoIndex: 0,
              roam: false,
              selectedMode: false,
              itemStyle: {
                borderColor: "#2ab8ff",
                borderWidth: 1.5,
                areaColor: "#12235c",
                fontSize: 10,
                fontWeight: 600,
              },
              emphasis: {
                itemStyle: {
                  areaColor: "rgba(224, 247, 255, 0.1)",
                  borderWidth: 4,
                  shadowBlur: 30,
                  fontWeight: 600,
                },
                label: {
                  show: true,
                  fontSize: 12,
                  fontWeight: "bold",
                  color: "#fff",
                },
              },
              label: {
                show: true,
                fontSize: 10,
                formatter: (params) => {
                  if (params.name === "崇州市") {
                    return "{dot|•} {text|" + params.name + "}";
                  }
                  if (params.name === "锦江区") {
                    return "{text|" + params.name + "}";
                  }
                  return params.name;
                },
                rich: {
                  dot: {
                    color: "#fff",
                    fontSize: 20,
                    padding: [0, 0, 0, 0],
                  },
                  text: {
                    color: "#fff",
                    fontSize: 12,
                    padding: [0, 0, 0, 0],
                  },
                },
              },
              labelLayout: {
                hideOverlap: true,
                draggable: true,
                x: (params) => {
                  if (params.name === "崇州市") {
                    return params.x + 40;
                  }
                  return params.x;
                },
              },
            },
          ],
        };
        // 定义区县映射
        let lastHoveredArea = "锦江区";
        this.chartInstance.getZr().on("globalout", () => {
          this.dimCounty = lastHoveredArea;
          // 先取消所有高亮
          this.chartInstance.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
          });
          // 高亮最后一次悬停的区域
          this.chartInstance.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            name: lastHoveredArea,
          });
          // 展示该区域tooltip
          setTimeout(() => {
            this.showJinjiangTooltip({
              name: lastHoveredArea,
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
          }, 100);
        });
        // 鼠标移入区县时
        this.chartInstance.on("mouseover", (params) => {
          if (params.name) {
            lastHoveredArea = params.name;
            // 先取消所有高亮
            this.chartInstance.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
            });
            // 高亮当前区域
            this.chartInstance.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
            // 展示当前区域tooltip
            this.showJinjiangTooltip({
              name: lastHoveredArea,
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
          }
        });

        this.chartInstance.on("click", (params) => {
          // ... existing code ...
             this.$emit("region-click", params.name);
          if (params.name === "锦江区") {
            this.showShuangliuPopup();
          }
        });

        // 设置 ECharts 配置项
        this.chartInstance.setOption(option);
        setTimeout(() => {
          this.showJinjiangTooltip({
            name: "锦江区",
            componentType: "series",
            seriesType: "map3D",
          });
          this.chartInstance.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            name: "锦江区",
          });
          this.chartInstance.dispatchAction({
            type: "select",
            seriesIndex: 0,
            name: "锦江区",
          });
        }, 100);

        // 地图初始化后，默认高亮并显示锦江区的tooltip
        setTimeout(() => {
          if (this.chartInstance) {
            this.showJinjiangTooltip({
              name: "锦江区",
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: "锦江区",
            });
            this.chartInstance.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              name: "锦江区",
            });
          }
        }, 200);
      } catch (error) {
        console.error("加载 GeoJSON 数据失败:", error);
      }
    },
  },
  watch: {
    flag(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$emit("changeCity", true);
          this.initChart();
        });
      }
    },
  },
};
</script>
<style scope="scoped" lang="scss">
.mapClass3 {
  width: 1600px;
  height: 1200px;
  position: absolute;
  top: 7%;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  margin: auto;
}

.tkClass2 {
  width: 700px;
  background: linear-gradient(
    180deg,
    rgba(30, 15, 1, 0.6) 0%,
    rgba(176, 88, 0, 0.27) 100%
  );
  border: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(251, 230, 176, 1),
      rgba(246, 197, 120, 0)
    )
    2 2;
  backdrop-filter: blur(4px);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2000;
  padding-top: 10px;
  box-sizing: border-box;
  .tkName {
    width: 100%;
    height: 54px;
    background: linear-gradient(
      270deg,
      rgba(255, 194, 0, 0) 0%,
      rgba(255, 142, 0, 0.71) 100%
    );
    font-weight: 500;
    font-size: 34px;
    color: #ffffff;
    line-height: 48px;
    letter-spacing: 1px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    padding-left: 34px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
  }
  .tkBody {
    width: 100%;
    padding: 20px 0 25px 20px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    .tkItemClass {
      display: flex;
      align-items: center;
      .cir {
        width: 6px;
        height: 6px;
        background: #ffffff;
        box-shadow: 0px 0px 13px 0px #ff9c00, 0px 0px 8px 0px #ff9000;
      }
      .cirRight {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #ffffff;
        line-height: 52px;
        letter-spacing: 1px;
        margin-left: 15px;
      }
    }
  }
}
</style>
