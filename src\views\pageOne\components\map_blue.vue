<!--
  成都市企业走访轨迹地图组件
  功能：
  1. 展示成都市各区县地图
  2. 显示企业走访轨迹和飞线效果
  3. 支持区县数据下钻
  4. 提供企业走访详情展示
  5. 支持地图交互和高亮效果
-->
<template>
  <div class="mapClass1" @click="handleMaskClick">
    <!-- 主地图容器 -->
    <div
      ref="chartRef3"
      style="width: 100%; height: 100%; min-height: 300px"
      v-if="flag"
    ></div>
    <!-- 区县地图容器 -->
    <div
      ref="chartRef4"
      style="width: 100%; height: 100%; position: relative; min-height: 300px"
      v-else
    >
      <!-- 遮罩层，用于处理点击空白处返回成都市地图 -->
      <div
        class="mask"
        @click="handleMaskClick"
        style="
          background-color: red;
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          background: transparent;
          pointer-events: none;
        "
      ></div>
    </div>
  </div>
</template>

<script>
import { productUpdateList } from "@/api/article.js";
import * as echarts from "echarts";
// 导入各区县地图数据
import geoJsonData from "../img/cds.json"; // 成都市
import geoJsonDJYSData from "../img/djys.json"; // 都江堰市
import geoJsonPZSData from "../img/pzs.json"; // 彭州市
import geoJsonQZData from "../img/qzs.json"; // 邛崃市
import geoJsonJJData from "../img/jjq.json"; // 锦江区
import geoJsonQYData from "../img/qyq.json"; // 青羊区
import geoJsonJNData from "../img/jnq.json"; // 金牛区
import geoJsonWHData from "../img/whq.json"; // 武侯区
import geoJsonCHData from "../img/chq.json"; // 成华区
import geoJsonLQData from "../img/lqyq.json"; // 龙泉驿区
import geoJsonQBYData from "../img/qbjq.json"; // 青白江区
import geoJsonXJData from "../img/xdq.json"; // 新都区
import geoJsonWJData from "../img/wjq.json"; // 温江区
import geoJsonSLData from "../img/slq.json"; // 双流区
import geoJsonPXData from "../img/pdq.json"; // 郫都区
import geoJsonJYData from "../img/jys.json"; // 简阳市
import geoJsonDYData from "../img/dyx.json"; // 大邑县
import geoJsonPJData from "../img/pjx.json"; // 蒲江县
import geoJsonXJXData from "../img/xjq.json"; // 新津区
import geoJsonJTXData from "../img/jtx.json"; // 金堂县
import geoJsonCLData from "../img/czs.json"; // 崇州市
import geoJsonDBXQData from "../img/dbxq.json"; // 东部新区
import geoJsonGXQData from "../img/gxq.json"; // 高新区
import geoJsonTFXQData from "../img/tfxq.json"; // 天府新区

export default {
  name: "map_blue",
  // 组件属性定义
  props: {
    visible: { type: Boolean, default: false }, // 是否可见
    title: { type: String, default: "循环弹框" }, // 标题
    chartData: {
      // 图表数据
      type: Object,
      default: () => {
        return {
          xAxisData: [],
          seriesData: [],
        };
      },
    },
  },
  // 组件数据
  data() {
    return {
      twoMap: null, // 二级地图实例
      flag: true, // 地图切换标志
      rotationAngle: 10, // 旋转角度
      chartInstance3: null, // 主地图实例
      chartInstance4: null, // 区县地图实例
      mapName: "成都市", // 当前地图名称
      cityName: "", // 城市名称
      districtMapDot: [], // 区县点位坐标集合
      centerDot: [104.547, 30.413], // 中心点坐标
      showCustomTooltip: false, // 自定义提示框显示状态
      tooltipContent: "", // 提示框内容
      tooltipPosition: { x: 0, y: 0 }, // 提示框位置
      randomData: {}, // 随机数据缓存
      maxDt: localStorage.getItem("maxDt"), // 最大日期
      tooltipList: [], // 提示框数据列表
      cityLines: [], // 城市连线数据
    };
  },
  mounted() {
    this.getLocus(); // 先获取走访轨迹数据，数据到后再刷新地图
    this.getCityLocus(); // 获取城市走访数据
  },
  methods: {
    // 获取走访轨迹数据
    async getLocus() {
      const res = await productUpdateList([
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "小微机制走访员工数量",
          dimNum: 1,
        },
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "小微机制走访企业数量",
          dimNum: 1,
        },
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "小微机制走访园区数量",
          dimNum: 1,
        },
      ]);
      this.tooltipList = res.data.data;
      // 数据拿到后再刷新地图
      this.$nextTick(() => {
        this.initChart();
      });
    },
    // 获取城市走访数据
    async getCityLocus() {
      const res = await productUpdateList([
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "蓉易贷走访明细",
          dimNum: 1,
        },
      ]);
      this.cityLines = res.data.data;
    },
    // 返回成都市地图
    backToChengdu() {
      this.flag = true;
      this.twoMap = null;
      this.mapName = "成都市";
      this.$nextTick(() => {
        this.initChart();
      });
    },
    // 生成随机数
    generateRandomNumber(min = 5000, max = 8000) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    // 处理遮罩层点击事件
    handleMaskClick(e) {
      // 如果点击的是地图容器本身，不处理
      if (
        (this.$refs.chartRef3 && this.$refs.chartRef3.contains(e.target)) ||
        (this.$refs.chartRef4 && this.$refs.chartRef4.contains(e.target))
      ) {
        return;
      }
      // 否则返回成都市地图
      this.flag = true;
      this.twoMap = null;
      this.mapName = "成都市";
      this.initChart();
    },
    // 获取区域数据
    getDistrictData(districtName) {
      // 如果该区域没有数据，则生成随机数据并缓存
      if (!this.randomData[districtName]) {
        this.randomData[districtName] = {
          enterprises: this.generateRandomNumber(10, 30),
          loanAmount: (Math.random() * 20 + 5).toFixed(2),
          loanHouseholds: this.generateRandomNumber(0, 150),
          averageRate: (Math.random() * 2 + 3).toFixed(2),
        };
      }
      return this.randomData[districtName];
    },
    // 初始化主地图
    async initChart() {
      // 清理旧实例
      this.cleanupChartInstances();
      try {
        // 定义主要区域坐标
        const mainDistricts = this.getMainDistricts();
        // 生成连线和散点数据
        const { allLines, allScatter } = this.generateMapData(mainDistricts);
        // 名称归一化函数
        const normalize = (str) =>
          (str || "").replace(/\s+/g, "").toLowerCase().trim();
        // 生成分级色块地图数据（自动遍历geoJsonData.features，确保每个区块都有value）
        let mapData = [];
        if (this.flag && geoJsonData && geoJsonData.features) {
          mapData = geoJsonData.features.map((f) => {
            const name =
              f.properties &&
              (f.properties.name ||
                f.properties.NAME ||
                f.properties.fullname ||
                f.properties.adcode_name);
            const normName = normalize(name);
            const item = this.tooltipList.find(
              (t) =>
                normalize(t.dimCounty) === normName &&
                t.indexName === "小微机制走访企业数量"
            );
            return {
              name: name,
              value: item ? Number(item.indexValue) : 0,
            };
          });
        }
        // 计算最大值 和最小值
        let maxValue = 100;
        let minValue = 0;
        if (mapData.length > 0) {
          const values = mapData.map((item) => item.value || 0);
          maxValue = Math.max(...values);
          minValue = Math.min(...values);
          // 处理所有值都为0的情况
          if (maxValue === 0) {
            maxValue = 1; // 设置非零值确保颜色显示
          }
        }
        // 调试输出
        console.log("mapData", mapData);
        console.log("maxValue", maxValue);
        // 注册地图
        echarts.registerMap(this.mapName, geoJsonData);
        // 初始化 ECharts 实例
        this.chartInstance3 = echarts.init(this.$refs.chartRef3);
        // 配置地图选项
        const option = this.getMapOption(
          allLines,
          allScatter,
          mapData,
          maxValue,
          minValue
        );
        // 设置地图配置
        if (this.mapName == "成都市") {
          this.chartInstance3.setOption(option);
          this.setupMapEvents();
        }
      } catch (error) {
        console.error("加载 GeoJSON 数据失败:", error);
      }
    },
    // 清理图表实例
    cleanupChartInstances() {
      if (this.chartInstance3) {
        this.chartInstance3.dispose();
        this.chartInstance3 = null;
      }
      if (this.chartInstance4) {
        this.chartInstance4.dispose();
        this.chartInstance4 = null;
      }
    },
    // 获取主要区域坐标
    getMainDistricts() {
      return [
        {
          name: "锦江区",
          center: [104.057, 30.707],
          points: [
            [104.057, 30.707], // 北偏西
            [104.137, 30.707], // 北偏东
            [104.007, 30.657], // 正西
            [104.087, 30.567], // 正南
          ],
        },
        // ... 其他区域坐标
      ];
    },
    // 生成地图数据
    generateMapData(mainDistricts) {
      let allLines = [];
      let allScatter = [];
      mainDistricts.forEach((d) => {
        d.points.forEach((p) => {
          allLines.push({ coords: [d.center, p] });
          allScatter.push({ name: d.name, value: p });
        });
      });
      return { allLines, allScatter };
    },
    // 获取地图配置选项
    getMapOption(allLines, allScatter, mapData, maxValue, minValue) {
      // 原有series
      const series = [
        {
          type: "map",
          map: this.mapName,
          geoIndex: 0,
          roam: false,
          selectedMode: false,
          itemStyle: {
            borderColor: "#2ab8ff",
            borderWidth: 1.5,
            areaColor: "rgba(0,0,0,0)", // 透明，突出色块
          },
          emphasis: {
            itemStyle: {
              areaColor: "rgba(224, 247, 255, 0.1)",
              borderWidth: 4,
              shadowBlur: 30,
            },
          },
          label: {
            show: true,
            color: "#fff",
            fontSize: this.$autoFontSize(14),
            fontWeight: "bold",
          },
          data: mapData,
        },
        {
          type: "scatter",
          coordinateSystem: "geo",
          symbol: "circle",
          symbolSize: 4,
          itemStyle: {
            color: "#fff",
            borderColor: "#29FCFF",
            borderWidth: 1,
            shadowBlur: 2,
            shadowColor: "#e0e7ff",
          },
          data: allScatter,
        },
      ];
      return {
        geo: [
          {
            map: this.mapName,
            aspectScale: 0.9,
            roam: false,
            zoom: 1.2,
            layoutSize: "95%",
            layoutCenter: ["50%", "50%"],
            selectedMode: false,
            label: {
              show: true,
              color: "#fff",
              fontSize: this.$autoFontSize(14),
              fontWeight: "bold",
            },
            itemStyle: {
              areaColor: {
                type: "linear-gradient",
                x: 0,
                y: 200,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(37,108,190,0.3)",
                  },
                  {
                    offset: 1,
                    color: "rgba(15,169,195,0.3)",
                  },
                ],
                global: true,
              },
              borderColor: "#4ecee6",
              borderWidth: 1,
            },
            emphasis: {
              itemStyle: {
                areaColor: {
                  type: "linear-gradient",
                  x: 0,
                  y: 300,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(37,108,190,0.8)",
                    },
                    {
                      offset: 1,
                      color: "rgba(15,169,195,0.8)",
                    },
                  ],
                  global: true,
                },
                borderWidth: 2,
                shadowBlur: 20,
                shadowColor: "rgba(0,218,255,0.5)",
              },
              label: {
                show: true,
                color: "#fff",
                fontSize: this.$autoFontSize(15),
                fontWeight: "bold",
              },
            },
            zlevel: 3,
          },
          // ... 其他地图层级配置
        ],
        tooltip: {
          show: true,
          trigger: "item",
          formatter: this.getTooltipFormatter(),
          backgroundColor: "rgba(0,0,0,0.7)",
          borderColor: "#fff",
          borderWidth: 1,
          padding: 10,
          marginTop: -30,
        },
        visualMap: {
          min: minValue,
          max: maxValue,
          calculable: true,
          orient: "vertical",
          left: "right",
          top: "center",
          inRange: {
            color: [
              "#e0f7fa", // 极浅蓝
              "#b2ebf2",
              "#80deea",
              "#4dd0e1",
              "#26c6da",
              "#00bcd4",
              "#039be5",
              "#0288d1",
              "#0277bd",
              "#01579b", // 深蓝
            ], // 更多层次蓝色分级
          },
          text: ["高", "低"],
          textStyle: {
            color: "#fff",
          },
        },
        series,
      };
    },
    // 获取提示框格式化函数
    getTooltipFormatter() {
      return (params) => {
        params.name = params.name || "锦江区";
        const districtNum1 = this.tooltipList.find(
          (item) =>
            item.dimCounty === params.name.trim() &&
            item.indexName === "小微机制走访员工数量"
        );
        const districtNum2 = this.tooltipList.find(
          (item) =>
            item.dimCounty === params.name.trim() &&
            item.indexName === "小微机制走访企业数量"
        );
        const districtNum3 = this.tooltipList.find(
          (item) =>
            item.dimCounty === params.name.trim() &&
            item.indexName === "小微机制走访园区数量"
        );
        return this.formatTooltipContent(
          params,
          districtNum1,
          districtNum2,
          districtNum3
        );
      };
    },
    // 格式化提示框内容
    formatTooltipContent(params, districtNum1, districtNum2, districtNum3) {
      return `
              <div class="tkClass">
                <div class="tkName">${params.name}</div>
                <div class="tkBody">
                  <div class="tkItemClass">
                    <div class="cir"></div>
                    <div class="cirRight" style='font-size:${this.$autoFontSize(
                      14
                    )}px'>
                      走访人员数量:${districtNum1?.indexValue || 0}人
                    </div>
                  </div>
                  <div class="tkItemClass">
                    <div class="cir"></div>
                    <div class="cirRight" style='font-size:${this.$autoFontSize(
                      14
                    )}px'>
                      走访企业数量:${districtNum2?.indexValue || 0}家
                    </div>
                  </div>
                  <div class="tkItemClass">
                    <div class="cir"></div>
                    <div class="cirRight" style='font-size:${this.$autoFontSize(
                      14
                    )}px'>
                      走访园区数量:${districtNum3?.indexValue || 0}个
                    </div>
                  </div>
                </div>
              </div>
            `;
    },
    // 设置地图事件
    setupMapEvents() {
      let lastHoveredArea = "锦江区";
      // 鼠标移出事件
      this.chartInstance3.getZr().on("globalout", () => {
        this.handleGlobalOut(lastHoveredArea);
      });
      // 鼠标移入事件
      this.chartInstance3.on("mouseover", (params) => {
        if (params.componentType === "series" && params.seriesType === "map") {
          lastHoveredArea = params.name;
          this.handleMouseOver(params);
        }
      });
      // 点击事件
      this.chartInstance3.on("click", (params) => {
        this.$emit("region-click", params.name);
        this.handleClick(params);
      });
    },
    // 处理鼠标移出事件
    handleGlobalOut(lastHoveredArea) {
      setTimeout(() => {
        if (this.chartInstance3) {
          this.chartInstance3.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
          });
          this.chartInstance3.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            name: lastHoveredArea,
          });
          this.chartInstance3.dispatchAction({
            type: "showTip",
            seriesIndex: 0,
            name: lastHoveredArea,
          });
        }
      }, 100);
    },
    // 处理鼠标移入事件
    handleMouseOver(params) {
      this.chartInstance3.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
      });
      this.chartInstance3.dispatchAction({
        type: "highlight",
        seriesIndex: 0,
        name: params.name,
      });
    },
    // 处理点击事件
    handleClick(params) {
      if (params.componentType === "series" && params.seriesType === "map") {
        params.name = params.name.replace(/\s+/g, "");
        this.mapName = params.name;
        this.handleDistrictClick(params);
      }
    },
    // 处理区县点击
    async handleDistrictClick(params) {
      const res = await productUpdateList([
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: params.name,
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "蓉易贷走访明细",
          dimNum: 1,
        },
      ]);
      this.districtMapDot = JSON.parse(res.data.data[0].bizContent);
      this.$emit("changeCity", false);
      this.flag = false;
      this.twoMap = this.getDistrictMap(params.name);
      this.$nextTick(() => {
        this.chartInstance3 = null;
        this.initChart2();
      });
    },
    // 获取区县地图数据
    getDistrictMap(districtName) {
      const districtMap = {
        都江堰市: geoJsonDJYSData,
        彭州市: geoJsonPZSData,
        邛崃市: geoJsonQZData,
        锦江区: geoJsonJJData,
        青羊区: geoJsonQYData,
        金牛区: geoJsonJNData,
        武侯区: geoJsonWHData,
        成华区: geoJsonCHData,
        龙泉驿区: geoJsonLQData,
        青白江区: geoJsonQBYData,
        新都区: geoJsonXJData,
        温江区: geoJsonWJData,
        双流区: geoJsonSLData,
        郫都区: geoJsonPXData,
        简阳市: geoJsonJYData,
        大邑县: geoJsonDYData,
        蒲江县: geoJsonPJData,
        新津区: geoJsonXJXData,
        金堂县: geoJsonJTXData,
        崇州市: geoJsonCLData,
        东部新区: geoJsonDBXQData,
        高新区: geoJsonGXQData,
        天府新区: geoJsonTFXQData,
      };
      return districtMap[districtName];
    },
    // 初始化区县地图
    async initChart2() {
      // 清理旧实例
      this.cleanupChartInstances();
      try {
        // 生成连线和散点数据
        const { allLines, allScatter } = this.generateDistrictMapData();
        // 注册地图
        echarts.registerMap(this.mapName, this.twoMap);
        // 初始化 ECharts 实例
        this.chartInstance4 = echarts.init(this.$refs.chartRef4);
        // 配置地图选项
        const option2 = this.getDistrictMapOption(allLines, allScatter);
        // 设置地图配置
        this.chartInstance4.setOption(option2);
        // 设置地图事件
        this.setupDistrictMapEvents();
      } catch (error) {
        this.handleChartInitError(error);
      }
    },
    // 生成区县地图数据
    generateDistrictMapData() {
      let allLines = [];
      let allScatter = [];
      this.districtMapDot.location.forEach((p, idx) => {
        allLines.push({
          coords: [
            this.districtMapDot.district.split(","),
            p.location.split(","),
          ],
          companyInfo: this.getMockCompanyInfo(idx),
        });
        allScatter.push({
          name: this.districtMapDot.district,
          value: p.location.split(","),
        });
      });
      return { allLines, allScatter };
    },
    // 获取模拟企业信息
    getMockCompanyInfo(idx) {
      return {
        companyName: `这是一个新企业${idx + 1}`,
        address: `江苏省苏州市苏州工业园区里面的金鸡湖隔壁的楼${idx + 1}楼`,
        industry: "金融行业",
        scale: "小型企业",
        finance: `${300 + idx}亿`,
        park: "工业园区后面的农业园区前面的商业园区",
        result: "非常棒",
      };
    },
    // 获取区县地图配置选项
    getDistrictMapOption(allLines, allScatter) {
      return {
        // 提示框配置
        tooltip: {
          show: true,
          trigger: "item",
          formatter: this.getDistrictTooltipFormatter(),
          backgroundColor: "rgba(0,0,0,0.7)",
          borderColor: "#fff",
          borderWidth: 1,
          padding: 10,
          position: this.getTooltipPosition,
        },
        // 地图配置
        geo: this.getDistrictGeoConfig(),
        // 地图系列配置
        series: this.getDistrictSeriesConfig(allLines, allScatter),
      };
    },
    // 获取区县提示框格式化函数
    getDistrictTooltipFormatter() {
      return (params) => {
        if (params.seriesType === "lines" && params.data.companyInfo) {
          const info = params.data.companyInfo;
          let obj = this.cityLines.find(
            (item) => item.dimCounty == this.mapName
          );
          obj = JSON.parse(obj.bizContent);
          let obj2 = obj.location.find(
            (item) => item.location == params.data.coords[1].join(",")
          );
          return this.formatDistrictTooltipContent(obj2);
        }
        return "";
      };
    },
    // 格式化区县提示框内容
    formatDistrictTooltipContent(obj2) {
      const fields = [
        { label: "走访时间", value: obj2 ? obj2.visit_time : "--" },
        { label: "企业名称", value: obj2 ? obj2.enterprise_name : "--" },
        { label: "企业地址", value: obj2 ? obj2.regist_addr : "--" },
        { label: "所属行业", value: obj2 ? obj2.industry_l1_name : "--" },
        { label: "企业规模", value: obj2 ? obj2.scale : "--" },
        { label: "融资需求", value: obj2 ? obj2.is_req : "--" },
        { label: "所在园区", value: obj2 ? obj2.park : "--" },
        { label: "走访成效", value: obj2 ? obj2.effect : "--" },
      ];
      return this.generateTooltipHTML(fields);
    },
    // 生成提示框HTML
    generateTooltipHTML(fields) {
      return `
        <div style="width: ${this.$autoFontSize(
          390
        )}px; background: linear-gradient(180deg, rgba(30, 15, 1, 0.6) 0%, rgba(176, 88, 0, 0.27) 100%); border: 2px solid; border-image: linear-gradient(180deg, rgba(251, 230, 176, 1), rgba(246, 197, 120, 0)) 2 2; backdrop-filter: blur(4px); position: absolute; top: 0; left: 0; z-index: 2000; padding-top: 10px; box-sizing: border-box;">
          <div style="width: ${this.$autoFontSize(
            350
          )}px; height: ${this.$autoFontSize(
        30
      )}px; background: linear-gradient(270deg, rgba(255, 194, 0, 0) 0%, rgba(255, 142, 0, 0.71) 100%); font-size:${this.$autoFontSize(
        17
      )}px; color: #ffffff; line-height: ${this.$autoFontSize(
        30
      )}px; letter-spacing: 1px; text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5); padding-left: ${this.$autoFontSize(
        28
      )}px; display: flex; font-weight: 500; align-items: left; box-sizing: border-box; letter-spacing: 2px;">走访企业信息</div>
          <div style="width:100%; padding:20px 0 25px 20px; display:flex; flex-direction:column;">
            ${fields.map((f) => this.generateFieldHTML(f)).join("")}
          </div>
        </div>
      `;
    },
    // 生成字段HTML
    generateFieldHTML(field) {
      return `
                        <div style="display:flex;align-items:center;margin-bottom:${this.$autoFontSize(
                          2
                        )}px;">
                          <div style='width:6px;height:6px;background:#fff;box-shadow:0px 0px 13px 0px #ff9c00,0px 0px 8px 0px #ff9000;'></div>
                          <div style='font-family:PingFangSC,PingFang SC,sans-serif;font-weight:400;font-size:${this.$autoFontSize(
                            14
                          )}px;color:#fff;line-height:${this.$autoFontSize(
        20
      )}px;letter-spacing:1px;margin-left:15px;word-break:break-all;white-space:normal;'>
             ${field.label}：${this.breakLine(
        field.value,
        field.label === "走访时间" ? 19 : 18
      )}
                    </div>
                  </div>
                `;
    },
    // 文本换行处理
    breakLine(str, len = 18) {
      if (!str) return "";
      let result = "";
      for (let i = 0; i < str.length; i += len) {
        result += str.substr(i, len) + "<br/>";
      }
      return result;
    },
    // 获取提示框位置
    getTooltipPosition(point, params, dom, rect, size) {
      return [point[0], point[1]];
    },
    // 获取区县地图配置
    getDistrictGeoConfig() {
      return [
        {
          map: this.mapName,
          aspectScale: 0.9,
          roam: false,
          zoom: 1,
          layoutSize: "95%",
          layoutCenter: ["50%", "50%"],
          selectedMode: false,
          label: {
            show: true,
            color: "#fff",
            fontSize: this.$autoFontSize(12),
          },
          itemStyle: {
            areaColor: {
              type: "linear-gradient",
              x: 0,
              y: 400,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(37,108,190,0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(15,169,195,0.3)",
                },
              ],
              global: true,
            },
            borderColor: "#4ecee6",
            borderWidth: 1,
          },
          emphasis: {
            itemStyle: {
              areaColor: {
                type: "linear-gradient",
                x: 0,
                y: 300,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(37,108,190,0.8)",
                  },
                  {
                    offset: 1,
                    color: "rgba(15,169,195,0.8)",
                  },
                ],
                global: true,
              },
              borderWidth: 2,
              shadowBlur: 20,
              shadowColor: "rgba(0,218,255,0.5)",
            },
            label: {
              show: true,
              color: "#fff",
              fontSize: this.$autoFontSize(14),
              fontWeight: "bold",
            },
          },
          zlevel: 3,
          silent: false,
        },
        // ... 其他地图层级配置
      ];
    },
    // 获取区县地图系列配置
    getDistrictSeriesConfig(allLines, allScatter) {
      return [
        {
          type: "map",
          map: this.mapName,
          geoIndex: 0,
          roam: false,
          selectedMode: false,
          label: {
            show: true,
            color: "#fff",
            fontSize: 12,
          },
          zoom: 0.8,
          layoutCenter: ["50%", "50%"],
          layoutSize: "70%",
          itemStyle: {
            areaColor: "#061537",
            borderColor: "#29FCFF",
            borderWidth: 3,
            shadowBlur: 20,
            shadowColor: "#29FCFF",
          },
        },
        {
          type: "lines",
          coordinateSystem: "geo",
          zlevel: 10,
          z: 20,
          large: true,
          largeThreshold: 100,
          clip: true,
          triggerLineEvent: true,
          name: "飞线",
          selectedMode: "series",
          select: {
            lineStyle: {
              type: "dashed",
            },
          },
          effect: {
            show: true,
            period: 6,
            roundTrip: true,
            trailLength: 0.5,
            color: "#fff",
            symbol: "circle",
            symbolSize: this.$autoFontSize(8),
          },
          lineStyle: {
            width: this.$autoFontSize(1.5),
            opacity: 0.8,
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0.5,
              y2: 0,
              colorStops: [
                { offset: 0, color: "#ffe600" },
                { offset: 1, color: "#ffffff" },
              ],
            },
            shadowBlur: this.$autoFontSize(2),
            shadowColor: "#fff",
            curveness: 0.5,
          },
          emphasis: {
            lineStyle: {
              width: this.$autoFontSize(3),
              opacity: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: "#FF8C00" },
                  { offset: 1, color: "#FFA500" },
                ],
              },
              shadowBlur: 3,
              shadowColor: "#FF8C00",
            },
            effect: {
              show: true,
              period: 6,
              trailLength: 0.5,
              color: "#FF8C00",
              symbol: "circle",
              symbolSize: 12,
            },
          },
          data: allLines.map((line, idx) => ({
            ...line,
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: "#ffe600" },
                  { offset: 1, color: "#ffffff" },
                ],
              },
              width: idx % 2 === 0 ? 0.5 : 0.3,
              opacity: 0.8,
              curveness: 0.5,
            },
            emphasis: {
              lineStyle: {
                width: 4,
                opacity: 1,
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    { offset: 0, color: "#FF8C00" },
                    { offset: 1, color: "#FFA500" },
                  ],
                },
                shadowBlur: 3,
                shadowColor: "#FF8C00",
              },
              effect: {
                show: true,
                period: 6,
                roundTrip: true,
                trailLength: 0.5,
                color: "#FF8C00",
                symbol: "circle",
                symbolSize: 12,
              },
            },
          })),
        },
        {
          type: "effectScatter",
          coordinateSystem: "geo",
          zlevel: 3,
          rippleEffect: {
            brushType: "stroke",
            scale: 4,
            period: 4,
          },
          symbol: "circle",
          symbolSize: this.$autoFontSize(5),
          itemStyle: {
            color: "#ffe600",
            shadowBlur: 10,
            shadowColor: "#ffe600",
          },
          data: allScatter,
        },
      ];
    },
    // 设置区县地图事件
    setupDistrictMapEvents() {
      this.$nextTick(() => {
        if (this.chartInstance4) {
          this.chartInstance4.off("mouseover");
          this.chartInstance4.off("mouseout");
          this.chartInstance4.on("click", { seriesIndex: 1 }, (params) => {
            this.chartInstance4.dispatchAction({
              type: "highlight",
              seriesIndex: 1,
              dataIndex: params.dataIndex,
            });
          });
          this.chartInstance4.on("mouseout", { seriesIndex: 1 }, (params) => {
            this.chartInstance4.dispatchAction({
              type: "hideTip",
            });
            this.chartInstance4.dispatchAction({
              type: "downplay",
              seriesIndex: 1,
            });
          });
        }
      });
    },
    // 处理图表初始化错误
    handleChartInitError(error) {
      console.error("加载 GeoJSON 数据失败:", error);
      setTimeout(() => {
        try {
          if (this.$refs.chartRef4) {
            this.chartInstance4 = echarts.init(this.$refs.chartRef4);
          }
        } catch (retryError) {
          console.error("重试初始化图表失败:", retryError);
        }
      }, 200);
    },
    // 处理城市变更
    handleCityChange(isChengdu) {
      if (isChengdu) {
        this.flag = true;
        this.twoMap = null;
        this.mapName = "成都市";
        this.$nextTick(() => {
          this.initChart();
        });
      }
    },
  },
  // 监听器
  watch: {
    flag(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$emit("changeCity", true);
          this.initChart();
        });
      }
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 地图容器样式 */
.mapClass1 {
  width: 1600px;
  height: 1200px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  margin: auto;
}

/* 提示框样式 */
.tkClass {
  width: 400px;
  background: linear-gradient(
    180deg,
    rgba(30, 15, 1, 0.6) 0%,
    rgba(176, 88, 0, 0.27) 100%
  );
  border: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(251, 230, 176, 1),
      rgba(246, 197, 120, 0)
    )
    2 2;
  backdrop-filter: blur(4px);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2000;
  padding-top: 12px;
  box-sizing: border-box;

  /* 提示框标题样式 */
  .tkName {
    width: 310px;
    height: 54px;
    font-weight: 500;
    font-size: 34px;
    color: #ffffff;
    line-height: 48px;
    letter-spacing: 1px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    padding-left: 35px;
    display: flex;
    align-items: left;
    box-sizing: border-box;
  }

  /* 提示框内容样式 */
  .tkBody {
    width: 100%;
    padding: 20px 0 25px 20px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    /* 提示框项目样式 */
    .tkItemClass {
      display: flex;
      align-items: center;

      /* 圆点样式 */
      .cir {
        width: 6px;
        height: 6px;
        background: #ffffff;
        box-shadow: 0px 0px 13px 0px #ff9c00, 0px 0px 8px 0px #ff9000;
      }

      /* 文本样式 */
      .cirRight {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 30px;
        color: #ffffff;
        line-height: 52px;
        letter-spacing: 1px;
        margin-left: 15px;
      }
    }
  }
}

/* 遮罩层样式 */
.mask {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background: transparent;
  pointer-events: auto;
}

/* 地图区域样式 */
.mapClass1 > [ref="chartRef4"],
.mapClass1 > [ref="chartRef3"] {
  pointer-events: auto;
  z-index: 2;
}
</style>
