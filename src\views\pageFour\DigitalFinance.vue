<!--
  数字金融产品页面组件
  功能：
  1. 展示数字金融产品的整体布局
  2. 提供政金企通道的可视化展示
  3. 展示企业端、政府端、机构端的功能模块
  4. 提供模型平台、基础平台、运营管理后台的展示
-->
<template>
  <div class="DigitalFinance-bgblackClass4">
    <div class="DigitalFinance-dpClass">
      <!-- 头部路由导航 -->
      <HeaderRouter @hideCenterBcg="hideCenterBcg" />
      <div class="DigitalFinance-pageContent">
        <!-- 左侧内容区域 -->
        <div class="DigitalFinance-pageLeft3">
          <div>
            <dataAcquisitionVue style="margin-top: 9%" />
          </div>
          <div>
            <sjhjFour style="margin-top: 5%" />
          </div>
          <div>
            <DataUse style="margin-top: 5%" />
          </div>
        </div>
        <!-- 中间内容区域：政金企通道展示 -->
        <div
          class="DigitalFinance-pageCenter4"
          v-if="flag"
          :class="{ 'DigitalFinance-show-background': showBackground }"
        >
          <!-- 政金企通道标题 -->
          <div class="DigitalFinance-cube-title">政金企通道</div>
          <!-- 3D立方体展示区域 -->
          <div class="DigitalFinance-cube-section">
            <!-- 3D立方体 -->
            <div
              class="DigitalFinance-cube-3d"
              @mouseenter="showCubePopup = true"
              @mouseleave="showCubePopup = false"
            ></div>
            <!-- 立方体悬浮标签 -->
            <div
              class="DigitalFinance-cube-popup"
              v-if="showCubePopup"
              @mouseenter="showCubePopup = true"
              @mouseleave="showCubePopup = false"
            >
              <!-- 左侧标签列 -->
              <div
                class="DigitalFinance-cube-popup-col DigitalFinance-left-col"
              >
                <div
                  class="DigitalFinance-cube-popup-item-vertical"
                  style="top: -10vh"
                >
                  线上系统直连标准规范
                </div>
                <div
                  class="DigitalFinance-cube-popup-item-vertical"
                  style="top: -10vh; left: 2.3vw"
                >
                  智融AI
                </div>
                <div
                  class="DigitalFinance-cube-popup-item-vertical"
                  style="top: 0px; left: 3.8vw"
                >
                  小微通
                </div>
                <div
                  class="DigitalFinance-cube-popup-item-vertical"
                  style="top: -10vh; left: 6vw"
                >
                  集团产品线上化
                </div>
              </div>
              <!-- 右侧标签列 -->
              <div
                class="DigitalFinance-cube-popup-col DigitalFinance-right-col"
              >
                <div
                  class="DigitalFinance-cube-popup-item-vertical"
                  style="top: -10vh; left: 3vw"
                >
                  一站式税务授权查询
                </div>
                <div
                  class="DigitalFinance-cube-popup-item-vertical"
                  style="top: -8vh; left: 5.5vw"
                >
                  政策AI
                </div>
                <div
                  class="DigitalFinance-cube-popup-item-vertical"
                  style="top: -10vh; left: 8vw"
                >
                  投融资线上撮合
                </div>
              </div>
            </div>
            <!-- 中间标签 -->
            <div
              @mouseenter="showCubePopup = true"
              @mouseleave="showCubePopup = false"
              :style="{
                color: showCubePopup ? '#05FFFB' : '#FFFFFF',
              }"
              class="DigitalFinance-cube-middle-label"
            >
              高效撮合对接能力
            </div>
          </div>
          <!-- 圆形图标展示区域 -->
          <div class="DigitalFinance-circle-section">
            <div class="DigitalFinance-circle-row">
              <!-- 企业端展示 -->
              <div class="DigitalFinance-circle-item">
                <!-- 企业端图标 -->
                <div
                  class="DigitalFinance-circle-icon-enterprise"
                  @mouseenter="
                    enterpriseHovered = true;
                    showEnterprisePopup = true;
                  "
                  @mouseleave="
                    enterpriseHovered = false;
                    showEnterprisePopup = false;
                  "
                ></div>
                <!-- 企业端标题 -->
                <div class="DigitalFinance-circle-title-enterprise">企业端</div>
                <!-- 企业端描述 -->
                <div
                  class="DigitalFinance-circle-desc-enterprise"
                  :style="{ color: enterpriseHovered ? '#05FFFB' : '#FFFFFF' }"
                >
                  数据增信能力
                </div>
                <!-- 企业端弹框 -->
                <div
                  class="DigitalFinance-popup-container DigitalFinance-enterprise-popup"
                  v-if="showEnterprisePopup"
                >
                  <div class="DigitalFinance-popup-content">
                    <div class="DigitalFinance-popup-item">
                      【 企业信用报告 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 预授信白名单模型 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 企业信用查询 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 企业信用修复 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 一站式征信报告查询 】
                    </div>
                  </div>
                </div>
              </div>
              <!-- 政府端展示 -->
              <div class="DigitalFinance-circle-item">
                <!-- 政府端图标 -->
                <div
                  class="DigitalFinance-circle-icon-government"
                  @mouseenter="
                    governmentHovered = true;
                    showGovernmentPopup = true;
                  "
                  @mouseleave="
                    governmentHovered = false;
                    showGovernmentPopup = false;
                  "
                ></div>
                <!-- 政府端标题 -->
                <div class="DigitalFinance-circle-title-government">政府端</div>
                <!-- 政府端描述 -->
                <div
                  class="DigitalFinance-circle-desc-government"
                  :style="{ color: governmentHovered ? '#05FFFB' : '#FFFFFF' }"
                >
                  "财金互动"<br />财政兑现能力
                </div>
                <!-- 政府端弹框 -->
                <div
                  class="DigitalFinance-popup-container DigitalFinance-government-popup"
                  v-if="showGovernmentPopup"
                >
                  <div class="DigitalFinance-popup-content">
                    <div class="DigitalFinance-popup-item">【 政策推荐 】</div>
                    <div class="DigitalFinance-popup-item">【 政策触达 】</div>
                    <div class="DigitalFinance-popup-item">【 政策申办 】</div>
                    <div class="DigitalFinance-popup-item">
                      【 产业园区图谱 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 政策大模型 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 数据驱动舱 】
                    </div>
                  </div>
                </div>
              </div>
              <!-- 机构端展示 -->
              <div class="DigitalFinance-circle-item">
                <!-- 机构端图标 -->
                <div
                  class="DigitalFinance-circle-icon-org"
                  @mouseenter="
                    institutionsHovered = true;
                    showInstitutionsPopup = true;
                  "
                  @mouseleave="
                    institutionsHovered = false;
                    showInstitutionsPopup = false;
                  "
                ></div>
                <!-- 机构端标题 -->
                <div class="DigitalFinance-circle-title-org">机构端</div>
                <!-- 机构端描述 -->
                <div
                  class="DigitalFinance-circle-desc-org"
                  :style="{
                    color: institutionsHovered ? '#05FFFB' : '#FFFFFF',
                  }"
                >
                  联合建模能力
                </div>
                <!-- 机构端弹框 -->
                <div
                  class="DigitalFinance-popup-container DigitalFinance-institutions-popup"
                  v-if="showInstitutionsPopup"
                >
                  <div class="DigitalFinance-popup-content">
                    <div class="DigitalFinance-popup-item">
                      【 贷前潜客挖掘 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 贷前精准触达 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 贷中风险监测 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 贷后风险预警 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 风险决策引擎 】
                    </div>
                    <div class="DigitalFinance-popup-item">
                      【 线上仲裁诉讼 】
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 平台展示区域 -->
          <div class="DigitalFinance-platform-section">
            <!-- 模型平台 -->
            <div class="DigitalFinance-platform-item">
              <!-- 模型平台图标 -->
              <div
                class="DigitalFinance-platform-icon DigitalFinance-model"
                @mouseenter="
                  modelHovered = true;
                  showModelPopup = true;
                "
                @mouseleave="
                  modelHovered = false;
                  showModelPopup = false;
                "
              ></div>
              <!-- 模型平台标题 -->
              <div
                class="DigitalFinance-platform-title DigitalFinance-model"
                :style="{ color: modelHovered ? '#05FFFB' : '#FFFFFF' }"
              >
                模型平台
              </div>
              <!-- 模型平台弹框 -->
              <div
                class="DigitalFinance-popup-container DigitalFinance-model-popup"
                v-if="showModelPopup"
              >
                <div class="DigitalFinance-popup-content">
                  <div class="DigitalFinance-popup-item">搜索算法</div>
                  <div class="DigitalFinance-popup-item">推荐算法</div>
                  <div class="DigitalFinance-popup-item">专家经验库</div>
                  <div class="DigitalFinance-popup-item">知识推理算法</div>
                  <div class="DigitalFinance-popup-item">
                    DeepSeek等通用模型
                  </div>
                </div>
              </div>
            </div>
            <!-- 基础平台 -->
            <div class="DigitalFinance-platform-item">
              <!-- 基础平台图标 -->
              <div
                class="DigitalFinance-platform-icon DigitalFinance-base"
                @mouseenter="
                  baseHovered = true;
                  showBasePopup = true;
                "
                @mouseleave="
                  baseHovered = false;
                  showBasePopup = false;
                "
              ></div>
              <!-- 基础平台标题 -->
              <div
                class="DigitalFinance-platform-title DigitalFinance-base"
                :style="{ color: baseHovered ? '#05FFFB' : '#FFFFFF' }"
              >
                基础平台
              </div>
              <!-- 基础平台弹框 -->
              <div
                class="DigitalFinance-popup-container DigitalFinance-base-popup"
                v-if="showBasePopup"
              >
                <div class="DigitalFinance-popup-content">
                  <div class="DigitalFinance-popup-item">授权管理中心</div>
                  <div class="DigitalFinance-popup-item">工作流引擎</div>
                  <div class="DigitalFinance-popup-item">故障监控</div>
                  <div class="DigitalFinance-popup-item">客户工作台</div>
                  <div class="DigitalFinance-popup-item">机构工作台</div>
                  <div class="DigitalFinance-popup-item">广告投放</div>
                </div>
              </div>
            </div>
            <!-- 运营管理后台 -->
            <div class="DigitalFinance-platform-item">
              <!-- 运营管理后台图标 -->
              <div
                class="DigitalFinance-platform-icon DigitalFinance-ops"
                @mouseenter="
                  opsHovered = true;
                  showOpsPopup = true;
                "
                @mouseleave="
                  opsHovered = false;
                  showOpsPopup = false;
                "
              ></div>
              <!-- 运营管理后台标题 -->
              <div
                class="DigitalFinance-platform-title DigitalFinance-ops"
                :style="{ color: opsHovered ? '#05FFFB' : '#FFFFFF' }"
              >
                运营管理后台
              </div>
              <!-- 运营管理后台弹框 -->
              <div
                class="DigitalFinance-popup-container DigitalFinance-ops-popup"
                v-if="showOpsPopup"
              >
                <div class="DigitalFinance-popup-content">
                  <div class="DigitalFinance-popup-item">运营圈客</div>
                  <div class="DigitalFinance-popup-item">权益对接</div>
                  <div class="DigitalFinance-popup-item">智能外呼</div>
                  <div class="DigitalFinance-popup-item">活动报名工具</div>
                  <div class="DigitalFinance-popup-item">舆情监控</div>
                  <div class="DigitalFinance-popup-item">办理失败监控</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 右侧内容区域 -->
        <div class="DigitalFinance-pageRight3">
          <div>
            <SzyyFour style="margin-top: 9%" />
          </div>
          <div>
            <ApplyModel style="margin-top: 5%" />
          </div>
          <div>
            <sjcpFour style="margin-top: 5%" />
          </div>
        </div>
      </div>
      <!-- 底部切换按钮 -->
      <div class="DigitalFinance-buttomClass">
        <ChangeButtom title="数字金融产品" />
      </div>
    </div>
  </div>
</template>

<script>
// 导入组件
import dataAcquisitionVue from "./components/dataAcquisition.vue";
import HeaderRouter from "../components/headerRouter";
import sjcpFour from "./components/sjcpFour";
import sjhjFour from "./components/sjhjFour";
import SzyyFour from "./components/szyyFour";
import ChangeButtom from "../components/changeButtom";
/* import DataAggregation from "./components/dataAggregation.vue"; */
import DataUse from "./components/dataUse.vue";
import ApplyModel from "./components/applyModel.vue";

export default {
  name: "pageone",
  components: {
    HeaderRouter,
    sjcpFour,
    sjhjFour,
    SzyyFour,
    ChangeButtom,
    dataAcquisitionVue,
    DataUse,
    ApplyModel,
  },
  data() {
    return {
      showDialog: false,
      flagFont: true,
      flag: true,
      showBackground: true, // 控制背景图片显示
      // 控制各个模块的悬浮状态
      institutionsHovered: false,
      enterpriseHovered: false,
      governmentHovered: false,
      modelHovered: false,
      baseHovered: false,
      opsHovered: false,
      // 控制各个弹框的显示状态
      showEnterprisePopup: false,
      showGovernmentPopup: false,
      showInstitutionsPopup: false,
      showModelPopup: false,
      showBasePopup: false,
      showOpsPopup: false,
      showCubePopup: false,
      // 标签数据
      labelData: [
        "投融资线上撮合",
        "数据增信能力",
        "财金互动",
        "财政兑现能力",
        "联合建模能力",
        "线上价值诉讼",
      ],
    };
  },
  mounted() {
    // 根据路由控制页面显示
    this.flag = this.$route.name === "pageFour";
    this.showBackground = this.$route.name === "pageFour";
  },
  methods: {
    // 页面跳转
    goPage(data) {
      if (!this.$refs.mapOne.flag) {
        this.$refs.mapOne.flag = data;
      }
    },
    // 改变颜色
    changeCity(data) {
      this.flagFont = data;
    },
    // 隐藏centerBcg背景图片
    hideCenterBcg() {
      this.flag = false;
      this.showBackground = false;
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 主容器样式 */
.DigitalFinance-bgblackClass4 {
  width: 100vw;
  height: 100vh;
  background-image: url("~@/assets/dp/bgblack.jpg");
  background-repeat: no-repeat;
  background-size: 100vw 100vh;
  position: relative;
  overflow: hidden;

  /* 页面容器样式 */
  .DigitalFinance-dpClass {
    width: 100vw;
    height: 100vh;
    background-image: url("~@/assets/dp/bg.png");
    background-repeat: no-repeat;
    background-size: 100vw 100vh;
    display: flex;
    flex-direction: column;
    .DigitalFinance-pageContent {
      display: flex;
      margin: 0 100px;
      height: 100%;
      justify-content: space-between;
      .DigitalFinance-pageLeft3 {
        height: 97%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
      }
      .DigitalFinance-pageRight3 {
        height: 97%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
      }
    }
    .DigitalFinance-buttomClass {
      position: absolute;
      width: 50%;
      bottom: 0;
    }
  }

  /* 中间内容区域样式 */
  .DigitalFinance-pageCenter4 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 70%;
    height: 70%;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center top;
    z-index: 1000;
    position: relative;
    margin-top: 18vh;
    background-image: url("~@/assets/serve/centerBcg.png");
    /* 让内容整体上移并缩小 */
    justify-content: flex-start;
    .DigitalFinance-cube-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 46px;
      color: #d3f1ff;
      line-height: 65px;
      letter-spacing: 2px;
      text-align: right;
      font-style: normal;
      background: linear-gradient(180deg, #ffffff 0%, #b7deff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: -90px;
      position: absolute;
      top: -120px;
      left: 740px;
    }
    .DigitalFinance-cube-section {
      .DigitalFinance-cube-3d {
        position: absolute;
        top: -170px;
        left: 640px;
        height: 102px;
        width: 450px; // 缩小立方体
        height: 450px;
        background: url("~@/assets/serve/cube.png") no-repeat center/contain;
        position: 100% 100%;
        cursor: pointer;
      }
      .DigitalFinance-cube-labels {
        position: relative;
        width: 100%;
        height: 100%;
        .DigitalFinance-cube-label-item {
          padding: 20px 0;
          width: 71px;
          position: absolute;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          letter-spacing: 1px;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-shadow: 0px 0px 10px rgba(0, 204, 255, 0.5),
            0px 1px 3px rgba(0, 0, 0, 0.32);
          white-space: nowrap;
          z-index: 10;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.33) 0%,
            rgba(255, 255, 255, 0.14) 28%,
            rgba(255, 255, 255, 0.14) 69%,
            rgba(255, 255, 255, 0.33) 100%
          );

          span {
            color: #ffffff;
            display: block;
            line-height: 30px;
            text-align: center;
          }
        }

        // 左上标签
        .DigitalFinance-cube-label-top-left {
          top: 00px;
          left: 190px;
        }

        // 左中标签
        .DigitalFinance-cube-label-middle-left {
          top: 0px;
          left: 400px;
        }

        // 左下标签
        .DigitalFinance-cube-label-bottom-left {
          top: 90px;
          left: 300px;
        }

        // 右上标签
        .DigitalFinance-cube-label-top-right {
          top: 100px;
          right: 300px;
        }

        // 右中标签
        .DigitalFinance-cube-label-middle-right {
          top: 270px;
          right: 400px;
        }

        // 右下标签
        .DigitalFinance-cube-label-bottom-right {
          top: 60px;
          right: 180px;
        }

        // 右下标签（第七个）
        .DigitalFinance-cube-label-bottom-right-2 {
          top: 20px;
          right: 400px;
        }
      }
      .DigitalFinance-cube-middle-label {
        position: absolute;
        top: 250px;
        left: 650px;
        width: 472px; // 缩小立方体
        height: 102px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 40px;
        color: #ffffff;
        line-height: 102px;
        letter-spacing: 2px;
        text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
          0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
        background: url("./assets/gaoxiao.png");
        background-size: 100% 100%;
        cursor: pointer;
        text-align: center;
        font-style: normal;
      }
    }
    .DigitalFinance-circle-section {
      width: 370px;
      height: 370px;
      margin-bottom: 18px;
      .DigitalFinance-circle-row {
        display: flex;
        justify-content: space-between;
        .DigitalFinance-circle-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .DigitalFinance-circle-icon-enterprise {
            position: absolute;
            left: 330px;
            top: 360px;
            width: 200px; // 缩小圆形icon
            height: 200px;
            border-radius: 50%;
            margin-bottom: 6px;
            background: linear-gradient(180deg, #398fff 0%, #0ff 100%);
            background-image: url("~@/assets/serve/enterprise.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          .DigitalFinance-circle-icon-enterprise:hover {
            background-image: url("~@/assets/serve/enterpriseHover.png");
          }
          .DigitalFinance-circle-icon-government {
            position: absolute;
            left: 780px;
            top: 410px;
            width: 200px; // 缩小圆形icon
            height: 200px;
            border-radius: 50%;
            margin-bottom: 6px;
            background: linear-gradient(180deg, #398fff 0%, #0ff 100%);
            background-image: url("~@/assets/serve/government.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          .DigitalFinance-circle-icon-government:hover {
            background-image: url("~@/assets/serve/governmentHover.png");
          }
          .DigitalFinance-circle-icon-org {
            position: absolute;
            left: 1220px;
            top: 360px;
            width: 200px; // 缩小圆形icon
            height: 200px;
            border-radius: 50%;
            margin-bottom: 6px;
            background: linear-gradient(180deg, #398fff 0%, #0ff 100%);
            background-image: url("~@/assets/serve/institutions.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          .DigitalFinance-circle-icon-org:hover {
            background-image: url("~@/assets/serve/institutionsHover.png");
          }
          .DigitalFinance-circle-title-enterprise {
            position: absolute;
            left: 365px;
            top: 460px;
            font-weight: 600;
            font-size: 40px;
            color: #d3f1ff;
            line-height: 56px;
            letter-spacing: 2px;
            text-align: right;
            font-style: normal;
            background: linear-gradient(90deg, #ffffff 0%, #b7deff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .DigitalFinance-circle-title-government {
            position: absolute;
            left: 815px;
            top: 510px;
            font-weight: 600;
            font-size: 40px;
            color: #d3f1ff;
            line-height: 56px;
            letter-spacing: 2px;
            text-align: right;
            font-style: normal;
            background: linear-gradient(90deg, #ffffff 0%, #b7deff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .DigitalFinance-circle-title-org {
            position: absolute;
            left: 1260px;
            top: 460px;
            font-weight: 600;
            font-size: 40px;
            color: #d3f1ff;
            line-height: 56px;
            letter-spacing: 2px;
            text-align: right;
            font-style: normal;
            background: linear-gradient(90deg, #ffffff 0%, #b7deff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .DigitalFinance-circle-desc-enterprise {
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            width: 321.19px;
            height: 80px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-align: center;
            font-style: normal;
            position: absolute;
            cursor: pointer;
            left: 270px;
            top: 550px;
          }
          .DigitalFinance-circle-desc-government {
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            width: 321.19px;
            height: 147px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-align: center;
            font-style: normal;
            cursor: pointer;
            position: absolute;
            left: 720px;
            top: 600px;
          }
          .DigitalFinance-circle-desc-org {
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            width: 321.19px;
            height: 80px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-align: center;
            font-style: normal;
            position: absolute;
            cursor: pointer;
            left: 1170px;
            top: 550px;
          }
        }
      }
    }
    .DigitalFinance-platform-section {
      display: flex;
      justify-content: center;
      gap: 36px; // 缩小间距
      margin-top: 10px;
      .DigitalFinance-platform-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        .DigitalFinance-platform-icon {
          width: 240.26px; // 缩小icon
          height: 191.09px;
          margin-bottom: 4px;
          &.DigitalFinance-model {
            background-image: url("~@/assets/serve/model.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            position: absolute;
            left: 250px;
            top: 39vh;
            cursor: pointer;
            transition: all 0.3s ease;
            &:hover {
              background-image: url("~@/assets/serve/modelHover.png");
            }
          }
          &.DigitalFinance-base {
            background-image: url("~@/assets/serve/base.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            position: absolute;
            left: 757px;
            top: 43vh;
            cursor: pointer;
            transition: all 0.3s ease;
            &:hover {
              background-image: url("~@/assets/serve/baseHover.png");
            }
          }
          &.DigitalFinance-ops {
            background-image: url("~@/assets/serve/ops.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            position: absolute;
            left: 1250px;
            top: 39vh;
            cursor: pointer;
            transition: all 0.3s ease;
            &:hover {
              background-image: url("~@/assets/serve/opsHover.png");
            }
          }
        }
        .DigitalFinance-platform-title {
          color: #b6d6ff;
          font-size: 12px; // 缩小
          margin-top: 1px;
          &.DigitalFinance-model {
            width: 269.51px;
            height: 69.28px;
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 69.28px;
            letter-spacing: 2px;
            text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
              0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
            text-align: center;
            font-style: normal;
            position: absolute;
            cursor: pointer;
            left: 240px;
            top: 48vh;
          }
          &.DigitalFinance-base {
            cursor: pointer;
            width: 269.51px;
            height: 69.28px;
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 69.28px;
            letter-spacing: 2px;
            text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
              0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 750px;
            top: 52vh;
          }
          &.DigitalFinance-ops {
            width: 269.51px;
            height: 69.28px;
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 69.28px;
            letter-spacing: 2px;
            text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
              0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 1255px;
            top: 48vh;
          }
        }
      }
    }
  }
}

/* 弹框样式 */
.DigitalFinance-popup-container {
  position: absolute;
  z-index: 2000;
  width: 423.42px;
  height: 423.42px;
  background-image: url("~@/assets/serve/serveHover.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 4px;
}
.DigitalFinance-enterprise-popup {
  top: -50px;
  left: 210px;
}

.DigitalFinance-government-popup {
  top: 0px;
  left: 660px;
}

.DigitalFinance-institutions-popup {
  top: -50px;
  left: 1100px;
}

.DigitalFinance-popup-content {
  margin-top: 70px;
  padding: 15px;
}

.DigitalFinance-popup-item {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28px;
  color: #ffffff;
  line-height: 40px;
  letter-spacing: 1px;
  text-shadow: 0px 0px 10px rgba(0, 204, 255, 0.5),
    0px 1px 3px rgba(0, 0, 0, 0.32);
  text-align: center;
  font-style: normal;
}
.DigitalFinance-model-popup {
  top: 18vh;
  left: 150px;
}
.DigitalFinance-base-popup {
  top: 22vh;
  left: 660px;
}
.DigitalFinance-ops-popup {
  top: 18vh;
  left: 1160px;
}
.DigitalFinance-popup-item:last-child {
  border-bottom: none;
}

.DigitalFinance-cube-popup {
  position: absolute;
  top: 120px;
  left: 400px;
  width: 00px;
  height: 400px;
  pointer-events: auto;
  z-index: 9999;
}

.DigitalFinance-cube-popup-col {
  position: absolute;
  height: 100%;
}

.DigitalFinance-cube-popup-col.DigitalFinance-left-col {
  position: absolute;
  left: -40px;
}

.DigitalFinance-cube-popup-col.DigitalFinance-right-col {
  position: absolute;
  right: -16vw;
}

.DigitalFinance-cube-popup-item-vertical {
  position: absolute;
  left: 0;
  right: 0;
  writing-mode: vertical-rl;

  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 25px;
  opacity: 1;
  color: #ffffff;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.33) 0%,
    rgba(255, 255, 255, 0.14) 28%,
    rgba(255, 255, 255, 0.14) 69%,
    rgba(255, 255, 255, 0.33) 100%
  );
  min-height: 60px;
  min-width: 36px;
  padding: 20px 8px;
  text-align: center;
  letter-spacing: 2px;
}
</style>
