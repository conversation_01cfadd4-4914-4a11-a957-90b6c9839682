<!--
  园区分布柱状图弹窗组件
  功能：
  1. 展示园区分布数据的柱状图
  2. 支持自动轮播展示
  3. 支持加载状态显示
  4. 支持自适应字体大小
-->
<template>
  <!-- 弹窗遮罩层 -->
  <div v-if="visible" class="dialogBar-model" @click="handleDialogClose">
    <!-- 弹窗内容区 -->
    <div class="dialogBar-content" @click.stop>
      <!-- 弹窗头部 -->
      <div class="dialogBar-header-box">
        <div class="dialogBar-header">
          <div class="dialogBar-titleBox1">
            <span class="dialogBar-title">{{ title }}</span>
          </div>
        </div>
      </div>
      <!-- 弹窗主体 -->
      <div class="dialogBar-body">
        <!-- 加载状态显示 -->
        <div v-if="loading && data.length !== 0" class="loading-container">
          <div class="loading-spinner">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
          <div class="loading-text">加载中...</div>
        </div>
        <!-- 数据为空提示 -->
        <div v-if="data.length === 0" class="no-data-container">
          <!-- <img src="@/assets/no-data.png" class="no-data-icon"/> -->
          <div class="no-data-text">暂无数据</div>
        </div>
        <!-- 图表容器 -->
        <div
          v-if="!loading && data.length > 0"
          class="dialogBar-chart"
          ref="dialogChart"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";

export default {
  name: "DialogBarPark",
  // 组件属性定义
  props: {
    visible: { type: Boolean, default: false }, // 控制弹窗显示
    data: { type: Array, default: () => [] }, // 图表数据
    title: { type: String, default: "放款企业产业园区分布" }, // 弹窗标题
    colorStops: {
      type: Array,
      default: () => [
        // 柱状图渐变色配置
        { offset: 0, color: "#16DEE1 " },
        { offset: 1, color: "#034347" },
      ],
    },
    displayCount: { type: Number, default: 12 }, // 显示数据条数
    autoPlayInterval: { type: Number, default: 3000 }, // 自动播放间隔
    loading: { type: Boolean, default: false }, // 加载状态
  },
  data() {
    return {
      dialogChart: null, // echarts实例
      dialogAutoPlayTimer: null, // 自动播放定时器
      dialogCurrentIndex: 0, // 当前显示的数据索引
      backgroundData: Array(16).fill(10), // 背景数据
    };
  },
  watch: {
    // 监听弹窗显示状态
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          if (!this.loading) {
            this.initDialogChart();
          }
        });
      } else {
        this.clearDialogChart();
      }
    },
    // data: {
    //   handler(newVal) {
    //     if (this.dialogChart && newVal.length) {
    //       this.$nextTick(() => {
    //         this.updateDialogChart();
    //       });
    //       // this.initDialogChart();
    //     }
    //   },
    //   deep: true,
    // },
    // 监听加载状态
    loading(val) {
      if (!val && this.visible) {
        this.$nextTick(() => {
          this.initDialogChart();
        });
      }
    },
    // 监听数据变化
    data: {
      handler() {
        if (this.visible && !this.loading && this.dialogChart) {
          this.$nextTick(() => {
            this.updateDialogChart();
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    // 关闭弹窗
    handleDialogClose() {
      this.$emit("close");
      this.clearDialogChart();
    },
    // 清理图表资源
    clearDialogChart() {
      if (this.dialogAutoPlayTimer) {
        clearInterval(this.dialogAutoPlayTimer);
        this.dialogAutoPlayTimer = null;
      }
      if (this.dialogChart) {
        this.dialogChart.dispose();
        this.dialogChart = null;
      }
      this.dialogCurrentIndex = 0;
    },
    // 初始化图表
    initDialogChart() {
      if (!this.$refs.dialogChart) return;
      const container = this.$refs.dialogChart;
      // 确保容器尺寸有效
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        setTimeout(() => this.initDialogChart(), 1000);
        return;
      }
      if (this.dialogChart) {
        this.dialogChart.dispose();
      }
      this.dialogChart = echarts.init(container);
      this.updateDialogChart();
      window.addEventListener("resize", this.handleDialogResize);
    },
    // 处理窗口大小变化
    handleDialogResize() {
      this.dialogChart && this.dialogChart.resize();
    },
    // 更新图表数据
    updateDialogChart() {
      if (!this.dialogChart) return;
      // 对数据进行排序
      const data = [...this.data].sort(
        (a, b) => parseFloat(b.amount) - parseFloat(a.amount)
      );
      const initialData = data.slice(0, this.displayCount);

      // 配置图表选项
      this.dialogChart.setOption({
        // 图表网格配置
        grid: {
          left: 0,
          right: 0,
          bottom: this.$autoFontSize(6),
          top: this.$autoFontSize(30),
          containLabel: true,
        },
        // 提示框配置
        tooltip: {
          show: false,
          trigger: "axis",
          axisPointer: { type: "shadow" },
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: initialData.map((item) => item.name),
          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(13),
            interval: 0,
            width: 300,
            overflow: "break",
            formatter: function (value) {
              return value.replace(/(.{6})/g, "$1\n");
            },
            margin: 20,
          },
          axisTick: { show: false },
          axisLine: {
            lineStyle: { color: "rgba(255,255,255,0.2)" },
          },
        },
        // Y轴配置
        yAxis: {
          show: false,
          type: "value",
          name: "金额（亿）",
          nameTextStyle: { color: "#fff", fontSize: 12 },
          axisLabel: { color: "#fff", fontSize: 12 },
          splitLine: { lineStyle: { color: "rgba(255,255,255,0.1)" } },
        },
        // 数据系列配置
        series: [
          // 背景柱状图
          {
            name: "全量背景图",
            type: "bar",
            barGap: "-100%",
            data: this.backgroundData,
            barWidth: this.$autoFontSize(12),
            itemStyle: { normal: { color: "rgba(63, 169, 245, 0.2)" } },
            z: 0,
          },
          // 主数据柱状图
          {
            data: initialData.map((item) => ({
              value: parseFloat(item.amount),
              count: item.count,
            })),
            type: "bar",
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: this.colorStops,
              },
              borderRadius: [0, 0, 0, 0],
            },
            // 标签配置
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return `{amount|${params.value}亿}\n{count|${params.data.count}笔}`;
              },
              rich: {
                amount: {
                  color: "#ffce7c",
                  fontSize: this.$autoFontSize(14),
                  textAlign: "center",
                  align: "center",
                  verticalAlign: "middle",
                  padding: [0, 0, this.$autoFontSize(2), 0],
                },
                count: {
                  color: "#23eaff",
                  fontSize: this.$autoFontSize(14),
                  textAlign: "center",
                  align: "center",
                  verticalAlign: "middle",
                },
              },
            },
          },
          // 散点图（用于装饰）
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "circle",
            symbolSize: [this.$autoFontSize(12), this.$autoFontSize(6)],
            itemStyle: { color: "#fff", shadowColor: "#fff" },
            z: 3,
            data: initialData.map((item, idx) => [
              idx,
              parseFloat(item.amount),
            ]),
          },
        ],
      });
      this.startDialogAutoPlay();
    },
    // 开始自动播放
    startDialogAutoPlay() {
      if (this.dialogAutoPlayTimer) {
        clearInterval(this.dialogAutoPlayTimer);
      }
      const sortedData = [...this.data].sort(
        (a, b) => parseFloat(b.amount) - parseFloat(a.amount)
      );
      this.dialogAutoPlayTimer = setInterval(() => {
        // 更新图表数据
        this.dialogChart.setOption({
          xAxis: {
            data: sortedData
              .slice(
                this.dialogCurrentIndex,
                this.dialogCurrentIndex + this.displayCount
              )
              .map((item) => item.name),
            axisLabel: { width: 300, margin: 20 },
            axisTick: { show: false },
          },
          series: [
            { name: "全量背景图", data: this.backgroundData },
            {
              data: sortedData
                .slice(
                  this.dialogCurrentIndex,
                  this.dialogCurrentIndex + this.displayCount
                )
                .map((item) => ({
                  value: parseFloat(item.amount),
                  count: item.count,
                })),
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                  return `{amount|${params.value}亿}\n{count|${params.data.count}笔}`;
                },
                rich: {
                  amount: {
                    color: "#ffce7c",
                    fontSize: this.$autoFontSize(14),
                    textAlign: "center",
                    align: "center",
                    verticalAlign: "middle",
                  },
                  count: {
                    color: "#23eaff",
                    fontSize: this.$autoFontSize(14),
                    textAlign: "center",
                    align: "center",
                    verticalAlign: "middle",
                  },
                },
              },
            },
            {
              data: sortedData
                .slice(
                  this.dialogCurrentIndex,
                  this.dialogCurrentIndex + this.displayCount
                )
                .map((item, idx) => [idx, parseFloat(item.amount)]),
            },
          ],
        });
        // 更新索引
        this.dialogCurrentIndex += 1;
        if (this.dialogCurrentIndex >= sortedData.length) {
          this.dialogCurrentIndex = 0;
        }
      }, this.autoPlayInterval);
    },
  },
  beforeDestroy() {
    this.clearDialogChart();
    window.removeEventListener("resize", this.handleDialogResize);
  },
};
</script>

<style scoped lang="scss">
/* 弹窗遮罩层样式 */
.dialogBar-model {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 弹窗内容区样式 */
.dialogBar-content {
  background: url("~@/assets/dp/dialogMax.png");
  background-size: 100% 100%;
  border-radius: 12px;
  width: 60%;
  height: 60%;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 弹窗头部样式 */
.dialogBar-header-box {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.dialogBar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 标题样式 */
.dialogBar-titleBox1 {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 150px;
  .dialogBar-title {
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
    margin-left: -40px;
    width: 100%;
  }
}

/* 弹窗主体样式 */
.dialogBar-body {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* 图表容器样式 */
.dialogBar-chart {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(0, 24, 48, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 加载状态容器样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 24, 48, 0.8);
  z-index: 1000;
}

/* 加载动画样式 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

/* 加载动画点样式 */
.dot {
  width: 20px;
  height: 20px;
  background: #16dee1;
  border-radius: 50%;
  animation: bounce 0.5s infinite alternate;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  &:nth-child(3) {
    animation-delay: 0.4s;
  }
  &:nth-child(4) {
    animation-delay: 0.6s;
  }
  &:nth-child(5) {
    animation-delay: 0.8s;
  }
}

/* 加载文字样式 */
.loading-text {
  color: #16dee1;
  font-size: 42px;
  font-family: PingFangSC, PingFang SC;
}
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.no-data-text {
  color: #909399;
  font-size: 24px;
  letter-spacing: 2px;
  line-height: 30px;
}
/* 加载动画关键帧 */
@keyframes bounce {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-10px);
  }
}
</style>
