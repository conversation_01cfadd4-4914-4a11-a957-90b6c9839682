{"name": "cdSreen", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "set NODE_ENV=production && vue-cli-service build", "build-uat": "vue-cli-service build --mode uat", "build-prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.18.0", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "element-ui": "^2.15.14", "jquery": "^3.7.1", "js-cookie": "^2.2.0", "js-md5": "^0.8.3", "jsencrypt": "^3.3.2", "nprogress": "^0.2.0", "v-scale-screen": "^1.0.0", "vue": "^2.6.14", "vue-count-to": "^1.0.13", "vue-router": "^3.5.1", "vue-seamless-scroll": "^1.1.23", "vue-spinner": "^1.0.4", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^6.1.1", "cross-env": "^7.0.3", "css-minimizer-webpack-plugin": "^3.4.1", "postcss-px-to-viewport": "^1.1.1", "postcss-px2rem": "^0.3.0", "postcss-pxtorem": "^6.1.0", "sass": "^1.89.2", "sass-loader": "^16.0.5", "terser-webpack-plugin": "^4.2.3", "vue-template-compiler": "2.6.14", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}