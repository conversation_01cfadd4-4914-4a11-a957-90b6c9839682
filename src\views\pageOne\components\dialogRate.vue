<!--
  平均放款利率趋势弹窗组件
  功能：
  1. 展示平台利率和LPR利率的趋势对比
  2. 支持月度和年度数据切换
  3. 提供折线图可视化展示
  4. 支持图表交互和提示
-->
<template>
  <!-- 使用Element UI的Dialog组件作为基础弹窗 -->
  <el-dialog
    :visible.sync="localVisible"
    :show-close="false"
    :close-on-click-modal="true"
    :modal-append-to-body="false"
    class="rate-dialog"
    width="40%"
  >
    <!-- 头部区域 -->
    <div class="header-box">
      <!-- 标题区域 -->
      <div class="titleBox">
        <span class="title">平均放款利率趋势</span>
      </div>
      <!-- 切换按钮组 -->
      <div class="tab-group">
        <button
          v-for="tab in ['month', 'year']"
          :key="tab"
          :class="['tab-btn', { active: currentTab === tab }]"
          @click="changeTab(tab)"
        >
          {{ tab === 'year' ? '年' : '月' }}
        </button>
      </div>
    </div>
    <!-- 图表容器 -->
    <div ref="chartRef" class="chart"></div>   
  </el-dialog>
</template>

<script>
import * as echarts from "echarts";
import {queryLineChartData, productUpdateList} from "@/api/article.js";

export default {
  name: "DialogRate",
  props: {
    // 控制弹窗显示
    visible: { type: Boolean, default: false },
    // 最大日期
    maxDt: { type: String, default: "" },
  },
  data() {
    return {
      localVisible: false, // 本地弹窗显示状态
      currentTab: "month", // 当前选中的标签
      chart: null, // ECharts实例
      // 图表数据配置
      chartData: {
        // 月度数据
        month: {
          x: ["202405", "202406", "202407", "202408", "202409", "202410", "202411", "202412", "202501", "202502", "202503", "202504"],
          platformRate: [4.08, 4.08, 4.00, 4.00, 3.85, 3.85, 3.85, 3.65, 3.65, 3.45, 3.45, 3.45],
          LPRRate: [3.95, 3.85, 3.85, 3.85, 3.60, 3.60, 3.60, 3.60, 3.60, 3.60, 3.60, 3.50],
        },
        // 年度数据
        year: {
          x: ["2023", "2024", "2025"],
          platformRate: [4, 4.05, 3.97],
          LPRRate: [3.85, 3.60],
        },
      },
    };
  },
  watch: {
    // 监听visible属性变化
    visible(val) {
      this.localVisible = val;
      if (val) {
        this.$nextTick(() => {
          setTimeout(this.initChart, 100);
        });
      } else {
        if (this.chart) {
          this.chart.dispose();
          this.chart = null;
        }
      }
    },
    // 监听localVisible属性变化
    localVisible(val) {
      if (val !== this.visible) {
        this.$emit("update:visible", val);
      }
    },
  },
  mounted() {
    this.localVisible = this.visible;
    // 初始化LPR数据
    this.lprMonth();
    this.lprYear();
  },
  methods: {
    // 获取年度LPR利率数据
    lprYear() {
      productUpdateList([{    
        dimTime: "年",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "LPR_1Y利率"
      }]).then(res => {
        let arr = [];
        res.data.data.forEach(item => {
          arr.push(item.indexValue);
        });
        this.chartData.year.LPRRate = arr;
      });
    },
    // 获取月度LPR利率数据
    lprMonth() {
      productUpdateList([{    
        dimTime: "月",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "LPR_1Y利率"
      }]).then(res => {
        this.changeTab('month');
        let arr = [];
        res.data.data.forEach(item => {
          arr.push(item.indexValue);
        });
        // 截取最近12个月的数据
        arr = arr.slice(-12);
        this.chartData.month.LPRRate = arr;
      });
    },
    // 切换标签页
    async changeTab(tab) {
      this.currentTab = tab;
      const today = new Date();
      const year = today.getFullYear();
      // 获取当前月份
      let month = String(today.getMonth() + 1).padStart(2, "0");
      month = `${year}${month}`;
      // 获取当前日期
      let day = String(today.getDate()).padStart(2, "0");
      day = `${year}${month}${day}`;
      
      let bizDate = '';
      let typeTab = '';
      
      // 根据标签类型设置查询参数
      if (tab === "日") {
        bizDate = day;
      } else if (tab === "month") {
        bizDate = month;
        typeTab = '月';
      } else if (tab === "year") {
        bizDate = year;
        typeTab = '年';
      }
      
      // 获取利率数据
      const res = await queryLineChartData({
        bizDate: `${bizDate}`,
        type: typeTab,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "平均融资利率",
      }); 
      
      this.$nextTick(() => {
        // 更新图表数据
        if(tab === 'year') {
          this.chartData.year.x = res.data.data.dataList;
          this.chartData.year.platformRate = res.data.data.countList;
        } else {
          this.chartData.month.x = res.data.data.dataList;
          this.chartData.month.platformRate = res.data.data.countList;
        }
        this.initChart();
      });
    },
    
    // 初始化图表
    initChart() {
      this.chart = echarts.init(this.$refs.chartRef);
      this.updateChart();
    },
    
    // 更新图表配置和数据
    updateChart() {
      if (!this.chart) return;
      const data = this.chartData[this.currentTab];
      
      // 创建标签格式化函数
      const createLabelFormatter = (isMonthView) => {
        return (params) => {
          const val = Number(params.value).toFixed(2);
          if (isMonthView) {
            // 月度视图：只在偶数索引显示标签
            return params.dataIndex % 2 === 0 ? val + '%' : '';
          } else {
            // 年度视图：显示所有标签
            return val + '%';
          }
        };
      };
      
      // 图表配置项
      const option = {
        // 提示框配置
        tooltip: { 
          trigger: "axis",
          backgroundColor: 'rgba(58, 139, 255, 0.2)',
          borderColor: '#0a4d8f',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#3A8BFF',
              width: 2
            }
          }
        }, 
        // 图例配置
        legend: {
          data: ["平台利率", "LPR利率"],
          textStyle: { color: "#fff", fontSize: this.$autoFontSize(14) },
          right: 40,
          top: 30,
        },
        // 图表网格配置
        grid: { 
          left: this.$autoFontSize(50), 
          right: this.$autoFontSize(40), 
          top: this.$autoFontSize(100), 
          bottom: this.$autoFontSize(40) 
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: this.currentTab === "month" ? data.x : data.x,
          axisLabel: { color: "#fff", fontSize: this.$autoFontSize(14) },
        },
        // Y轴配置
        yAxis: {
          type: "value",
          name: "单位：%",
          nameTextStyle: { 
            color: "#fff",
            marginRight: 30,
            fontSize: this.$autoFontSize(14),
            padding: [0, this.$autoFontSize(20), this.$autoFontSize(30), 0]
          },
          axisLabel: { 
            color: "#fff",
            marginRight: 30,
            fontSize: this.$autoFontSize(14)
          },
        },
        // 数据系列配置
        series: [
          {
            name: "平台利率",
            type: "line",
            data: data.platformRate,
            itemStyle: { color: "#3A8BFF" },
            lineStyle: { width: 2 },
            symbol: 'circle',
            symbolSize: 8,
            label: {
              show: true,
              position: 'top',
              formatter: createLabelFormatter(this.currentTab === 'month'),
              color: '#fff',
              fontSize: this.$autoFontSize(14),
              borderRadius: 4,
              padding: [this.$autoFontSize(12), this.$autoFontSize(14)],
              distance: -8
            },
            emphasis: {
              itemStyle: {
                borderWidth: 3,
                borderColor: '#fff',
                shadowBlur: 10
              }
            }
          },
          {
            name: "LPR利率",
            type: "line",
            data: data.LPRRate,
            itemStyle: { color: "orange" },
            lineStyle: { width: 2 },
            symbol: 'circle',
            symbolSize: 8,
            label: {
              show: true,
              position: 'top',
              formatter: createLabelFormatter(this.currentTab === 'month'),
              color: '#fff',
              borderRadius: 4,
              fontSize: this.$autoFontSize(14),
              padding: [-this.$autoFontSize(32), 2],
              distance: 10
            },
            emphasis: {
              itemStyle: {
                borderWidth: 3,
                borderColor: '#fff',
                shadowBlur: 10
              }
            }
          },
        ],
      };
      this.chart.setOption(option, true);
      this.chart.resize();
    },
  },
};
</script>

<style lang="scss" scoped>
/* 弹框样式 */
::v-deep .el-dialog {
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  right: 0;
  margin: auto;
  background: url("~@/assets/dp/dialog.png");
  background-size: 100% 100%;
  border: 0.05208vw solid #6ec6ff;
  height: 50%;
}

/* 标签组样式 */
.tab-group {
  display: flex;
  justify-content: flex-end;
  margin-top: -69px;
  height: 36px;
  position: relative;
}

/* 标签按钮样式 */
.tab-btn {
  font-family: PingFangSC;
  font-weight: 400;
  font-size: 34px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  width: 140px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  color: #67a7ff;
  border: 1px solid #4ea6ff;
  cursor: pointer;
  margin-left: -1px;
  transition: background 0.2s, color 0.2s;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  );
  box-shadow: inset 0px 0px 18px 0px #168aff;
  border: 2px solid rgba(110, 160, 227, 0.8);
}

/* 激活状态的标签按钮样式 */
.tab-btn.active {
  background: #2176c7;
  color: #fff;
  border: 1px solid #2176c7;
  z-index: 1;
}

/* 图表容器样式 */
.chart {
  width: 100%;
  height: 40vh;
}

/* 头部区域样式 */
.header-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 00px 24px 0 24px;
}

/* 标题区域样式 */
.titleBox {
  margin-top: -30px;
  background-image: url("~@/assets/dp/headerbg.png");
  font-family: YouSheBiaoTiHei;
  background-size: 940px 89px;
  background-repeat: no-repeat;
  width: 940px;
  height: 89px;
  display: flex;
  align-items: center;
  .title {
    padding-left: 100px;
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    letter-spacing: 6px;
    text-shadow: 0px 0px 4px rgba(65, 158, 255, 0.89);
    text-align: left;
    font-style: normal;
  }
}
</style>
