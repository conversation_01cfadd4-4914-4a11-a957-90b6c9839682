<template>
    <div class="fwstjjClass">
      <div style="">
        <div class="headerImg">
           <div class="lbClass">模型应用</div>
          <div class="rightClass">
            <div class="djgdClass" @click="openDialogModel" >点击查看更多</div>
            <div>
              <img src="@/assets/dp/rightT.png" class="imgRight" />
            </div>
          </div>
        </div>

      </div>
    <div class="bottomClass">
      
  <div class="apply-model-container">
    <!-- 上方三个按钮 -->
    <div class="top-buttons">
      <div class="model-button" @click="selectModel('special')">
        <div class="button-text">专精特新贷款</div>
        <div class="arrow-up-group">
          <div class="arrow-up arrow-up-1"></div>
          <div class="arrow-up arrow-up-2"></div>
          <div class="arrow-up arrow-up-3"></div>
        </div>
      </div>
      <div class="model-button" @click="selectModel('oil')">
        <div class="button-text">川渝油气贷</div>
        <div class="arrow-up-group">
          <div class="arrow-up arrow-up-1"></div>
          <div class="arrow-up arrow-up-2"></div>
          <div class="arrow-up arrow-up-3"></div>
        </div>
      </div>
      <div class="model-button" @click="selectModel('merchant')">
        <div class="button-text">商户e贷</div>
        <div class="arrow-up-group">
          <div class="arrow-up arrow-up-1"></div>
          <div class="arrow-up arrow-up-2"></div>
          <div class="arrow-up arrow-up-3"></div>
        </div>
      </div>
    </div>

    <!-- 中间行业数据按钮 -->
    <div class="data-button industry-data" @click="selectDataType('industry')">
      <div class="data-text">行业数据</div>
    </div>

    <!-- 加号连接器 -->
    <div class="connector">
      <div class="plus-icon">+</div>
    </div>

    <!-- 底部通用数据按钮 -->
    <div class="data-button general-data" @click="selectDataType('general')">
      <div class="data-text" >通用数据</div>
    </div>
  </div>
  </div>
  <dialogModel ref="dialogModel"  :visible="visible" @close="closeDialogModel" />
  </div>
</template>

<script>
import dialogModel from './dialogModel.vue';
export default {
  name: 'ApplyModel',
  data() {
    return {
      selectedModel: '',
      selectedDataType: '',
      visible: false
    }
  },
  components: {
    dialogModel
  },
  methods: {
    openDialogModel() {
      this.visible = true;
    },  
    closeDialogModel() {
      this.visible = false;
    },
    selectModel(model) {
      this.selectedModel = model;
      // 可以在这里添加选择模型后的逻辑
      this.$emit('modelSelected', model);
    },
    selectDataType(type) {
      this.selectedDataType = type;
      // 可以在这里添加选择数据类型后的逻辑
      this.$emit('dataTypeSelected', type);
    }
  }
}
</script>

<style scoped lang="scss">
.fwstjjClass{
  width: 940px;
  height: 26vh;
  display: flex;
  flex-direction: column;
  .headerImg{
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image:url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #FFFFFF;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0,175,255,0.68), 0px 2px 4px #000C1A;
    display: flex;
    justify-content: space-between;
  }
  .rightClass {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -80px;
    .djgdClass {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26px;
      color: #77c1ff;
      letter-spacing: 1px;
      cursor: pointer;
      margin-right: 12px;
    }
    .imgRight {
      width: 12px;
      height: 22px;
      position: relative;
      top: -1px;
    }
  }
  .bottomClass{
    width: 100%;
    flex: 1;
    background: linear-gradient( 180deg, rgba(3,29,58,0) 0%, rgba(0,50,107,0.64) 100%);
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(169deg, rgba(44, 110, 162, 0), rgba(47, 115, 169, 1)) 2 2;
    backdrop-filter: blur(7px);
    display: flex;
    justify-content: center;
    align-items: center;
.apply-model-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width:   854px;

}

.top-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 60px;
}

.model-button{
width: 268px;
height: 69px;
background: linear-gradient( 270deg, rgba(87,152,255,0.12) 0%, rgba(87,152,255,0.07) 50%, rgba(87,151,255,0.12) 100%);
box-shadow: inset 0px 0px 12px 0px rgba(87,151,255,0.58);
border: 1px solid rgba(87,151,255,0.63);
font-family: PingFangSC, PingFang SC;
font-weight: 400;
font-size: 28px;
color: #FFFFFF;
line-height: 69px;
letter-spacing: 1px;
text-align: center;

}

.button-text {
    background: linear-gradient( 270deg, rgba(87,152,255,0.12) 0%, rgba(87,152,255,0.07) 50%, rgba(87,151,255,0.12) 100%);
box-shadow: inset 0px 0px 12px 0px rgba(87,151,255,0.58);
border: 1px solid rgba(87,151,255,0.63);
}
.arrow-up-group {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 60px;
  justify-content: flex-start;
}

.arrow-up {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #fff;
  filter: drop-shadow(0 0 3px #fff);
margin-top: 3px;
  opacity: 0.7;
}

.arrow-up-1 {
  opacity: 1;
  border-bottom-color: #fff;
}

.arrow-up-2 {
  opacity: 0.6;
  border-bottom-color: #fff;
}

.arrow-up-3 {
  opacity: 0.3;
  border-bottom-color: #fff;
}
.arrow-down {
  margin-top: 8px;
  display: flex;
  justify-content: center;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #29FCFF;
  filter: drop-shadow(0 0 3px #29FCFF);
}

.data-button {
  width: 854px;
  height: 69px;
  line-height: 69px;
  background-color: rgba(0, 51, 102, 0.8);
  color: #fff;
  border-radius: 4px;
  text-align: center;
  margin: 10px 0;
  cursor: pointer;
  font-family: PingFangSC, PingFang SC;
font-weight: 400;
font-size: 28px;
color: #FFFFFF;
letter-spacing: 1px;
text-align: center;
background: linear-gradient( 270deg, rgba(87,152,255,0.12) 0%, rgba(87,152,255,0.07) 50%, rgba(87,151,255,0.12) 100%);
box-shadow: inset 0px 0px 12px 0px rgba(87,151,255,0.58);
border: 1px solid rgba(87,151,255,0.63);
}

.data-text {
  font-size: 28px;
}

.connector {
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.plus-icon {
  width: 30px;
  height: 30px;
  color: #fff;
  background: none;
  border-radius: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 52px;
  font-weight: bold;
  box-shadow: none;
}

/* 添加悬停效果 */
.model-button:hover .button-text,
.data-button:hover {
    background: linear-gradient( 270deg, rgba(87,152,255,0.12) 0%, rgba(87,152,255,0.07) 50%, rgba(87,151,255,0.12) 100%);
box-shadow: inset 0px 0px 12px 0px rgba(87,151,255,0.58);
border: 1px solid rgba(87,151,255,0.63);
}
  }
}
</style>