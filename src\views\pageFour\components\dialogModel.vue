<template>
  <div v-if="visible" class="dialogModel" @click="handleMaskClick">
    <div class="dialog-content" @click.stop>
      <div class="header-box">
        <div class="dialog-header">
          <div class="titleBox">
            <span class="title">模型应用</span>
          </div>
        </div>
        <div class="tab-group"></div>
      </div>
      <div class="dialog-body"> 
        <div class="data-structure-dialog-body">
          <!-- 第一层：贷款产品 -->
          <div class="loan-products">
            <div class="loan-product-group" v-for="(group, idx) in loanProducts" :key="idx">
              <div class="loan-product-title">{{ group.title }}</div>
              <div class="loan-product-bank" v-for="bank in group.banks" :key="bank">{{ bank }}</div>
            </div>
          </div>
          <!-- 箭头 -->
          <div class="arrow-row">
            <div v-for="i in loanProducts.length" :key="i" class="arrow"></div>
          </div>
          <!-- 第二层：行业数据 -->
          <div class="industry-data">
            <div class="industry-data-title">行业数据</div>
            <div class="industry-data-list">
              <div class="industry-data-item" v-for="item in industryData" :key="item">{{ item }}</div>
            </div>
          </div>
          <!-- 箭头 -->
          <div class="arrow-row-1">
<!--             <div v-for="i in 6" :key="i" class="arrow" style="height: 40px;line-height: 40px;"></div> -->
          </div>
          <!-- 第三层：通用数据 -->
          <div class="general-data">
            <div class="general-data-title">通用数据</div>
            <div class="general-data-list">
              <div class="general-data-item" v-for="item in generalData" :key="item">{{ item }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EchartsDialogLoop",
  props: {
    visible: { type: Boolean, default: false },
  },
  data() {
    return {
      maxDt: localStorage.getItem("maxDt"),
      chart: null,
      loanProducts: [
        { title: "专精特新贷款", banks: ["成都农商银行"] },
        { title: "川渝油气贷", banks: ["成都银行"] },
        { title: "川渝油气贷", banks: ["中国银行", "农业银行", "工商银行"] },
        { title: "商户e贷", banks: ["农业银行"] },
        { title: "天府粮仓贷", banks: ["平安银行"] },
        { title: "惠蓉e贷", banks: ["天府银行"] },
      ],
      industryData: [
        "粮票数据", "科创专利数据", "油气数据", "商户流水数据", "土地流转数据", "农业保险数据"
      ],
      generalData: [
        "企业基本信息", "公积金缴纳", "纳税信息", "知识产权", "司法信息", "经营信息", "水电气信息", "社保", "……"
      ]
    };
  },

  methods: {
    /*     async init(){
      const res = await productUpdate({
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 0,
        dimIndustryChain: "ALL",
        indexName: "累计放款金额",
      });
      console.log(res);
    }, */
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    handleMaskClick() {
      this.close();
    },
  },
  mounted() {
    
    if (this.visible) {
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
}
</script>

<style scoped lang="scss">
.dialogModel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.titleBox {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  .title {
    padding-left: 50px;
    width: 314px;
    height: 89px;
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    margin-left: 50px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
  }
}
.dialog-content {
  background: url("../assets/modelBg.png") no-repeat center center;
  background-size: 1930.79px 1152.11px;
  border: 2px solid #6ec6ff;
  border-radius: 12px;
  width: 1930.79px;
  height: 1152.11px;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
}
.header-box {
  display: flex;
}
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 24px 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}
.tab-group {
  display: flex;
  margin-left: 300px;
  margin-right: 30px;
  margin-top: 30px;
  height: 36px;
  position: relative;
}
.tab-btn {
  font-family: PingFangSC;
  font-weight: 400;
  font-size: 34px;
  line-height: 47px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  width: 140px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  color: #67a7ff;
  border: 1px solid #4ea6ff;
  cursor: pointer;
  margin-left: -1px;
  transition: background 0.2s, color 0.2s;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  );
  box-shadow: inset 0px 0px 18px 0px #168aff;
  border: 2px solid rgba(110, 160, 227, 0.8);
}
.tab-btn.active {
  background: #2176c7;
  color: #fff;
  border: 1px solid #2176c7;
  z-index: 1;
}
.dialog-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 60px;
  position: absolute;
  top: -100px;
  right: 0;
  padding: 0;
  cursor: pointer;
}
.dialog-body {
  padding: 74px 96px 0 96px;
}
.dialog-footer {
  padding: 10px 24px 18px 24px;
  text-align: right;
}
.data-structure-dialog-body {
  color: #fff;
  font-family: PingFangSC, PingFang SC;
  padding: 20px 0;
}
.loan-products {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}
.loan-product-group {
  background: url("../assets/modelbg1.png") ;
  background-size: 248.07px 231px;
  width: 248.07px;
  height:231px;
  border-radius: 8px;
  text-align: center;

}
.loan-product-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28px;
  color: #FFFFFF;
  letter-spacing: 1px;
  text-align: center;
  font-style: normal;
  line-height: 60px;
  height: 60px;
  margin-bottom: 50px;
}
.loan-product-bank {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24px;
  color: #FFD26A;
  line-height: 33px;
  letter-spacing: 1px;
  text-align: center;
  font-style: normal;
}
.arrow-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.arrow-row-1 {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 44px;
}
.arrow {
    background: url("../assets/fontUp.png") no-repeat center center;
    background-size: 32px 49.14px;
    width: 32px;
    height: 115px;
    line-height: 115px;
}
.industry-data {
  background: url("../assets/modelbg2.png") no-repeat center center;
  background-size: 1771px 218px;
  width: 1771px;
  height:218px;
  border-radius: 8px;
  margin-bottom: 10px;
}
.industry-data-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28px;
  color: #FFFFFF;
  line-height: 70px;
  height: 70px;
  letter-spacing: 1px;
  text-align: center;
  font-style: normal;
}
.industry-data-list {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  margin-top: 40px;
}
.industry-data-item {
  min-width: 110px;
  margin: 4px 10px;
  padding: 16px 27px;
  background: rgba(65,204,255,0.08);
  border-radius: 6px;
  text-align: center;
  font-family: PingFangSC, PingFang SC;
font-weight: 400;
font-size: 24px;
color: #63FF8C;
line-height: 33px;
letter-spacing: 1px;
text-align: right;
font-style: normal;
border: 1px solid #63FF8C;
}
.general-data {
  background: url("../assets/modelbg3.png");
  background-size: 1771px 184px;
  width: 1771px;
  height:184px;
  border-radius: 8px;
  padding: 10px 0;
}
.general-data-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28px;
  color: #FFFFFF;
  height: 60px;
  line-height: 60px;
  letter-spacing: 1px;
  text-align: center;
  font-style: normal;
}
.general-data-list {
  width: 1771px;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-around;
  margin-top: 40px;
  padding: 0 10px;
}
.general-data-item {
  margin-left: 57px;
  margin-right: 57px;
  min-width: 110px;
  font-size: 24px;
  margin: 4px 10px;
  color: #3CE1FF;
  text-align: center;
}
</style>
