<!--
  成都市地图组件
  功能：
  1. 展示成都市各区县地图
  2. 支持区县数据展示和交互
  3. 提供数据下钻功能
  4. 支持地图高亮和提示框
-->
<template>
  <div class="mapClass">
    <!-- 地图容器 -->
    <div ref="chartRef" style="width: 100%; height: 100%" v-if="flag"></div>
    <div ref="chartRef2" style="width: 100%; height: 100%" v-else></div>
    <!--     <div class="shuangliu-tooltip" v-if="showShuangliuTooltip">
      <div class="tkClass">
        <div class="tkName">锦江区</div>
        <div class="tkBody">
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              累计认证企业:{{ shuangliuTooltipData.enterprises }}家
            </div>
          </div>
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              放款金额:{{ shuangliuTooltipData.loanAmount }}亿
            </div>
          </div>
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              放款户数:{{ shuangliuTooltipData.loanHouseholds }}户
            </div>
          </div>
          <div class="tkItemClass">
            <div class="cir"></div>
            <div class="cirRight">
              平均利率:{{ shuangliuTooltipData.averageRate }}%
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import geoJsonData from "../img/cds.json";
import geoJsonDJYSData from "../img/djys.json"; //都江堰市
import geoJsonPZSData from "../img/pzs.json"; //彭州市
import geoJsonQZData from "../img/qzs.json"; // 邛崃市
import geoJsonJJData from "../img/jjq.json"; // 锦江区
import geoJsonQYData from "../img/qyq.json"; // 青羊区
import geoJsonJNData from "../img/jnq.json"; // 金牛区
import geoJsonWHData from "../img/whq.json"; // 武侯区
import geoJsonCHData from "../img/chq.json"; // 成华区
import geoJsonLQData from "../img/lqyq.json"; // 龙泉驿区
import geoJsonQBYData from "../img/qbjq.json"; // 青白江区
import geoJsonXJData from "../img/xdq.json"; // 新都区
import geoJsonWJData from "../img/wjq.json"; // 温江区
import geoJsonSLData from "../img/slq.json"; // 双流区
import geoJsonPXData from "../img/pdq.json"; // 郫都区
import geoJsonJYData from "../img/jys.json"; // 简阳市
import geoJsonDYData from "../img/dyx.json"; // 大邑县
import geoJsonPJData from "../img/pjx.json"; // 蒲江县
import geoJsonXJXData from "../img/xjq.json"; // 新津区
import geoJsonJTXData from "../img/jtx.json"; // 金堂县
import geoJsonCLData from "../img/czs.json"; // 崇州市

export default {
  name: "map_base",
  data() {
    return {
      twoMap: null,
      flag: true,
      rotationAngle: 10,
      cdList: [],
      chartInstance: null,
      chartInstance2: null,
      mapName: "",
      cityName: "",
      districtMapDot: {
        点1: [104.45, 30.55], // 西北
        点2: [104.5, 30.35], // 西南
        点3: [104.6, 30.6], // 北部
        点4: [104.75, 30.5], // 东北
        点5: [104.8, 30.4], // 东部
        点6: [104.7, 30.3], // 东南
        点7: [104.55, 30.25], // 南部
        点8: [104.65, 30.45], // 中部偏东
      }, //区的点坐标集合
      centerDot: [104.547, 30.413], // 简阳市中心点
      showCustomTooltip: false,
      tooltipContent: "",
      tooltipPosition: { x: 0, y: 0 },
      randomData: {},
      showShuangliuTooltip: false,
      dimCounty: "锦江区",
      shuangliuTooltipData: {
        name: "锦江区",
        dimCounty: "锦江区",
        enterprises: 7883,
        loanAmount: 20.51,
        loanHouseholds: 924,
        averageRate: 4.31,
      },
    };
  },
  props: {
    maxDt: {
      type: String,
      required: true,
    },
    isMaxDtReady: {
      type: Boolean,
      required: true,
    },
  },
  mounted() {
    this.initChart();
    this.getmap();
    // 初始化时显示锦江区的弹框
    setTimeout(() => {
      this.showShuangliuTooltip = true;
    }, 100);
  },
  methods: {
    async getmap() {
      let obj = [
        {
          indexName: "累计认证企业",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 2,
        },
        {
          indexName: "累计放款户数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 2,
        },
        {
          indexName: "累计放款金额",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 2,
        },
        {
          indexName: "平均融资利率",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 2,
        },
      ];
      await productUpdateList(obj).then((res) => {
        this.cdList = res.data.data;
        console.log("值", this.cdList);
        this.initChart();
        this.shuangliuTooltipData.enterprises =
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "累计认证企业"
            );
          }) &&
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "累计认证企业"
            );
          }).indexValue;
        this.shuangliuTooltipData.loanAmount =
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "累计放款金额"
            );
          }) &&
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "累计放款金额"
            );
          }).indexValue;
        this.shuangliuTooltipData.loanHouseholds =
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "累计放款户数"
            );
          }) &&
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "累计放款户数"
            );
          }).indexValue;
        this.shuangliuTooltipData.averageRate =
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "平均融资利率"
            );
          }) &&
          this.cdList.find((item) => {
            return (
              item.dimCounty == "锦江区" && item.indexName == "平均融资利率"
            );
          }).indexValue;
      });
    },
    // 添加显示锦江区弹框的方法
    showShuangliuPopup() {
      this.showShuangliuTooltip = true;
    },

    // 隐藏锦江区弹框的方法
    hideShuangliuPopup() {
      this.showShuangliuTooltip = false;
    },

    showJinjiangTooltip(params) {
      // 这里直接调用 tooltip 的 formatter 逻辑
      const districtData = this.getDistrictData(params.name);
      // 通过 ECharts 的 dispatchAction 显示 tooltip
      if (this.chartInstance) {
        this.chartInstance.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          name: params.name,
        });
      }
    },
    backToChengdu() {
      this.flag = true;
      this.twoMap = null;
      // 这里会自动触发watch，重新加载成都市地图
    },
    generateRandomNumber(min = 5000, max = 8000) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    // 获取区域数据
    getDistrictData(districtName) {
      // 如果该区域没有数据，则生成随机数据并缓存
      if (!this.randomData[districtName]) {
        this.randomData[districtName] = {
          enterprises: this.generateRandomNumber(5000, 8000),
          loanAmount: (Math.random() * 20 + 5).toFixed(2),
          loanHouseholds: this.generateRandomNumber(800, 1500),
          averageRate: (Math.random() * 2 + 3).toFixed(2),
        };
      }
      return this.randomData[districtName];
    },
    async initChart() {
      if (!this.$refs.chartRef) return;
      this.chartInstance = null;
      if (this.chartInstance) {
        this.chartInstance.dispose();
        this.chartInstance = null;
      }
      if (this.chartInstance2) {
        this.chartInstance2.dispose();
        this.chartInstance2 = null;
      }
      try {
        // 名称归一化函数
        const normalize = (str) =>
          (str || "").replace(/\s+/g, "").toLowerCase().trim();
        // 生成分级色块地图数据（自动遍历geoJsonData.features，确保每个区块都有value）
        let mapData = [];
        if (this.flag && geoJsonData && geoJsonData.features) {
          mapData = geoJsonData.features.map((f) => {
            const name =
              f.properties &&
              (f.properties.name ||
                f.properties.NAME ||
                f.properties.fullname ||
                f.properties.adcode_name);
            const normName = normalize(name);
            const item = this.cdList.find(
              (t) =>
                normalize(t.dimCounty) === normName &&
                t.indexName === "累计放款金额"
            );
            return {
              name: name,
              value: item ? Number(item.indexValue) : 0,
            };
          });
        }
        // 计算最大值和最小值
        let maxValue = 100;
        let minValue = 0;
        if (mapData.length > 0) {
          const values = mapData.map((item) => item.value || 0);
          maxValue = Math.max(...values);
          minValue = Math.min(...values);
          // 处理所有值都为0的情况
          if (maxValue === 0) {
            maxValue = 1; // 设置非零值确保颜色显示
          }
        }
        // 调试输出
        console.log("mapData", mapData);
        console.log("maxValue", maxValue);
        // 注册地图
        echarts.registerMap(this.mapName, geoJsonData);
        // 初始化 ECharts 实例
        this.chartInstance = echarts.init(this.$refs.chartRef);
        // 配置地图
        // 生成所有飞线和点数据
        // 需要在地图上显示的点
        const scatterData = [];
        const option = {
          geo: [
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false, // 是否允许缩放
              zoom: 1.2, // 默认显示级别
              layoutSize: "95%",
              layoutCenter: ["50%", "50%"],
              selectedMode: true,
              label: {
                show: true,
                color: "#fff",
                fontSize: this.$autoFontSize(14),
                fontWeight: "bold",
                position: "right",
                offset: [0, 0],
                verticalAlign: "middle",
                align: "right",
              },
              itemStyle: {
                areaColor: {
                  type: "linear-gradient",
                  x: 0,
                  y: 200,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(37,108,190,0.3)", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(15,169,195,0.3)", // 50% 处的颜色
                    },
                  ],
                  global: true, // 缺省为 false
                },
                borderColor: "#4ecee6",
                borderWidth: 1,
              },
              emphasis: {
                itemStyle: {
                  areaColor: {
                    type: "linear-gradient",
                    x: 0,
                    y: 300,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: "rgba(37,108,190,0.8)",
                      },
                      {
                        offset: 1,
                        color: "rgba(15,169,195,0.8)",
                      },
                    ],
                    global: true,
                  },
                  borderWidth: 2,
                  shadowBlur: 20,
                  shadowColor: "rgba(0,218,255,0.5)",
                },
                label: {
                  show: true,
                  color: "#fff",
                  fontSize: this.$autoFontSize(15),
                  fontWeight: "bold",
                },
              },
              zlevel: 3,
            },
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false, // 是否允许缩放
              zoom: 1.2, // 默认显示级别
              layoutSize: "95%",
              layoutCenter: ["50%", "50%"],
              itemStyle: {
                borderColor: "rgba(192,245,249,.6)",
                borderWidth: 2,
                shadowColor: "#2C99F6",
                shadowOffsetY: 0,
                shadowBlur: 120,
                areaColor: "rgba(29,85,139,.2)",
              },
              zlevel: 2,
              silent: true,
            },
            {
              map: this.mapName,
              aspectScale: 0.9,
              roam: false, // 是否允许缩放
              zoom: 1.2, // 默认显示级别
              layoutSize: "95%",
              layoutCenter: ["50%", "51.5%"],
              itemStyle: {
                // areaColor: '#005DDC',
                areaColor: "rgba(0,27,95,0.4)",
                borderColor: "#004db5",
                borderWidth: 1,
              },
              zlevel: 1,
              silent: true,
            },
          ],
          tooltip: {
            show: true,
            trigger: "item", // 触发类型为 'item'，表示鼠标移入地图上的某个区域时显示
            formatter: (params) => {
              params.name = params.name.replace(/\s+/g, "");
              if (this.dimCounty !== params.name) {
                this.dimCounty = params.name;
                this.shuangliuTooltipData.enterprises =
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "累计认证企业"
                    );
                  }) &&
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "累计认证企业"
                    );
                  }).indexValue;
                this.shuangliuTooltipData.loanAmount =
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "累计放款金额"
                    );
                  }) &&
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "累计放款金额"
                    );
                  }).indexValue;
                this.shuangliuTooltipData.loanHouseholds =
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "累计放款户数"
                    );
                  }) &&
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "累计放款户数"
                    );
                  }).indexValue;
                this.shuangliuTooltipData.averageRate =
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "平均融资利率"
                    );
                  }) &&
                  this.cdList.find((item) => {
                    return (
                      item.dimCounty == params.name &&
                      item.indexName == "平均融资利率"
                    );
                  }).indexValue;
              }
              // 自定义浮窗内容
              return `
              <div class="tkClass">
                <div class="tkName">${params.name}</div>
                <div class="tkBody">
                  <div class="tkItemClass">
                    <div class="cir"></div>
                    <div class="cirRight" style='font-size:${this.$autoFontSize(
                      14
                    )}px'>累计认证企业:${
                this.shuangliuTooltipData.enterprises
              }家</div>
                  </div>
                  <div class="tkItemClass">
                    <div class="cir"></div>
                    <div class="cirRight" style='font-size:${this.$autoFontSize(
                      14
                    )}px'>放款金额:${
                this.shuangliuTooltipData.loanAmount
              }亿</div>
                  </div>
                  <div class="tkItemClass">
                    <div class="cir"></div>
                    <div class="cirRight" style='font-size:${this.$autoFontSize(
                      14
                    )}px'>放款户数:${
                this.shuangliuTooltipData.loanHouseholds
              }户</div>
                  </div>
                  <div class="tkItemClass">
                    <div class="cir"></div>
                    <div class="cirRight">平均利率:${
                      this.shuangliuTooltipData.averageRate
                    }%</div>
                  </div>
                </div>
              </div>`;
            },
            backgroundColor: "rgba(0,0,0,0.7)", // 背景颜色
            borderColor: "#fff", // 边框颜色
            borderWidth: 1, // 边框宽度
            padding: 10, // 内边距
            marginTop: -30, // 外边距
          },
          series: [
            {
              type: "map",
              map: this.mapName,
              data: mapData,
              geoIndex: 0,
              roam: false,
              selectedMode: false,
              itemStyle: {
                borderColor: "#2ab8ff",
                borderWidth: 1.5,
                // areaColor: "#12235c",
              },
              emphasis: {
                itemStyle: {
                  areaColor: "rgba(224, 247, 255, 0.1)", // 设置为透明色
                  borderWidth: 4,
                  shadowBlur: 30,
                },
              },
              label: {
                show: true,
                fontSize: this.$autoFontSize(15),
                formatter: (params) => {
                  if (params.name === "崇州市") {
                    return "{dot|•}{text|" + params.name + "}";
                  }
                  if (params.name === "锦江区") {
                    return "{text|" + params.name + "}";
                  }
                  return params.name;
                },
                rich: {
                  dot: {
                    color: "#fff",
                    fontSize: 20,
                    padding: [0, 0, 0, 0],
                  },
                  text: {
                    color: "#fff",
                    fontSize: 12,
                    padding: [0, 0, 0, 0],
                  },
                },
              },
              labelLayout: {
                hideOverlap: true,
                draggable: true,
                x: (params) => {
                  if (params.name === "崇州市") {
                    return params.x + 40;
                  }
                  return params.x;
                },
              },
            },
          ],
          visualMap: {
            min: minValue,
            max: maxValue,
            calculable: true,
            orient: "vertical",
            left: "right",
            top: "center",
            inRange: {
              color: [
                "#e0f7fa", // 极浅蓝
                "#b2ebf2",
                "#80deea",
                "#4dd0e1",
                "#26c6da",
                "#00bcd4",
                "#039be5",
                "#0288d1",
                "#0277bd",
                "#01579b", // 深蓝
              ], // 更多层次蓝色分级
            },
            text: ["高", "低"],
            textStyle: {
              color: "#fff",
            },
          },
        };
        // 定义区县映射
        const districtMap = {
          都江堰市: geoJsonDJYSData,
          彭州市: geoJsonPZSData,
          邛崃市: geoJsonQZData,
          锦江区: geoJsonJJData,
          青羊区: geoJsonQYData,
          金牛区: geoJsonJNData,
          武侯区: geoJsonWHData,
          成华区: geoJsonCHData,
          龙泉驿区: geoJsonLQData,
          青白江区: geoJsonQBYData,
          新都区: geoJsonXJData,
          温江区: geoJsonWJData,
          双流区: geoJsonSLData,
          郫都区: geoJsonPXData,
          简阳市: geoJsonJYData,
          大邑县: geoJsonDYData,
          蒲江县: geoJsonPJData,
          新津区: geoJsonXJXData,
          金堂县: geoJsonJTXData,
          崇州市: geoJsonCLData,
        };
        let lastHoveredArea = "锦江区";
        this.chartInstance.getZr().on("globalout", () => {
          this.dimCounty = lastHoveredArea;
          // 先取消所有高亮
          this.chartInstance.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
          });
          // 高亮最后一次悬停的区域
          this.chartInstance.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            name: lastHoveredArea,
          });
          // 展示该区域tooltip
          setTimeout(() => {
            this.showJinjiangTooltip({
              name: lastHoveredArea,
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
          }, 100);
        });
        if (this.chartInstance) {
          this.chartInstance.on("click", (params) => {
            this.$emit("region-click", params.name);
          });
        }
        // 鼠标移入区县时
        this.chartInstance.on("mouseover", (params) => {
          if (params.name) {
            lastHoveredArea = params.name;
            // 先取消所有高亮
            this.chartInstance.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
            });
            // 高亮当前区域
            this.chartInstance.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
            // 展示当前区域tooltip
            this.showJinjiangTooltip({
              name: lastHoveredArea,
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: lastHoveredArea,
            });
          }
        });

        this.chartInstance.on("click", (params) => {
          // ... existing code ...

          if (params.name === "锦江区") {
            this.showShuangliuPopup();
          }
        });

        // 设置 ECharts 配置项
        this.chartInstance.setOption(option);
        setTimeout(() => {
          this.showJinjiangTooltip({
            name: "锦江区",
            componentType: "series",
            seriesType: "map3D",
          });
          this.chartInstance.dispatchAction({
            type: "highlight",
            seriesIndex: 0,
            name: "锦江区",
          });
          this.chartInstance.dispatchAction({
            type: "select",
            seriesIndex: 0,
            name: "锦江区",
          });
        }, 100);

        // 地图初始化后，默认高亮并显示锦江区的tooltip
        setTimeout(() => {
          if (this.chartInstance) {
            this.showJinjiangTooltip({
              name: "锦江区",
              componentType: "series",
              seriesType: "map",
            });
            this.chartInstance.dispatchAction({
              type: "showTip",
              seriesIndex: 0,
              name: "锦江区",
            });
            this.chartInstance.dispatchAction({
              type: "highlight",
              seriesIndex: 0,
              name: "锦江区",
            });
          }
        }, 200);
      } catch (error) {
        console.error("加载 GeoJSON 数据失败:", error);
      }
    },
  },
  watch: {
    flag(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$emit("changeCity", true);
          this.initChart();
        });
      }
    },
  },
};
</script>
<style scope="scoped" lang="scss">
.mapClass {
  width: 1600px;
  height: 1200px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  margin: auto;
}

.tkClass {
  width: 450px;
  background: linear-gradient(
    180deg,
    rgba(30, 15, 1, 0.6) 0%,
    rgba(176, 88, 0, 0.27) 100%
  );
  border: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(251, 230, 176, 1),
      rgba(246, 197, 120, 0)
    )
    2 2;
  backdrop-filter: blur(4px);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2000;
  padding-top: 10px;
  box-sizing: border-box;
  .tkName {
    width: 310px;
    height: 54px;
    background: linear-gradient(
      270deg,
      rgba(255, 194, 0, 0) 0%,
      rgba(255, 142, 0, 0.71) 100%
    );
    font-weight: 500;
    font-size: 34px;
    color: #ffffff;
    line-height: 48px;
    letter-spacing: 1px;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
    padding-left: 17px;
    display: flex;
    align-items: left;
    box-sizing: border-box;
  }
  .tkBody {
    width: 100%;
    padding: 20px 0 25px 20px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    .tkItemClass {
      display: flex;
      align-items: center;
      .cir {
        width: 6px;
        height: 6px;
        background: #ffffff;
        box-shadow: 0px 0px 13px 0px #ff9c00, 0px 0px 8px 0px #ff9000;
      }
      .cirRight {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 30px;
        color: #ffffff;
        line-height: 52px;
        letter-spacing: 1px;
        margin-left: 15px;
      }
    }
  }
}
</style>
