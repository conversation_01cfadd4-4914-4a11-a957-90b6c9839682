<!--
  放款企业高技术产业分布组件
  功能：
  1. 展示放款企业的高技术产业分布情况
  2. 提供产业数据的柱状图可视化
  3. 显示每个产业的放款金额和笔数
  4. 支持数据自动滚动展示
  5. 提供详情弹窗查看完整数据
-->
<template>
  <div class="HighTechnology-szyyClassFour">
    <!-- 头部区域：标题 -->
    <div class="HighTechnology-headerImg">
      <div>放款企业高技术产业分布</div>
      <!--       <div class="ChainDistribution-rightClass">
        <div class="ChainDistribution-djgdClass" @click="handleClick">
          点击查看更多
        </div>
        <div>
          <img
            src="@/assets/dp/rightT.png"
            class="ChainDistribution-imgRight"
          />
        </div>
      </div> -->
    </div>
    <div class="HighTechnology-industry-park-header">
      高技术产业投放贷款：<span class="HighTechnology-industry-park-total">{{
        total
      }}</span>
      亿
    </div>
    <div class="HighTechnology-bottomClass">
      <div ref="parkChart" class="HighTechnology-park-chart"></div>
    </div>
    <DialogBar
      :visible="visible"
      @close="handleClose"
      title="放款企业高技术产业分布"
      :chartData="parkList"
      :chart-width="900"
      :chart-height="468"
      :colorStops="colorStops"
    />
  </div>
</template>

<script>
import { productUpdateList, queryRealtimeMetricList } from "@/api/article.js";
import DialogBar from "../../components/dialogBar-x.vue";
import * as echarts from "echarts";
export default {
  name: "",
  components: {
    DialogBar,
  },
  props: {
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  data() {
    return {
      colorStops: [
        {
          offset: 0,
          color: "#3A76FF",
        },
        {
          offset: 1,
          color: "#031147",
        },
      ],
      visible: false,
      myChart: null,
      scrollTimer: null,
      currentIndex: 0,
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
      flag: true,
      listData: [],
      rrObj: {
        qaNum: "",
        sessionNum: "",
        userAcccess: "",
        useNum: "",
      },
      RealtimeQuery: "",
      RealtimeAvg: "",
      bankNames: [
        "中国\n建设银行",
        "中国\n农业银行",
        "成都\n银行",
        "中国\n工商银行",
        "中国\n银行",
        "中国\n交通银行",
      ],
      barData: [150, 80, 120, 130, 110, 90], // 金额（万）
      lineData: [23, 23, 23, 23, 23, 23], // 百分比
      labelData: [
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
      ],
      parkList: [
        { name: "软件和信息技术服务业", amount: "3.8", count: 4562 },
        { name: "研究和试验发展", amount: "2.9", count: 3289 },
        { name: "科技推广和应用服务业", amount: "2.3", count: 2756 },
        { name: "专业技术服务业", amount: "1.8", count: 2156 },
        { name: "互联网和相关服务", amount: "1.5", count: 1892 },
        { name: "电信、广播电视和卫星传输服务", amount: "1.2", count: 1568 },
      ],
      total: "",
    };
  },

  mounted() {
    this.featchData();
  },
  watch: {
    // isMaxDtReady: {
    //   handler(newVal) {
    //     if (newVal) {
    //       // 只有当 maxDt 准备好后才调用其他接口
    //       this.getrzjrjg();
    //     }
    //   },
    //   immediate: true,
    // },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.featchData(newVal);
      },
    },
  },
  methods: {
    featchData() {
      const industries = [
        "软件和信息技术服务业",
        "研究和试验发展",
        "科技推广和应用服务业",
        "专业技术服务业",
        "互联网和相关服务",
        "电信、广播电视和卫星传输服务",
      ];

      // 创建所有请求的数组
      const requests = industries.flatMap((industry) => [
        {
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: industry,
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
          dimIndustryChain: "ALL",
          indexName: "放款企业科技型行业金额",
        },
        {
          bizDate: localStorage.getItem("maxDt"),
          dimTime: "ALL",
          dimIndustry: industry,
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
          dimIndustryChain: "ALL",
          indexName: "放款企业科技型行业笔数",
        },
      ]);

      productUpdateList(requests)
        .then((res) => {
          const responseData = res.data.data || [];
          const processedData = [];

          // 按行业处理金额和笔数
          industries.forEach((industry, index) => {
            const amountItem = responseData.find(
              (item) =>
                item.dimIndustry === industry &&
                item.indexName === "放款企业科技型行业金额"
            );
            const countItem = responseData.find(
              (item) =>
                item.dimIndustry === industry &&
                item.indexName === "放款企业科技型行业笔数"
            );

            if (amountItem && countItem) {
              processedData.push({
                name: industry,
                amount: parseFloat(amountItem.indexValue || 0),
                count: parseInt(countItem.indexValue || 0, 10),
              });
            }
          });

          // 按金额排序并计算总额
          this.parkList = processedData.sort((a, b) => b.amount - a.amount);
          this.total = this.parkList
            .reduce((sum, item) => sum + item.amount, 0)
            .toFixed(2);

          this.initParkChart();
        })
        .catch((error) => {
          console.error("API请求失败:", error);
          this.parkList = [];
          this.total = "0.00";
        });
    },
    //     featchData(){
    //          productUpdateList([{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "软件和信息技术服务业",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业金额",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "研究和试验发展",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业金额",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "科技推广和应用服务业",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业金额",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "专业技术服务业",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业金额",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "互联网和相关服务",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业金额",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "电信、广播电视和卫星传输服务",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业金额",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "软件和信息技术服务业",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业笔数",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "研究和试验发展",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业笔数",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "科技推广和应用服务业",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业笔数",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "专业技术服务业",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业笔数",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "互联网和相关服务",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业笔数",
    //       },{
    //         bizDate: localStorage.getItem("maxDt"),
    //         dimTime: "ALL",
    //         dimIndustry: "电信、广播电视和卫星传输服务",
    //         // dimCounty: "ALL",
    //           dimCounty: this.selectedRegion || "ALL",
    //         dimPark: "ALL",
    //         // dimNum: 1,
    //          dimNum: this.selectedRegion === "ALL" ? 1 : 2,
    //         dimIndustryChain: "ALL",
    //         indexName: "放款企业科技型行业笔数",
    //       }]).then(res=>{
    //        let data =res.data.data.slice(0,6);
    //        let data2 =res.data.data.slice(6,12);
    //       data.map((item,index)=>{
    //         item.amount=item.indexValue-0;
    //         item.count=data2[index].indexValue-0;
    //         item.name=this.parkList[index].name;
    //        })
    //        this.parkList=data;
    //        this.total=this.parkList.reduce((a,b)=>a+b.amount,0).toFixed(2);
    //        this.parkList=this.parkList.sort((a,b)=>b.amount-a.amount);
    // /*        console.log(this.parkList,data,'this.parkList'); */
    //        this.initParkChart();
    //     })
    //     },
    handleClose() {
      this.visible = false;
    },
    handleClick() {
      this.visible = true;
    },
    initParkChart() {
      if (!this.$refs.parkChart) return;

      this.myChart = echarts.init(this.$refs.parkChart);
      const option = {
        grid: {
          left: "0%",
          right: "0%",
          bottom: "0%",
          top: this.$autoFontSize(32),
          containLabel: true,
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            xAxisIndex: [0],
            height: 8,
            bottom: 2,
            borderColor: "transparent",
            backgroundColor: "#0a2e4a",
            fillerColor: "rgba(43,156,255,0.2)",
            handleStyle: {
              color: "#2b9cff",
              borderColor: "#2b9cff",
            },
            moveHandleStyle: {
              color: "#2b9cff",
            },
            selectedDataBackground: {
              lineStyle: {
                color: "#2b9cff",
              },
              areaStyle: {
                color: "#2b9cff",
              },
            },
            emphasis: {
              handleStyle: {
                color: "#398fff",
              },
            },
            showDetail: false,
            showDataShadow: false,
            brushSelect: false,
            zoomLock: false,
            throttle: 100,
            z: 100,
          },
        ],
        tooltip: {
          show: false,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            const data = params[0];
            return `${data.name}<br/>
                    <span style="color:#ffce7c">${data.value}亿</span><br/>
                    <span style="color:#23eaff">${data.data.count}笔</span>`;
          },
        },
        xAxis: {
          type: "category",
          data: this.parkList.map((item) => item.name),
          axisLabel: {
            color: "#fff",
            fontSize: this.autoFontSize(13),
            interval: 0,
            width: 300,
            overflow: "break",
            formatter: function (value) {
              return value.replace(/(.{5})/g, "$1\n");
            },
          },
          axisTick: {
            show: false, // 隐藏刻度线
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          },
        },
        yAxis: {
          show: false,
          type: "value",
          name: "金额（亿元）",
          nameTextStyle: {
            color: "#fff",
            fontSize: 14,
          },
          axisLabel: {
            color: "#fff",
            fontSize: 14,
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.1)",
            },
          },
        },
        series: [
          {
            name: "全量背景图",
            type: "bar",
            barGap: "-100%",
            data: [
              25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25,
            ],
            barWidth: this.autoFontSize(12),
            itemStyle: {
              normal: {
                color: "rgba(63, 169, 245, 0.2)",
              },
            },
            z: 0,
          },
          {
            data: this.parkList.map((item) => ({
              value: parseFloat(item.amount),
              count: item.count,
            })),
            type: "bar",
            barWidth: this.autoFontSize(12),

            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#3A76FF",
                  },
                  {
                    offset: 1,
                    color: "#031147",
                  },
                ],
              },
              borderRadius: [0, 0, 0, 0],
            },
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return `{amount|${params.value}亿}\n{count|${params.data.count}笔}`;
              },
              rich: {
                amount: {
                  color: "#ffce7c",
                  padding: [0, 0, 5, 0],
                  fontSize: this.$autoFontSize(14),
                  align: "center",
                },
                count: {
                  color: "#23eaff",
                  fontSize: this.$autoFontSize(14),
                  align: "center",
                },
              },
            },
          },
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "circle",
            symbolSize: [this.autoFontSize(12), this.autoFontSize(6)], // 椭圆
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: this.parkList.map((item, idx) => [
              idx,
              parseFloat(item.amount),
            ]),
          },
        ],
      };
      this.myChart.setOption(option);
      /*       this.startAutoScroll(this.parkList.length); */
      window.addEventListener("resize", () => {
        this.initParkChart(); // 重新设置option
        this.myChart.resize();
      });
    },
    autoFontSize(base = 16) {
      // 1920是设计稿宽度，base是设计稿字号
      return Math.round((window.innerWidth / 1920) * base);
    },
    startAutoScroll(totalItems) {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      this.scrollTimer = setInterval(() => {
        const option = this.myChart.getOption();
        const currentData = option.xAxis[0].data;

        // 更新当前索引
        this.currentIndex = (this.currentIndex + this.scrollStep) % totalItems;

        // 重新排序数据
        const newData = [...currentData];
        for (let i = 0; i < this.scrollStep; i++) {
          const item = newData.shift();
          newData.push(item);
        }

        // 更新图表
        this.myChart.setOption({
          xAxis: {
            data: newData,
          },
          series: [
            {
              name: "全量背景图",
              data: [
                100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100,
                100, 100, 100,
              ],
            },
            {
              data: newData.map((name) => {
                const item = this.parkList.find((d) => d.name === name);
                return item
                  ? {
                      value: parseFloat(item.amount),
                      count: item.count,
                    }
                  : { value: 0, count: 0 };
              }),
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                  return `{amount|${params.value}亿元}\n{count|${params.data.count}笔}`;
                },
                rich: {
                  amount: {
                    color: "#ffce7c",
                    fontSize: this.$autoFontSize(16),
                    fontWeight: "bold",
                    padding: [0, 0, 5, 0],
                  },
                  count: {
                    color: "#23eaff",
                    fontSize: this.$autoFontSize(14),
                    fontWeight: "bold",
                  },
                },
              },
            },
            {
              data: newData.map((name, idx) => {
                const item = this.parkList.find((d) => d.name === name);
                return [idx, item ? parseFloat(item.amount) : 0];
              }),
            },
          ],
        });
      }, this.scrollSpeed);
    },
  },
};
</script>
<style scoped lang="scss">
.HighTechnology-szyyClassFour {
  width: 940px;
  height: 25vh;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .HighTechnology-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
  }
  .ChainDistribution-rightClass {
    display: flex;
    align-items: center;
    justify-content: center;

    .ChainDistribution-djgdClass {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26px;
      color: #77c1ff;
      letter-spacing: 1px;
      cursor: pointer;
      margin-right: 12px;
    }

    .ChainDistribution-imgRight {
      width: 12px;
      height: 22px;
      position: relative;
      top: -1px;
    }
  }
  .HighTechnology-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    position: relative;
    overflow: hidden;
    min-height: 300px;
  }
}

.HighTechnology-industry-park-header {
  color: #fff;
  font-size: 28px;

  margin-left: 60px;
}

.HighTechnology-industry-park-total {
  font-family: OPPOSans, OPPOSans;
  font-weight: bold;
  font-size: 44px;
  color: #ffffff;
  line-height: 70px;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
  background: linear-gradient(90deg, #ffffff 0%, #7cebff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.HighTechnology-park-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
  box-sizing: border-box;
}
</style>
