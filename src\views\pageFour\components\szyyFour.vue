<!-- 
  数智应用组件
  功能：展示数智应用相关的统计信息和产品数据
  特点：
  1. 支持两种展示模式切换（flag控制）
  2. 模式一：展示访问用户数、功能使用次数等统计数据
  3. 模式二：展示信用融资联合建模产品相关信息
  4. 美观的数据展示界面，包含图片和渐变效果
-->
<template>
  <div class="szyyClassFour">
    <!-- 头部标题区域 -->
    <div class="headerImgSzyy">
      <div>数智应用</div>
      <div class="rightClass">
        <div></div>
      </div>
    </div>
    <!-- 主体内容区域 -->
    <div class="bottomClass">
      <!-- 模式一：统计数据展示 -->
      <div class="flagOneClass" v-if="flag">
        <!-- 左侧图片 -->
        <div class="xm"><img src= "@/assets/dp/xm.png" class="xmClass" /></div>
        <!-- 右侧对话框数据展示 -->
        <div class="duihuaClass">
          <!-- 访问用户数 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">访问用户数：</div>
            <div class="fwyhsNumClass">{{parseInt(rrObj.qaNum) }}</div>
            <div class="dwClass">次</div>
          </div>
          <!-- 功能使用次数 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">功能使用次数：</div>
            <div class="fwyhsNumClass">{{parseInt(rrObj.sessionNum)  }}</div>
            <div class="dwClass">次</div>
          </div>
          <!-- 平均问答次数 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">平均问答次数：</div>
            <div class="fwyhsNumClass">{{rrObj.userAcccess}}</div>
            <div class="dwClass">次</div>
          </div>
          <!-- 平均单次使用时长 -->
          <div class="duihuaItem">
            <div class="fwyhsClass">平均单次使用时长：</div>
            <div class="fwyhsNumClass">{{rrObj.useNum}}</div>
            <div class="dwClass">分钟</div>
          </div>
        </div>
      </div>
      <!-- 模式二：产品信息展示 -->
      <div class="flagTwoClass" v-if="!flag">
        <!-- 联合建模产品信息 -->
        <div class="twoItem">
          <div class="leftClass"><img src= "@/assets/dp/lk.png" class="leftImgClass" /></div>
          <div class="rightFlagClass">
            <div class="rightName">开发信用融资联合建模产品</div>
            <div class="rightNum">
              <div class="oneClass">联合</div>
              <div class="twoClass">6</div>
              <div class="oneClass">家银行，开发联合建模产品</div>
              <div class="twoClass">9</div>
              <div class="threeClass">款</div>
            </div>
          </div>
        </div>
        <!-- 白名单模式信息 -->
        <div class="twoItem marginClass">
          <div class="leftClass"><img src= "@/assets/dp/lvk.png" class="leftImgClass" /></div>
          <div class="rightFlagClass2">
            <div class="rightName">开发信用融资联合建模产品</div>
            <div class="rightNum">
              <div class="oneClass">联合</div>
              <div class="twoClass">6</div>
              <div class="oneClass">家银行，首批白名单企业</div>
              <div class="twoClass">30</div>
              <div class="threeClass">万户</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from 'vue-seamless-scroll' 
import { productUpdateList, queryRealtimeMetricList } from "@/api/article.js";

export default {
  name: 'szyyFour',
  components: {                                            
    vueSeamlessScroll
  },
  data(){
    return {
      maxDt: localStorage.getItem("maxDt"), // 最大日期
      flag: true, // 控制显示模式
      listData: [], // 列表数据
      // 统计数据对象
      rrObj:{
        qaNum: '', // 访问用户数
        sessionNum: '', // 功能使用次数
        userAcccess: '', // 平均问答次数
        useNum: '' // 平均单次使用时长
      },
      RealtimeQuery: '', // 实时查询数据
      RealtimeAvg: '' // 实时平均值
    }
  },
  mounted(){
    this.getszyyFour() // 获取产品数据
    this.getszyyFour1() // 获取统计数据
    this.getszyyFour2() // 获取实时数据
  },
  methods:{
    // 获取产品相关数据
    async getszyyFour(){
      let param = [{
          indexName: "开发信用融资联合建模产品银行数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "开发联合建模产品数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "创新预授信白名单模式银行数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
        {
          indexName: "创新预授信白名单模式企业户数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
        },
      ];
      const res = await productUpdateList(param);
      if(res.data.code==200){
        this.listData = res.data.data
      }
    },
    // 获取统计数据
    async getszyyFour1(){
      let obj = [
        {
          indexName: "访问用户数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 1,
        },
        {
          indexName: "功能使用次数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 1,
        }
      ]
      const res = await productUpdateList(obj);
      if(res.data.code==200){
        this.rrObj.qaNum = res.data.data[0].indexValue
        this.rrObj.sessionNum = res.data.data[1].indexValue
        // 计算平均问答次数，保留两位小数
        this.rrObj.userAcccess = (this.rrObj.sessionNum/this.rrObj.qaNum).toFixed(2)  
      }
    },
    // 获取实时数据
    async getszyyFour2() {
      let obj = [
        {
          indexName: "问答次数",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 1,
        },
        {
          indexName: "平均单次使用时长",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          dimCounty: "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 1,
        }
      ]
      const res = await queryRealtimeMetricList(obj);
      if(res.data.code==200){
        this.rrObj.useNum = res.data.data[1].indexValue
      }
    }
  }
}
</script>

<style scope="scoped" lang="scss">
/* 组件主容器样式 */
.szyyClassFour{
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 光效样式 */
  .guangClass{
    width: 220px;
    height: 12px;
    background: radial-gradient(397% 76% at 50% 50%, #97CAEB 0%, #048EE6 100%);
    filter: blur(4.338461538461543px);
    position: absolute;
    bottom: 0;
  }

  /* 头部标题样式 */
  .headerImgSzyy{
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image:url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #FFFFFF;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0,175,255,0.68), 0px 2px 4px #000C1A;

    /* 右侧按钮样式 */
    .rightClass{
      display: flex;
      .djgdClassOne{
        position: relative;
        right: -1px;
      }
      // 按钮样式1
      .djgdClassOne1{
        width: 270px;
        height: 56px;
        background: linear-gradient(180deg, #052A53 0%, #033D7B 63%, #0047A8 100%);
        border: 1px solid #1790FF;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #FFFFFF;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
      }
      // 按钮样式2
      .djgdClassOne2{
        width: 270px;
        height: 56px;
        background: linear-gradient(180deg, rgba(5,38,83,0) 0%, rgba(12,74,139,0.49) 100%);
        box-shadow: inset 0px 0px 14px 0px #168AFF;
        border: 2px solid rgba(110,160,227,0.8);
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #67A7FF;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
      }
    }
  }

  /* 底部内容区域样式 */
  .bottomClass{
    width: 100%;
    flex: 1;
    // 设置渐变背景
    background: linear-gradient(180deg, rgba(3,29,58,0) 0%, rgba(0,50,107,0.64) 100%);
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(169deg, rgba(44, 110, 162, 0), rgba(47, 115, 169, 1)) 2 2;
    display: flex;
    justify-content: space-between;
    align-items: center;

    /* 模式一样式 */
    .flagOneClass{
      display: flex;
      padding-left: 90px;
      // 左侧图片样式
      .xm{
        padding-top: 100px;
        .xmClass{
          width: 264px;
          height: 310px;
        }
      }
      // 右侧对话框样式
      .duihuaClass{
        width: 510px;
        height: 336px;
        background-image:url("~@/assets/dp/duihua.png");
        background-repeat: no-repeat;
        background-size: 510px 336px;
        margin-top: 35px;
        padding: 35px 0 30px 50px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
        // 数据项样式
        .duihuaItem{
          display: flex;
          align-items: flex-end;
          // 标签文字样式
          .fwyhsClass{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28px;
            color: #FFFFFF;
            line-height: 40px;
          }
          // 数值样式
          .fwyhsNumClass{
            font-family: OPPOSans, OPPOSans;
            font-weight: normal;
            font-size: 36px;
            letter-spacing: 2px;
            color:#00F6FF;
          }
          // 单位样式
          .dwClass{
            margin-left: 5px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28px;
            color: #FFFFFF;
          }
        }
      }
    }

    /* 模式二样式 */
    .flagTwoClass{
      padding-left: 85px;
      padding-top: 60px;
      .marginClass{
        margin-top: 40px;
      }
      // 产品项样式
      .twoItem{
        display: flex;
        // 左侧图片样式
        .leftClass{
          margin-right: 30px;
          margin-top: 15px;
          .leftImgClass{
            width: 215px;
            height:142px
          }
        }
        // 右侧内容样式
        .rightFlagClass{
          // 标题样式
          .rightName{
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            -webkit-text-fill-color: transparent;
            background: linear-gradient(180deg,#FFFFFF 0%, #85BEFF 100%);
            background-clip: text;
          }
          // 数据展示样式
          .rightNum{
            width: 540px;
            height: 72px;
            background-image:url("~@/assets/dp/lkhf.png");
            background-repeat: no-repeat;
            background-size: 540px 72px;
            margin-top: 24px;
            padding-left: 32px;
            display: flex;
            align-items: center;
            // 标签文字样式
            .oneClass{
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: #8FC4FF;
            }
            // 数值样式
            .twoClass{
              font-family: OPPOSans, OPPOSans;
              font-weight: normal;
              font-size: 46px;
              letter-spacing: 2px;
              background: linear-gradient(180deg, #ffffff 0%, #F5873D 100%);
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            // 单位样式
            .threeClass{
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: #FFFFFF;
            }
          }
        }
        // 第二个产品项样式
        .rightFlagClass2{
          .rightName{
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 32px;
            -webkit-text-fill-color: transparent;
            background: linear-gradient(180deg, #FFFFFF 0%, #85EFFF 100%);
            background-clip: text;
          }
          .rightNum{
            width: 540px;
            height: 72px;
            background-image:url("~@/assets/dp/lvkhf.png");
            background-repeat: no-repeat;
            background-size: 540px 72px;
            margin-top: 24px;
            padding-left: 32px;
            display: flex;
            align-items: center;
            .oneClass{
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: rgba(180,255,254,0.84);
            }
            .twoClass{
              font-family: OPPOSans, OPPOSans;
              font-weight: normal;
              font-size: 46px;
              letter-spacing: 2px;
              background: linear-gradient(180deg, #ffffff 0%, #F5873D 100%);
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .threeClass{
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: 24px;
              color: #FFFFFF;
            }
          }
        }
      }
    }
  } 
}
</style>