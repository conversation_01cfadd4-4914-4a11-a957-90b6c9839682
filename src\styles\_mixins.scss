// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'small' {
    @media (max-width: 576px) { @content; }
  }
  @else if $breakpoint == 'medium' {
    @media (max-width: 768px) { @content; }
  }
  @else if $breakpoint == 'large' {
    @media (max-width: 992px) { @content; }
  }
  @else if $breakpoint == 'xlarge' {
    @media (max-width: 1200px) { @content; }
  }
}

// 文本溢出省略号
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// flex 布局
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
} 