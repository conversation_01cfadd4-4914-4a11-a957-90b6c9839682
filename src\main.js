// 导入Vue核心库
import Vue from 'vue'
// 导入根组件
import App from './App.vue'
// 导入路由配置
import router from './router'
// 导入状态管理
import store from './store'

// 按需引入Element UI组件
import { Button, Table, Pagination, Dialog, Tooltip } from 'element-ui'
// 导入Element UI样式
import 'element-ui/lib/theme-chalk/index.css'

// 导入JSEncrypt用于RSA加密
import JSEncrypt from 'jsencrypt'
// 创建JSEncrypt实例
let jse = new JSEncrypt();
// 设置RSA公钥
jse.setPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCuCGsmAajmbyWI4wPefTS1ixHEMtPy6/hiHHSjKXABSLCfTuWA3d9tD1hn2k9fI/S61XoO6EPhNMXVKjEtNh7pm5d5WqGVu1XEA9yHs5P2SBtChNCPXZcUY2RF3SR38zjo1fbDpBvuQOUhOLnz/51N4smiR/EYlCcCoPNj3D2lWQIDAQAB")

// 关闭生产环境提示
Vue.config.productionTip = false
// 将JSEncrypt实例挂载到Vue原型上，使其在全局可用
Vue.prototype.$jse = jse

// 按需注册Element UI组件
Vue.use(Button)
Vue.use(Table)
Vue.use(Pagination)
Vue.use(Dialog)
Vue.use(Tooltip)

// 导入并注册全局缩放功能
import { autoFontSize } from '@/util/echartsFont'
Vue.prototype.$autoFontSize = autoFontSize

// 错误处理相关代码（当前已注释）
/* Vue.config.errorHandler = function (err, vm, info) {
  return false;
}; */
/* window.addEventListener('unhandledrejection', event => {
  event.preventDefault();
}); */
/* window.onerror = function(message, source, lineno, colno, error) {
  event.preventDefault();
  console.log("A global error occurred: ", message);
};window.addEventListener('error', event => {
  if (event.message && event.message.includes('Redirected when going from')) {
    event.preventDefault();
  }
}); */

// 创建Vue实例并挂载到DOM
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
