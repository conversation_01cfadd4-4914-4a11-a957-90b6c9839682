import Cookies from 'js-cookie'
const TokenKey = 'saber-access-token'
const RefreshTokenKey = 'saber-refresh-token'
export function getToken() {
    return Cookies.get(TokenKey)
}

export function setToken(token) {
  if(process.env.NODE_ENV == 'production'){
    return Cookies.set(TokenKey, token, {'domain':'.szjrfw.com'}) 
  }else{
    return Cookies.set(Token<PERSON>ey, token) // 用在 开发、测试环境
  }
}

export function getRefreshToken() {
  return Cookies.get(RefreshTokenKey)
}

export function setRefreshToken(token) {
  if(process.env.NODE_ENV == 'production'){
    return Cookies.set(RefreshTokenKey, token, {'domain':'.szjrfw.com'}) // 生产环境 需要放开
  }else{
    return Cookies.set(RefreshTokenKey, token) // 用在 开发、测试环境
  }
}

export function removeToken() {
    return Cookies.remove(TokenKey)
}

export function removeRefreshToken() {
  return Cookies.remove(RefreshTokenKey)
}

