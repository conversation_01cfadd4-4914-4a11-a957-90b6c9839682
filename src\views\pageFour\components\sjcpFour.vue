<template>
  <div class="fwstjjClass">
    <div class="headerImg">
      <div class="lbClass">预授信白名单服务</div>
      <div class="rightClass">
        <div
          :class="[flag ? 'djgdClassOne2' : 'djgdClassOne1']"
          @click="chainClick"
        >
          重点产业链
<!--           <div class="guangClass" v-if="!flag"></div> -->
        </div>
        <div
          :class="[flag ? 'djgdClassOne1' : 'djgdClassOne2', 'djgdClassOne']"
          @click="parkClick"
        >
          产业园区
<!--           <div class="guangClass" v-if="flag"></div> -->
        </div>
        <!--         <div></div> -->
      </div>
    </div>
    <div class="bottomClass">
      <div class="header"  v-if="!flag">
        覆盖全市产业链：<span class="total">16</span> 条
      </div>
      <div class="header"  v-if="flag">
        覆盖全市园区：<span class="total">52</span> 个
      </div>
      <div class="industryBar_box">
        <div class="industryBar" ref="industryBar" v-show="!flag"></div>
        <div class="industryBar1" ref="industryBar1" v-show="flag"></div>
      </div>
    </div>
    <DialogBar
      :visible="visible"
      @close="handleClose"
      title="预授信白名单服务"
      :chartData="chartData"
      :chart-width="1000"
      :colorStops="colorStops"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdateList } from "@/api/article.js";
import DialogBar from "../../components/dialogBar.vue";
export default {
  name: "sjcpFour",
  components: {
    DialogBar,
  },
  data() {
    return {
      visible: false,
      chartData: [],
      colorStops: [
        { offset: 0, color: "#57BDFF" },
        { offset: 1, color: "#0C438C" },
      ],
      myChart: null,
      scrollTimer: null,
      currentIndex: 0,
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
      flag: false,
      originalData: [],
      chainData: [], // 存储产业链数据
      parkData: [], // 存储园区数据
      paramsList: [
        "工业互联网",
        "轨道交通",
        "人工智能与机器人（含车载智能控制系统）",
        "生态环保",
        "新材料",
        "新型显示",
        "低空经济",
        "卫星互联网与卫星应用",
        "集成电路",
        "智能终端",
        "汽车（新能源汽车）",
        "高端软件与操作系统",
        "大飞机制造与服务",
        "文创业（含数字文创）",
        "新能源",
        "工业无人机",
        "航空发动机",
        "高端医疗器械",
        "氢能",
      ],
      parkList1: [
        { name: "温江国家农业科技园区", amount: "3.8", count: 4562 },
        { name: "国宾都市文旅产业园", amount: "2.9", count: 3289 },
        { name: "四川成都新津经济开发区", amount: "2.3", count: 2756 },
        { name: "荷花池商务商贸产业园", amount: "1.8", count: 2156 },
        { name: "四川成都成华经济开发区", amount: "1.5", count: 1892 },
        { name: "成都—阿坝工业园区", amount: "1.2", count: 1568 },
        { name: "四川天府新兴经济开发区（拟筹）", amount: "0.9", count: 1234 },
        { name: "华西转化医学产业园", amount: "0.8", count: 1123 },
        { name: "四川成都武侯经济开发区", amount: "0.7", count: 987 },
        { name: "四川成都锦江经济开发区", amount: "0.6", count: 856 },
        { name: "成都国际铁路港经济技术开发区", amount: "0.5", count: 765 },
        { name: "四川金堂经济开发区", amount: "0.4", count: 654 },
        { name: "天府果荟国家现代农业产业园", amount: "0.3", count: 543 },
        { name: "成都音乐文创园", amount: "0.2", count: 432 },
        { name: "四川邛崃经济开发区", amount: "0.1", count: 321 },
        { name: "四川成都郫都高新技术产业园区", amount: "3.8", count: 4562 },
        { name: "四川彭州经济开发区", amount: "2.9", count: 3289 },
        { name: "城北（香城）新消费活力区", amount: "2.3", count: 2756 },
        { name: "四川天府国际空港经济开发区（在筹）", amount: "1.8", count: 2156 },
        { name: "成都高新技术产业开发区", amount: "1.5", count: 1892 },
        { name: "成都新材料产业化工园区", amount: "1.2", count: 1568 },
        { name: "天府中央商务区", amount: "0.9", count: 1234 },
        { name: "成都经济技术开发区", amount: "0.8", count: 1123 },
        { name: "四川成都温江高新技术产业园区", amount: "0.7", count: 987 },
        { name: "四川都江堰经济开发区", amount: "0.6", count: 856 },
        { name: "成都熊猫国际旅游度假区", amount: "0.5", count: 765 },
        { name: "四川成都新都高新技术产业园区", amount: "0.4", count: 654 },
        { name: "少城国际文创谷", amount: "0.3", count: 543 },
        { name: "四川崇州经济开发区", amount: "0.2", count: 432 },
        { name: "简阳临空经济产业园", amount: "0.1", count: 321 },
        { name: "四川成都金牛高新技术产业园区", amount: "3.8", count: 4562 },
        { name: "天府数字农旅产业园", amount: "2.9", count: 3289 },
        { name: "四川成都双流经济开发区", amount: "2.3", count: 2756 },
        { name: "都江堰现代文旅融合园区", amount: "1.8", count: 2156 },
        { name: "四川成都青羊经济开发区", amount: "1.5", count: 1892 },
        { name: "东郊记忆艺术区", amount: "1.2", count: 1568 },
        { name: "成都影视城", amount: "0.9", count: 1234 },
        { name: "街子古镇群旅游度假区", amount: "0.8", count: 1123 },
        { name: "四川简阳经济开发区", amount: "0.7", count: 987 },
        { name: "四川蒲江经济开发区", amount: "0.6", count: 856 },
        { name: "四川大邑经济开发区", amount: "0.5", count: 765 },
        { name: "龙门山旅游度假区", amount: "0.4", count: 654 },
        { name: "天府蔬香农业产业园", amount: "0.3", count: 543 },
        { name: "中国天府农业博览园", amount: "0.2", count: 432 },
        { name: "天府现代种业园", amount: "0.1", count: 321 },
        { name: "西岭冰雪·安仁文博国际文化旅游区", amount: "0.1", count: 321 },
        { name: "天府菌都农业产业园", amount: "0.1", count: 321 },
        { name: "天府粮仓国家现代农业产业园", amount: "0.1", count: 321 },
        { name: "天府奥体公园", amount: "0.1", count: 321 },
      ],
    };
  },
  mounted() {
    this.initData();
  },
  beforeDestroy() {
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    async initData() {
      // 分别调用两个接口
      const chainObj = this.paramsList.map(item => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 4,
        dimIndustryChain: item,
        indexName: "白名单数量",
      }));

      const parkObj = this.parkList1.map(item => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: item.name,
        dimNum: 3,
        dimIndustryChain: 'ALL',
        indexName: "白名单数量",
      }));

      try {
        // 先获取产业链数据
        const chainRes = await productUpdateList(chainObj);
        this.chainData = chainRes.data.data.map(item => ({
          name: item.dimIndustryChain,
          value: item.indexValue
        })).sort((a, b) => a.value - b.value);

        this.originalData = this.chainData;
        await this.$nextTick();
        this.initBar();

        // 再获取园区数据
        const parkRes = await productUpdateList(parkObj);
        this.parkData = parkRes.data.data.map(item => ({
          name: item.dimPark,
          value: item.indexValue
        })).sort((a, b) => a.value - b.value);
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },
    parkClick() {
      this.flag = true;
      this.originalData = this.parkData;
      this.$nextTick(() => {
        this.initBar();
      });
    },
    chainClick() {
      this.flag = false;
      this.originalData = this.chainData;
      this.$nextTick(() => {
        this.initBar();
      });
    },
    initBar() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      const chartRef = this.flag ? this.$refs.industryBar1 : this.$refs.industryBar;
      if (!chartRef) {
        console.warn('Chart container not found, retrying...');
        setTimeout(() => this.initBar(), 10000);
        return;
      }

      this.myChart = echarts.init(chartRef);
      const data = [...this.originalData];
      let maxValue = Math.max(...data.map((d) => Number(d.value)));
      maxValue = maxValue * 1.5;

      this.myChart.setOption({
        grid: { left:this.$autoFontSize(150), right: this.$autoFontSize(20), top: this.$autoFontSize(10), bottom: this.$autoFontSize(30) },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            if (data.name && data.name.length > 9) {
              return data.name;
            }
            return '';
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: this.$autoFontSize(14)
          },
          extraCssText: 'padding: 8px 12px;'
        },
        xAxis: {
          type: "value",
          show: false,
          max: maxValue, 
        },
        yAxis: {
          type: "category",
          data: this.originalData.map((item) => item.name),
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: "#fff",
            padding:[this.$autoFontSize(5),0,0,0],
            fontSize: this.$autoFontSize(12),
            formatter: function(value) {
              if (value.length > 9) {
                return value.substring(0, 9) + '...';
              }
              return value;
            },
            rich: {
              a: {
                color: '#fff',
                fontSize: this.$autoFontSize(12),
                lineHeight: this.$autoFontSize(20)
              }
            }
          }
        },
        series: [
          {
            type: "bar",
            data: data.map(() => maxValue),
            barWidth: this.$autoFontSize(22),
            itemStyle: {
              color: "#00326B",
              borderRadius: 0,
            },
            barGap: "-80%",
            z: 1,
          },
          {
            type: "bar",
            data: data.map((item) => item.value),
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: "#57BDFF" },
                  { offset: 1, color: "#0C438C" },
                ],
              },
              borderRadius: 0,
            },
            label: {
              show: true,
              position: "right",
              formatter: (params) => {
                const d = data[params.dataIndex];
                return `${d.value}户`;
              },
              color: "#fff",
              fontSize: this.$autoFontSize(14),
            },
          }
          ,
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "rect",
            symbolSize: [this.$autoFontSize(2), this.$autoFontSize(16)], // 宽8，高24
            symbolOffset: [0, 2],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: data.map((item, idx) => [Number(item.value), idx]),
          },
        ],
      });

      // 启动自动滚动
      this.startAutoScroll(data.length);

      window.addEventListener("resize", () => this.myChart.resize());
    },
    startAutoScroll(totalItems) {
  if (this.scrollTimer) {
    clearInterval(this.scrollTimer);
  }
  this.scrollTimer = setInterval(() => {
    const option = this.myChart.getOption();
    const currentData = option.yAxis[0].data;

    // 更新当前索引
    this.currentIndex = (this.currentIndex + this.scrollStep) % totalItems;

    // 重新排序数据
    const newData = [...currentData];
    for (let i = 0; i < this.scrollStep; i++) {
      newData.unshift(newData.pop())
    }
    //重新排序
    this.originalData=newData.map((n)=>{
      if(this.originalData.find((d)=>d.name==n)){
        const item=this.originalData.find((d)=>d.name==n)
        return item
      }

    })  
    // 重新计算 maxValue
    let maxValue = Math.max(...this.originalData.map((d) => Number(d.value)));
    maxValue = maxValue * 1.5;

    // 更新图表
    this.myChart.setOption({
      yAxis: {
        data: newData,
      },
      xAxis: {
        max: maxValue, // 保证主条和背景条长度一致
      },
      series: [
        // 背景条
        {
          data: newData.map(() => maxValue),
        },
        // 主数据
        {
          data: newData.map((name) => {
            const item = this.originalData.find((d) => d.name === name);
            return item ? item.value : 0;
          }),          
          label: {
            show: true,
            position: "right",
            formatter: (params) => {
              const d = this.originalData[params.dataIndex];
              return `${d.value}户`;
            },
          },
        },
        // 白色长条
        {
          data: newData.map((name, idx) => {
            const item = this.originalData.find((d) => d.name === name);
            return [item ? Number(item.value) : 0, idx];
          }),
        },
      ],
    });
  }, this.scrollSpeed);
},
    handleClick() {
      this.visible = true;
      this.$nextTick(() => {
        this.chartData = this.originalData.slice(0, 12);
      });
    },
    handleClose() {
      this.visible = false;
    },
  },
};
</script>

<style scoped lang="scss">
.fwstjjClass {
  width: 950px;
  height: 25vh;
  display: flex;
  flex-direction: column;
  .guangClass {
    width: 120px;
    height: 12px;
    background: radial-gradient(397% 76% at 50% 50%, #97caeb 0%, #048ee6 100%);
    position: absolute;
    bottom: 0;
  }
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .lbClass {
      height: 80px;
      line-height: 80px;
    }
    .rightClass {
      display: flex;
      .djgdClassOne {
        position: relative;
        right: -1px;
      }
      .djgdClassOne1 {
        width: 200px;
        height: 56px;
        background: linear-gradient(
          180deg,
          #052a53 0%,
          #033d7b 63%,
          #0047a8 100%
        );
        border: 1px solid #1790ff;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #ffffff;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
      }
      .djgdClassOne2 {
        width: 200px;
        height: 56px;
        background: linear-gradient(
          180deg,
          rgba(5, 38, 83, 0) 0%,
          rgba(12, 74, 139, 0.49) 100%
        );
        box-shadow: inset 0px 0px 14px 0px #168aff;
        border: 2px solid rgba(110, 160, 227, 0.8);
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #67a7ff;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
      }
    }
  }
  .industryBar_box {

  }

  /* 下面的样式移到 .fwstjjClass 下一级 */
  .industryBar {
    width: 940px;
    height: 1200px;
  }
  .industryBar1 {
    width: 940px;
    height: 3000px;
  }
  .bottomClass {
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    overflow: hidden;
    &::-webkit-scrollbar {
      width: 8px;
      background: #0a2e4a;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #2b9cff 0%, #398fff 100%);
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      background: #0a2e4a;
      border-radius: 4px;
    }
    .header {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      line-height: 60px;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      width: 100%;
      margin-left: 40px;
    }
    .total {
      letter-spacing: 2px;
      text-align: center;
      font-family: OPPOSans, OPPOSans;
      font-weight: bold;
      font-size: 46px;
      letter-spacing: 2px;
      // text-shadow: 0px 0px 4px rgba(255,185,49,0.47), 0px 0px 14px rgba(255,189,65,0.54), 0px 2px 4px rgba(0,0,0,0.5);
      background: linear-gradient(90deg, #FFFFFF 0%, #7CCAFF 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
</style>
