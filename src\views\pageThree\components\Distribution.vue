<!--
  放款企业制造业分布组件
  功能：
  1. 展示放款企业的制造业分布情况
  2. 提供制造业数据的柱状图可视化
  3. 显示每个制造业的放款金额和笔数
  4. 支持数据自动滚动展示
  5. 提供详情弹窗查看完整数据
-->
<template>
  <div class="Distribution-szyyClassFour">
    <!-- 头部区域：标题和查看更多按钮 -->
    <div class="Distribution-headerImg">
      <div>放款企业制造业分布</div>
      <div class="ChainDistribution-rightClass">
        <div class="ChainDistribution-djgdClass" @click="handleClick">点击查看更多</div>
        <div>
          <img
            src="@/assets/dp/rightT.png"
            class="ChainDistribution-imgRight"
          />
        </div>
      </div>
    </div>
    <!-- 头部统计信息：显示总金额 -->
    <div class="Distribution-industry-park-header">
      制造业投放贷款：<span class="Distribution-industry-park-total">{{ total }}</span> 亿元
    </div>
    <!-- 底部内容区域：展示制造业数据图表 -->
    <div class="Distribution-bottomClass">
      <div ref="parkChart" class="Distribution-park-chart"></div>
    </div>
    <!-- 详情弹窗组件 -->
    <DialogBar
      :visible="visible"
      @close="handleClose"
      title="放款企业制造业分布"
      :chartData="parkList"
      :chart-width="900"
      :chart-height="468"
      :colorStops="colorStops"
    />
  </div>
</template>

<script>
import { productUpdateList, queryRealtimeMetricList } from "@/api/article.js";
import DialogBar from "../../components/dialogBar-x.vue";
import * as echarts from "echarts";

export default {
  name: 'szyyFour',
  components: {
    DialogBar
  },
  data() {
    return {
      // 图表渐变色配置
      colorStops: [{
        offset: 0,
        color: '#40BEFD'
      }, {
        offset: 1,
        color: '#032A47'
      }],
      visible: false, // 控制弹窗显示
      myChart: null, // ECharts实例
      scrollTimer: null, // 自动滚动定时器
      currentIndex: 0, // 当前滚动索引
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
      flag: true, // 控制标志
      listData: [], // 列表数据
      // 实时查询相关数据
      rrObj: {
        qaNum: '',
        sessionNum: '',
        userAcccess: '',
        useNum: ''
      },
      RealtimeQuery: '', // 实时查询
      RealtimeAvg: '', // 实时平均值
      // 银行名称列表
      bankNames: [
        '中国\n建设银行',
        '中国\n农业银行',
        '成都\n银行',
        '中国\n工商银行',
        '中国\n银行',
        '中国\n交通银行'
      ],
      barData: [150, 80, 120, 130, 110, 90], // 金额（万）
      lineData: [23, 23, 23, 23, 23, 23],    // 百分比
      // 标签数据
      labelData: [
        { percent: '23%', amount: '130万' },
        { percent: '23%', amount: '130万' },
        { percent: '23%', amount: '130万' },
        { percent: '23%', amount: '130万' },
        { percent: '23%', amount: '130万' },
        { percent: '23%', amount: '130万' }
      ],
      total: '', // 总金额
      // 制造业列表数据
      parkList: [
        { name: '成都高新西区南部园区', amount: '3.8', count: 4562 },
        { name: '华西大健康产业功能区', amount: '2.9', count: 3289 },
        { name: '成都医学城', amount: '2.3', count: 2756 },
        { name: '成都金牛高新技术产业园区', amount: '1.8', count: 2156 },
        { name: '万安镇工业园区', amount: '1.5', count: 1892 },
        { name: '成都天府国际生物城', amount: '1.2', count: 1568 },
        { name: '成都科学城', amount: '0.9', count: 1234 },
        { name: '成都航空产业园', amount: '0.8', count: 1123 },
        { name: '成都电子信息产业功能区', amount: '0.7', count: 987 },
        { name: '成都智能网联汽车产业园', amount: '0.6', count: 856 },
        { name: '成都轨道交通产业功能区', amount: '0.5', count: 765 },
        { name: '成都新材料产业功能区', amount: '0.4', count: 654 },
        { name: '成都节能环保产业功能区', amount: '0.3', count: 543 },
        { name: '成都生物医药产业功能区', amount: '0.2', count: 432 },
        { name: '成都智能制造产业功能区', amount: '0.1', count: 321 }
      ]
    }
  },
  mounted() {
    // 获取制造业数据
    productUpdateList([{
      bizDate: localStorage.getItem("maxDt"),
      dimTime: "ALL",
      dimIndustry: "制造业",
      dimCounty: "ALL",
      dimPark: "ALL",
      dimNum: 1,
      dimIndustryChain: 'ALL',
      indexName: "放款企业制造业金额",
    }, {
      bizDate: localStorage.getItem("maxDt"),
      dimTime: "ALL",
      dimIndustry: "制造业",
      dimCounty: "ALL",
      dimPark: "ALL",
      dimNum: 1,
      dimIndustryChain: 'ALL',
      indexName: "放款企业制造业笔数",
    }]).then(res => {
      // 处理返回的数据
      let data = JSON.parse(res.data.data[0].bizContent);
      console.log("值",data)
      let data2 = JSON.parse(res.data.data[1].bizContent);
      let list = data.top_industries;

      // 处理数据格式
      list.forEach((item, index) => {
        item.value = ((item.amount - 0) / 10000).toFixed(2) - 0;
        item.amount = parseFloat(item.value);
        item.name = item.industry_name;
        item.count = data2.top_industries[index].loan_num;
      });

      // 添加其他类别数据
      list.push({
        name: '其他',
        value: (data.other / 10000).toFixed(2),
        amount: (data.other / 10000).toFixed(2),
        count: data2.other
      });

      this.parkList = list;
      this.total = res.data.data[0].indexValue;

      // 按金额从大到小排序
      this.parkList.sort((a, b) => b.value - a.value);
      this.initParkChart();
    });
  },
  beforeDestroy() {
    // 组件销毁前清理资源
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.visible = false;
    },
    // 打开弹窗
    handleClick() {
      this.visible = true;
    },
    // 初始化制造业分布图表
    initParkChart() {
      if (!this.$refs.parkChart) return;

      this.myChart = echarts.init(this.$refs.parkChart);
      const option = {
        // 图表网格配置
        grid: {
          left: this.$autoFontSize(0),
          right: this.$autoFontSize(0),
          bottom: this.$autoFontSize(6),
          top: this.$autoFontSize(30),
          containLabel: true
        },
        // 数据缩放配置
        dataZoom: [
          {
            type: 'slider',
            show: false,
            xAxisIndex: [0],
            start: 0,
            end: 40,
            height: 8,
            bottom: 2,
            borderColor: 'transparent',
            backgroundColor: '#0a2e4a',
            fillerColor: 'rgba(43,156,255,0.2)',
            handleStyle: {
              color: '#2b9cff',
              borderColor: '#2b9cff'
            },
            moveHandleStyle: {
              color: '#2b9cff'
            },
            selectedDataBackground: {
              lineStyle: {
                color: '#2b9cff'
              },
              areaStyle: {
                color: '#2b9cff'
              }
            },
            emphasis: {
              handleStyle: {
                color: '#398fff'
              }
            },
            showDetail: false,
            showDataShadow: false,
            brushSelect: false,
            zoomLock: false,
            throttle: 100,
            z: 100
          }
        ],
        // 提示框配置
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>
                    <span style="color:#ffce7c">${data.value}亿元</span><br/>
                    <span style="color:#23eaff">${data.data.count}笔</span>`;
          }
        },
        // X轴配置
        xAxis: {
          type: 'category',
          data: this.parkList.map(item => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(13),
            interval: 0,
            width: 300,
            overflow: 'break',
            formatter: function(value) {
              return value.replace(/(.{6})/g, '$1\n');
            }
          },
          axisTick: {
            show: false  // 隐藏刻度线
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        // Y轴配置
        yAxis: {
          show: false,
          type: 'value',
          name: '金额（万）',
          nameTextStyle: {
            color: '#fff',
            fontSize: this.$autoFontSize(14)
          },
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(14)
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        // 系列配置
        series: [
          // 背景条
          {
            name: "全量背景图",
            type: "bar",
            barGap: "-100%",
            data: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              normal: {
                color: "rgba(63, 169, 245, 0.2)",
              },
            },
            z: 0,
          },
          // 主数据条
          {
            data: this.parkList.map(item => ({
              value: parseFloat(item.value),
              count: item.count
            })),
            type: 'bar',
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: '#40BEFD'
                }, {
                  offset: 1,
                  color: '#032A47'
                }]
              },
              borderRadius: [0, 0, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return `{amount|${params.value}亿元}\n{count|${params.data.count}笔}`;
              },
              rich: {
                amount: {
                  color: '#ffce7c',
                  fontSize: this.$autoFontSize(14),
                  verticalAlign: 'middle',
                  align: 'center',
                  padding: [0, 0, 5, 0]
                },
                count: {
                  color: '#23eaff',
                  fontSize: this.$autoFontSize(14),
                  verticalAlign: 'middle',
                  align: 'center',
                }
              }
            }
          },
          // 白色标记点
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "circle",
            symbolSize: [this.$autoFontSize(12), this.$autoFontSize(6)], // 椭圆
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: this.parkList.map((item, idx) => [
              idx,
              parseFloat(item.value),
            ]),
          }
        ]
      };
      this.myChart.setOption(option);
      this.startAutoScroll(this.parkList.length);
      window.addEventListener('resize', () => {
        this.myChart.resize();
      });
    },
    // 启动自动滚动
    startAutoScroll(totalItems) {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      this.scrollTimer = setInterval(() => {
        const option = this.myChart.getOption();
        const currentData = option.xAxis[0].data;

        // 更新当前索引
        this.currentIndex = (this.currentIndex + this.scrollStep) % totalItems;

        // 重新排序数据
        const newData = [...currentData];
        for (let i = 0; i < this.scrollStep; i++) {
          const item = newData.shift();
          newData.push(item);
        }

        // 更新图表
        this.myChart.setOption({
          xAxis: {
            data: newData,
          },
          series: [
            // 背景条
            {
              name: "全量背景图",
              data: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
            },
            // 主数据
            {
              data: newData.map((name) => {
                const item = this.parkList.find((d) => d.name === name);
                return item
                  ? {
                      value: parseFloat(item.value),
                      count: item.count,
                    }
                  : { value: 0, count: 0 };
              }),
              label: {
                show: true,
                position: "top",
                formatter: function(params) {
                  return `{amount|${params.value}亿元}\n{count|${params.data.count}笔}`;
                },
                rich: {
                  amount: {
                    color: "#ffce7c",
                    fontSize: this.$autoFontSize(14),
                    padding: [0, 0, 5, 0],
                  },
                  count: {
                    color: "#23eaff",
                    fontSize: this.$autoFontSize(14),
                  },
                },
              },
            },
            // 白色标记点
            {
              data: newData.map((name, idx) => {
                const item = this.parkList.find((d) => d.name === name);
                return [idx, item ? parseFloat(item.value) : 0];
              }),
            },
          ],
        });
      }, this.scrollSpeed);
    },
  }
}
</script>

<style scoped lang="scss">
/* 主容器样式 */
.Distribution-szyyClassFour {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 光效样式 */
  .guangClass {
    width: 220px;
    height: 12px;
    background: radial-gradient(397% 76% at 50% 50%, #97CAEB 0%, #048EE6 100%);
    filter: blur(4.338461538461543px);
    position: absolute;
    bottom: 0;
  }

  /* 头部区域样式 */
  .Distribution-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #FFFFFF;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0,175,255,0.68), 0px 2px 4px #000C1A;

    /* 右侧按钮样式 */
    .rightClass {
      display: flex;
      .djgdClassOne {
        position: relative;
        right: -1px;
      }
      .djgdClassOne1 {
        width: 270px;
        height: 56px;
        background: linear-gradient(180deg, #052A53 0%, #033D7B 63%, #0047A8 100%);
        border: 1px solid #1790FF;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #FFFFFF;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
      }
      .djgdClassOne2 {
        width: 270px;
        height: 56px;
        background: linear-gradient(180deg, rgba(5,38,83,0) 0%, rgba(12,74,139,0.49) 100%);
        box-shadow: inset 0px 0px 14px 0px #168AFF;
        border: 2px solid rgba(110,160,227,0.8);
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #67A7FF;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
      }
    }
  }

  /* 右侧按钮组样式 */
  .ChainDistribution-rightClass {
    display: flex;
    align-items: center;
    justify-content: center;

    .ChainDistribution-djgdClass {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26px;
      color: #77c1ff;
      letter-spacing: 1px;
      cursor: pointer;
      margin-right: 12px;
    }

    .ChainDistribution-imgRight {
      width: 12px;
      height: 22px;
      position: relative;
      top: -1px;
    }
  }

  /* 底部内容区域样式 */
  .Distribution-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(180deg, rgba(3,29,58,0) 0%, rgba(0,50,107,0.64) 100%);
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(169deg, rgba(44, 110, 162, 0), rgba(47, 115, 169, 1)) 2 2;
    position: relative;
    overflow: hidden;
    min-height: 300px;
  }
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 30vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bank-chart {
  width: 100%;
  height: 100%;
}

/* 头部统计信息样式 */
.Distribution-industry-park-header {
  color: #fff;
  font-size: 28px;
  margin-bottom: 10px;
  margin-left: 60px;
}

/* 总数样式 */
.Distribution-industry-park-total {
  font-family: OPPOSans, OPPOSans;
  font-weight: bold;
  font-size: 44px;
  color: #FFFFFF;
  line-height: 70px;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #7CEBFF 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 滚动条样式 */
.industry-park-bar-scroll {
  overflow: hidden;
}

/* 柱状图组样式 */
.industry-park-bar-group {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  height: 220px;
  width: max-content;
  min-width: unset;
}

/* 柱状图项样式 */
.industry-park-bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120px;
}

/* 标签样式 */
.industry-park-bar-label {
  text-align: center;
  margin-bottom: 6px;
}

/* 金额样式 */
.industry-park-amount {
  color: #ffce7c;
  font-size: 20px;
  font-weight: bold;
  display: block;
}

/* 笔数样式 */
.industry-park-count {
  color: #23eaff;
  font-size: 18px;
  font-weight: bold;
  display: block;
}

/* 柱状图外框样式 */
.industry-park-bar-outer {
  width: 28px;
  height: 140px;
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  display: flex;
  align-items: flex-end;
  margin-bottom: 8px;
}

/* 柱状图内部样式 */
.industry-park-bar-inner {
  width: 150%;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(180deg, #7cebff 0%, #23eaff 100%);
  transition: height 0.3s;
}

/* 名称样式 */
.industry-park-name {
  color: #fff;
  font-size: 16px;
  margin-top: 8px;
  text-align: center;
  word-break: break-all;
}

/* 图表容器样式 */
.Distribution-park-chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
  box-sizing: border-box;
}
</style>