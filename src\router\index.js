import Vue from 'vue'
import VueRouter from 'vue-router'
import Message from 'element-ui/lib/message'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'login',
    component: () => import(/* webpackChunkName: "login" */ '../views/login.vue'),
    meta: { 
      keepAlive: true,
      requiresAuth: false
    }
  },  
  {
    path: '/pageOne',
    name: 'pageOne',
    component: () => import(/* webpackChunkName: "pageOne" */ '../views/pageOne/pageOne.vue'),
    meta: { 
      keepAlive: true,
      requiresAuth: true
    }
  },  
  {
    path: '/pageTwo',
    name: 'pageTwo',
    component: () => import(/* webpackChunkName: "pageTwo" */ '../views/pageTwo/pageTwo.vue'),
    meta: { 
      keepAlive: true,
      requiresAuth: true
    }
  },
  {
    path: '/pageThree',
    name: 'pageThree',
    component: () => import(/* webpackChunkName: "pageThree" */ '../views/pageThree/pageThree.vue'),
    meta: { 
      keepAlive: true,
      requiresAuth: true
    }
  },
  {
    path: '/pageFour',
    name: 'pageFour',
    component: () => import(/* webpackChunkName: "pageFour" */ '../views/pageFour/DigitalFinance.vue'),
    meta: { 
      keepAlive: true,
      requiresAuth: true
    }
  }
]

const router = new VueRouter({
  routes
})

// 检查登录状态
const checkLoginStatus = () => {
  const userInfo = sessionStorage.getItem('userInfo');
  const currentUsername = sessionStorage.getItem('username');
  
  if (!userInfo || !currentUsername) {
    return false;
  }

  try {
    // 解密用户信息
    const decryptedUserInfo = JSON.parse(atob(userInfo));
    const loginTime = decryptedUserInfo.loginTime;
    const storedUsername = decryptedUserInfo.username;
    const currentTime = new Date().getTime();
    
    // 检查登录是否超过24小时 24 * 60 * 60 * 1000
    if (currentTime - loginTime >24 * 60 * 60 * 1000) {
      // 清除过期信息
      sessionStorage.removeItem('userInfo');
      sessionStorage.removeItem('username');
      return false;
    }
    if(!storedUsername||!currentUsername){
      sessionStorage.removeItem('userInfo');
      sessionStorage.removeItem('username');
      return false;
    }
    // 检查用户名是否匹配
    if (storedUsername !== currentUsername) {
      // 用户名不匹配，清除信息
      sessionStorage.removeItem('userInfo');
      sessionStorage.removeItem('username');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Failed to parse user info:', error);
    return false;
  }
}

// 路由守卫
router.beforeEach(async (to, from, next) => {
  try {
    // 检查是否需要登录权限
    if (to.meta.requiresAuth) {
      const isLoggedIn = checkLoginStatus();
      if (!isLoggedIn) {
        next({ path: '/', replace: true });
        return;
      }
    }  

    // 如果已登录且访问登录页，重定向到首页
    if (to.path === '/' && checkLoginStatus()) {
      next({ path: '/pageOne', replace: true });
      return;
    }  

    // 预加载路由
    if (to.name === 'pageOne') {
      import(/* webpackChunkName: "pageTwo" */ '../views/pageTwo/pageTwo.vue')
    } else if (to.name === 'pageThree') {
      import(/* webpackChunkName: "pageOne" */ '../views/pageOne/pageOne.vue')
    } else if (to.name === 'pageFour') {
      import(/* webpackChunkName: "pageFour" */ '../views/pageFour/DigitalFinance.vue')
    } else if (to.name === 'pageTwo') {
      import(/* webpackChunkName: "pageThree" */ '../views/pageThree/pageThree.vue')
    }
    
    next();
  } catch (error) {
    const isLoggedIn = checkLoginStatus();
    if (!isLoggedIn) {
      // 未登录或登录已过期，直接重定向到登录页
      next({ path: '/', replace: true });
      return;
    }
  }
})



export default router
