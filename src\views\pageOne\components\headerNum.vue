<!--
  头部数据指标组件
  功能：
  1. 展示关键业务指标数据
  2. 支持数据动画效果
  3. 提供数据变化提示
  4. 支持点击查看详细趋势
  5. 支持双弹框展示
-->
<template>
  <div class="headerNumClass">
    <!-- 累计注册用户卡片 -->
    <div class="itemClass" @click="clickOne">
      <div class="itemHeader">
        <div class="numdClass">
          <!-- 数字动画组件 -->
          <countTo
            class="countto"
            :startVal="prevHeaderNum1"
            :endVal="headerNum1"
            :duration="1000"
            @mounted="onCountMounted"
            @callback="onCountCallback"
          ></countTo>
          <!-- 数据变化提示 -->
          <transition name="fade-tooltip">
            <div v-if="showTooltip1" class="tooltip">
              +{{ headerNum1 - prevHeaderNum1 }}
            </div>
          </transition>
        </div>
        <div class="huClass">人</div>
      </div>
      <div class="textName">累计注册用户</div>
    </div>

    <!-- 累计认证企业卡片 -->
    <div class="itemClass" @click="clickTwo">
      <div class="itemHeader">
        <div class="numdClass">
          <countTo
            class="countto"
            :startVal="prevHeaderNum2"
            :endVal="headerNum2"
            :duration="1000"
            @mounted="onCountMounted"
            @callback="onCountCallback"
          ></countTo>
          <transition name="fade-tooltip">
            <div v-if="showTooltip2" class="tooltip">
              +{{ headerNum2 - prevHeaderNum2 }}
            </div>
          </transition>
        </div>
        <div class="huClass">户</div>
      </div>
      <div class="textName">累计认证企业</div>
    </div>

    <!-- 累计放款户数卡片 -->
    <div class="itemClass" @click="clickThree">
      <div class="itemHeader">
        <div class="numdClass">
          <countTo
            class="countto"
            :startVal="prevHeaderNum3"
            :endVal="headerNum3"
            :duration="1000"
            @mounted="onCountMounted"
            @callback="onCountCallback"
          ></countTo>
          <transition name="fade-tooltip">
            <div v-if="showTooltip3" class="tooltip">
              +{{ headerNum3 - prevHeaderNum3 }}
            </div>
          </transition>
        </div>
        <div class="huClass">户</div>
      </div>
      <div class="textName">累计放款户数</div>
    </div>

    <!-- 累计放款金额卡片 -->
    <div class="itemClass" @click="clickFour">
      <div class="itemHeader">
        <div class="numdClass">
          <countTo
            class="countto"
            :startVal="prevHeaderNum4"
            :endVal="headerNum4"
            :decimals="2"
            :duration="1000"
            @mounted="onCountMounted"
            @callback="onCountCallback"
          ></countTo>
          <transition name="fade-tooltip">
            <div
              v-if="showTooltip4"
              class="tooltip"
              style="margin-right: -20px"
            >
              +{{ (headerNum4 - prevHeaderNum4).toFixed(2) }}
            </div>
          </transition>
        </div>
        <div class="huClass">亿</div>
      </div>
      <div class="textName">累计放款金额</div>
    </div>

    <!-- 平均放款利率卡片 -->
    <div class="itemClass" @click="clickFive">
      <div class="itemHeader">
        <div class="numdClass">
          <countTo
            class="countto"
            :startVal="1"
            :endVal="headerNum5"
            :decimals="2"
            :duration="3000"
          ></countTo>
          %
        </div>
      </div>
      <div class="textName">平均放款利率</div>
    </div>

    <!-- 单弹框 -->
    <div v-if="!showDoubleDialog && !showDialogRate">
      <dialogVue
        :visible.sync="showDialog"
        :chartData="chartData"
        :yLable="yLable"
        :title="title"
        :indexName="indexName"
        ref="dialogRef"
      />
    </div>

    <!-- 双弹框并排容器 -->
    <div
      v-if="showDoubleDialog && !showDialogRate"
      class="double-dialog-container"
    >
      <dialogVue
        :visible.sync="showDoubleDialog"
        :chartData="chartData"
        :yLable="yLable"
        :title="title"
        :indexName="indexName"
        custom-class="dialog-left-25"
        @close="closeDoubleDialog"
        ref="dialogRef"
      />
      <dialogLoopVue
        :visible="true"
        :chartData="chartDataLoop"
        :yLable="doubleYLable2"
        :title="doubleTitle2"
        custom-class="dialog-right-25"
        @close="closeDoubleDialog"
      />
    </div>

    <!-- 利率弹框 -->
    <div v-if="showDialogRate">
      <dialogRateVue
        :visible.sync="showDialogRate"
        :chartData="chartData"
        :yLable="yLable"
        :title="title"
        :maxDt="maxDt"
      />
    </div>
  </div>
</template>

<script>
import dialogVue from "./dialog.vue";
import dialogLoopVue from "./dialogLoop.vue";
import dialogRateVue from "./dialogRate.vue";
import countTo from "vue-count-to";
import { queryLineChartData, queryRealtimeMetricList } from "@/api/article.js";

export default {
  name: "headerNum",
  components: { countTo, dialogVue, dialogLoopVue, dialogRateVue },
  // 组件属性定义
  props: {
    maxDt: {
      type: String,
      required: true,
    },
    isMaxDtReady: {
      type: Boolean,
      required: true,
    },
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  // 组件数据
  data() {
    return {
      indexName: "", // 指标名称
      // 图表数据配置
      chartData: {
        xAxis: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月",
        ],
        yAxis: ["100", "200", "300", "400", "500"],
        series: [320, 400, 350, 238, 300, 330, 310, 370, 340, 420, 410, 400],
      },
      showDialog: false, // 单弹框显示状态
      showDialogLoop: false, // 循环弹框显示状态
      yLable: "", // Y轴标签
      title: "累计注册用户", // 标题
      loopTitle: "", // 循环标题
      // 循环图表数据
      chartDataLoop: {
        xAxisData: [],
        seriesData: [],
      },
      // 双弹框相关数据
      showDoubleDialog: false,
      doubleChartData1: {},
      doubleChartData2: {},
      doubleYLable1: "",
      doubleYLable2: "",
      doubleTitle1: "",
      doubleTitle2: "",
      // 数据指标值
      headerNum1: 0, // 累计注册用户
      headerNum2: 0, // 累计认证企业
      headerNum3: 0, // 累计放款户数
      headerNum4: 0, // 累计放款金额
      headerNum5: 0, // 平均放款利率
      timer: null, // 定时器
      showDialogRate: false, // 利率弹框显示状态
      // 数据变化相关
      prevHeaderNum1: 0, // 上一次的注册用户数
      prevHeaderNum2: 0, // 上一次的认证企业数
      prevHeaderNum3: 0, // 上一次的放款户数
      prevHeaderNum4: 0, // 上一次的放款金额
      showTooltip1: false, // 注册用户提示显示状态
      showTooltip2: false, // 认证企业提示显示状态
      showTooltip3: false, // 放款户数提示显示状态
      showTooltip4: false, // 放款金额提示显示状态
      canShowTooltip: false, // 是否允许显示提示
      // API数据缓存
      prevApiData: {
        headerNum1: 0,
        headerNum2: 0,
        headerNum3: 0,
        headerNum4: 0,
        headerNum5: 0,
      },
      apiTimer: null, // API调用定时器
      count: 0, // 计数器
    };
  },
  // 监听器
  watch: {
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用其他接口
          this.init();
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        // 清除旧定时器避免重复
        if (this.apiTimer) {
          clearInterval(this.apiTimer);
          this.apiTimer = null;
        }
        // 立即调用一次初始化
        this.init();
        // 创建新定时器（使用箭头函数保持this上下文）
        this.apiTimer = setInterval(() => {
          this.init();
        }, 30000);
      }
    },
  },
  mounted() {
    // 5秒后允许显示tooltip
    setTimeout(() => {
      this.canShowTooltip = true;
    }, 5000);

    // 设置定时器，每30秒调用一次
    this.apiTimer = setInterval(() => {
      this.init();
    }, 30000);
  },

  beforeDestroy() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    if (this.apiTimer) {
      clearInterval(this.apiTimer);
      this.apiTimer = null;
    }
  },
  methods: {
    // 数字动画组件挂载回调
    onCountMounted() {
      // 初始化时保存当前值作为前一个值
      this.prevHeaderNum1 = this.headerNum1;
      this.prevHeaderNum2 = this.headerNum2;
      this.prevHeaderNum3 = this.headerNum3;
      this.prevHeaderNum4 = this.headerNum4;
    },
    // 数字动画完成回调
    onCountCallback() {
      // 数字变化完成后隐藏提示
      setTimeout(() => {
        this.showTooltip1 = false;
        this.showTooltip2 = false;
        this.showTooltip3 = false;
        this.showTooltip4 = false;
      }, 1000);
    },
    // 初始化数据
    init() {
      // 调用实时指标接口
      queryRealtimeMetricList([
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "累计注册用户",
          dimNum: 1,
        },
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "累计认证企业",
          dimNum: 0,
        },
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "累计放款户数",
          dimNum: 0,
        },
        {
          indexName: "累计放款金额",
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          dimNum: 0,
        },
        {
          bizDate: this.maxDt,
          dimTime: "ALL",
          dimIndustry: "ALL",
          // dimCounty: "ALL",
          dimCounty: this.selectedRegion || "ALL",
          dimPark: "ALL",
          dimIndustryChain: "ALL",
          indexName: "平均融资利率",
          dimNum: 0,
        },
      ])
        .then((res) => {
          if (res.data.code == 200) {
            // 确保data数组存在且长度足够
            if (!res.data.data || res.data.data.length < 5) {
              console.warn("API返回数据不完整");
              // 重置当前值和缓存值
              this.headerNum1 = 0;
              this.headerNum2 = 0;
              this.headerNum3 = 0;
              this.headerNum4 = 0;
              this.headerNum5 = 0;

              // 更新前值缓存
              this.prevApiData = {
                headerNum1: 0,
                headerNum2: 0,
                headerNum3: 0,
                headerNum4: 0,
                headerNum5: 0,
              };

              // 强制隐藏所有tooltip
              this.showTooltip1 = false;
              this.showTooltip2 = false;
              this.showTooltip3 = false;
              this.showTooltip4 = false;

              return; // 提前返回
            }

            // 安全地转换数值
            const safeGetValue = (item) => {
              return item ? Number(item.indexValue) || 0 : 0;
            };

            // 保存旧值用于动画
            this.prevHeaderNum1 = this.headerNum1;
            this.prevHeaderNum2 = this.headerNum2;
            this.prevHeaderNum3 = this.headerNum3;
            this.prevHeaderNum4 = this.headerNum4;

            // 计算差值
            const diff1 =
              safeGetValue(res.data.data[0]) - this.prevApiData.headerNum1;
            const diff2 =
              safeGetValue(res.data.data[1]) - this.prevApiData.headerNum2;
            const diff3 =
              safeGetValue(res.data.data[2]) - this.prevApiData.headerNum3;
            const diff4 =
              safeGetValue(res.data.data[3]) - this.prevApiData.headerNum4;

            // 更新新值
            this.headerNum1 = safeGetValue(res.data.data[0]);
            this.headerNum2 = safeGetValue(res.data.data[1]);
            this.headerNum3 = safeGetValue(res.data.data[2]);
            this.headerNum4 = safeGetValue(res.data.data[3]);
            this.headerNum5 = safeGetValue(res.data.data[4]) || 3.95;

            // 更新API数据缓存
            this.prevApiData = {
              headerNum1: this.headerNum1,
              headerNum2: this.headerNum2,
              headerNum3: this.headerNum3,
              headerNum4: this.headerNum4,
              headerNum5: this.headerNum5,
            };

            // 只在差值大于0且允许显示tooltip时显示提示
            if (diff1 > 0 && this.canShowTooltip) this.showTooltip1 = true;
            if (diff2 > 0 && this.canShowTooltip) this.showTooltip2 = true;
            if (diff3 > 0 && this.canShowTooltip) this.showTooltip3 = true;
            if (diff4 > 0 && this.canShowTooltip) this.showTooltip4 = true;

            // 1.5秒后隐藏提示
            setTimeout(() => {
              this.showTooltip1 = false;
              this.showTooltip2 = false;
              this.showTooltip3 = false;
              this.showTooltip4 = false;
            }, 1500);
          }
        })
        .catch((error) => {
          console.error("获取数据失败:", error);
        });
    },
    // 累计注册用户点击事件
    async clickOne() {
      this.indexName = "累计注册用户";
      let currentYear = new Date().getFullYear();
      const res = await queryLineChartData({
        bizDate: `${currentYear}`,
        type: "年",
        dimTime: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: this.indexName,
      });
      this.$nextTick(() => {
        this.showDialog = true;
        this.$refs.dialogRef.currentTab = "年";
        this.chartData.xAxis = res.data.data.dataList;
        this.chartData.series = res.data.data.countList;
        this.chartData.yAxis = [10000, 20000, 30000, 40000, 50000];
        this.yLable = "单位：户";
        this.title = "累计注册用户趋势";
      });
    },
    // 累计认证企业点击事件
    async clickTwo() {
      this.indexName = "累计认证企业";
      this.title = "累计认证企业趋势";
      let currentYear = new Date().getFullYear();
      const res = await queryLineChartData({
        bizDate: `${currentYear}`,
        type: "年",
        dimTime: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: this.indexName,
      });
      this.$nextTick(() => {
        this.$refs.dialogRef.currentTab = "年";
        this.chartData.xAxis = res.data.data.dataList;
        this.chartData.series = res.data.data.countList;
        this.yLable = "单位：户";
        this.showDialog = true;
      });
    },
    // 累计放款户数点击事件
    async clickThree() {
      this.indexName = "累计放款户数";
      this.title = "累计放款户数趋势";
      let currentYear = new Date().getFullYear();
      const res = await queryLineChartData({
        bizDate: `${currentYear}`,
        type: "年",
        dimTime: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: this.indexName,
      });
      this.$nextTick(() => {
        this.$refs.dialogRef.currentTab = "年";
        this.chartData.xAxis = res.data.data.dataList;
        this.chartData.series = res.data.data.countList;
        this.yLable = "单位：户";
        this.showDialog = true;
      });
    },
    // 关闭双弹框
    closeDoubleDialog() {
      this.showDoubleDialog = false;
    },
    // 累计放款金额点击事件
    async clickFour() {
      this.indexName = "累计放款金额";
      this.title = "累计放款金额趋势";
      let currentYear = new Date().getFullYear();
      const res = await queryLineChartData({
        bizDate: `${currentYear}`,
        type: "年",
        dimTime: "ALL",
        dimIndustry: "ALL",
        // dimCounty: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: this.indexName,
      });
      this.$nextTick(() => {
        this.$refs.dialogRef.currentTab = "年";
        this.chartData.xAxis = res.data.data.dataList;
        this.chartData.series = res.data.data.countList;
        this.yLable = "单位：亿元";
        this.showDoubleDialog = true;
      });
    },
    // 平均放款利率点击事件
    clickFive() {
      this.showDialogRate = true;
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 头部数据指标容器 */
.headerNumClass {
  display: flex;
  width: 1700px;
  justify-content: space-between;
  padding-top: 1vh;
  margin-left: 100px;
  height: auto;

  /* 数据卡片样式 */
  .itemClass {
    cursor: pointer;

    /* 卡片头部样式 */
    .itemHeader {
      display: flex;
      justify-content: space-evenly;
      align-items: baseline;

      /* 数字容器样式 */
      .numdClass {
        display: flex;
        font-family: OPPOSans, OPPOSans;
        font-weight: bold;
        font-size: 51px;
        color: #ffffff;
        letter-spacing: 2px;
        text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
        background: linear-gradient(180deg, #ffffff 0%, #f5873d 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;
        display: flex;
        align-items: center;

        /* 数据变化提示样式 */
        .tooltip {
          position: absolute;
          top: 30px;
          right: -120px;
          background: none;
          color: transparent;
          background: linear-gradient(180deg, #3a8bff 0%, #00cfff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-size: 32px;
          padding: 2px 8px;
          border-radius: 4px;
          font-weight: bold;
          animation: floatUp 4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }
      }

      /* 单位样式 */
      .huClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        letter-spacing: 1px;
        margin-left: 6px;
      }
    }

    /* 指标名称样式 */
    .textName {
      width: 230px;
      margin-top: 10px;
      height: 60px;
      background-image: url("~@/assets/dp/hkhf.png");
      background-repeat: no-repeat;
      background-size: 230px 60px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      letter-spacing: 1px;
    }
  }
}

/* 弹框位置样式 */
.dialog-left-25 {
  transform: translateX(-50%);
}
.dialog-right-25 {
  transform: translateX(100%);
}

/* 双弹框容器样式 */
.double-dialog-container {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 24, 48, 0.6);
  .dialog-content {
    margin: 0 30px;
  }
}

/* 数据变化提示动画 */
@keyframes floatUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  25% {
    opacity: 1;
    transform: translateY(0);
  }
  60% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-15px);
  }
}

/* 过渡动画样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.fade-tooltip-enter-active,
.fade-tooltip-leave-active {
  transition: all 2s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-tooltip-enter,
.fade-tooltip-leave-to {
  opacity: 0;
  transform: translateY(-15px);
}
</style>
