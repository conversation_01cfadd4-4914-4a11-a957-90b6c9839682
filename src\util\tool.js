export const getDateTime = (dayNum) => {
    var dateDay = new Date()
    dateDay.setDate(dateDay.getDate() + dayNum)
    var yyyy = dateDay.getFullYear()
    var mm = (dateDay.getMonth() + 1) < 10 ? '0' + (dateDay.getMonth() + 1) : dateDay.getMonth() + 1
    var dd = dateDay.getDate()<10?'0' + dateDay.getDate() : dateDay.getDate()
    return yyyy + '-' + mm + '-' + dd
  }
  export const getDateCalc = (date, dayNum) => {
    var dateDay = new Date(date)
    dateDay.setDate(dateDay.getDate() + dayNum)
    var yyyy = dateDay.getFullYear()
    var mm = (dateDay.getMonth() + 1) < 10 ? '0' + (dateDay.getMonth() + 1) : dateDay.getMonth() + 1
    var dd = dateDay.getDate()<10?'0' + dateDay.getDate() : dateDay.getDate()
    return yyyy + '-' + mm + '-' + dd
  }