<!--
  切换按钮组件
  功能：
  1. 提供两个选项的切换功能（区县放款情况/企业走访轨迹）
  2. 支持高亮显示当前选中项
  3. 支持地图视图切换
  4. 支持自定义样式和动画效果
-->
<template>
  <div class="changeButtomClass1">
    <!-- 区县放款情况按钮 -->
    <div class="checkFont1 lBox" :class="{ active: flagFont }" @click="selectTab(0)">
      <span>区（市）县放款情况</span>
    </div>
    <!-- 分隔线 -->
    <div class="lineClass1"></div>
    <!-- 企业走访轨迹按钮 -->
    <div class="checkFont1 rBox" :class="{ active: !flagFont }" @click="selectTab(1)">
      <span>企业走访轨迹</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "changeButtom",
  // 组件属性定义
  props: {
    flagFont: {
      type: Boolean,
      required: true, // 控制按钮高亮状态
    },
  },
  components: {},
  // 组件数据
  data() {
    return {
      activeIndex: 0, // 当前选中的按钮索引
      flag: false, // 控制按钮状态的标志
    };
  },
  // 监听器
  watch: {
    flag(newVal) {
      // flag为true时高亮左侧，为false时高亮右侧
      this.activeIndex = newVal ? 0 : 1;
    },
  },
  // 生命周期钩子
  mounted() {
    // 初始化时根据flag设置高亮
    this.activeIndex = this.flag ? 0 : 1;
  },
  methods: {
    // 切换到地图视图
    goMap() {
      this.$emit("goPage", true);
    },
    // 处理按钮点击事件
    selectTab(index) {
      if (index === 0) {
        // 切换到省地图
        this.$emit("goPage", true);
      } else {
        // 打开map_blue的地图
        this.$emit("goPage", false);
        // 触发返回成都市地图事件
        this.$emit("changeCity", false);
      }
      this.activeIndex = index;
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 切换按钮容器 */
.changeButtomClass1 {
  z-index: 100;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 10px;

  /* 分隔线样式 */
  .lineClass1 {
    width: 1px;
    height: 20px;
    margin: 0 30px;
    background: #ffffff;
    position: relative;
    top: -6px;
  }

  /* 企业走访轨迹按钮样式 */
  .qyzfgjClass {
    width: 240px;
    height: 56px;
    cursor: pointer;
  }

  /* 按钮基础样式 */
  .checkFont1 {
    height: 56px;
    font-family: YouSheBiaoTiHei;
    font-size: 43px;
    color: #ffffff;
    cursor: pointer; // 添加手势
    line-height: 56px;
    text-align: right;
    font-style: normal;
    z-index: 101;

    /* 按钮激活状态样式 */
    &.active {
      letter-spacing: 2px;
      text-shadow: 0px 2px 2px rgba(0, 0, 0, 0);
      // 渐变背景效果
      background: linear-gradient(180deg, #FFFFFF 0%, #FFCE7C 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
</style>
