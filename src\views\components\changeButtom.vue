<template>
  <div class="changeButtomClass">
    <div class="icon-container">
      <div class="left-icon"></div>
      <div class="checkFont">{{ title }}</div>
      <div class="right-icon"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TitleHeader",
  props: {
    title: {
      type: String,
      required: true,
      default: '标题'
    }
  }
};
</script>

<style scope="scoped" lang="scss">
.changeButtomClass {
  width: 50%;
  margin-left: 75%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 10px;
  .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .left-icon {
    width: 65.5px;
    height: 62.93px;
    margin-right: 25px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('@/assets/dp/left.png') no-repeat ;
    background-size : 65.5px 62.93px;
  }
  
  .right-icon {
    width: 65.5px;
    height: 62.93px;
    margin-left: 25px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('@/assets/dp/right.png')  no-repeat;
    background-size : 65.5px 62.93px;
  }

}

/* 图标呼吸效果 */
@keyframes pulse-left {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse-right {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 文字光效动画 */
@keyframes shine {
  0% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.2);
  }
  100% {
    filter: brightness(1);
  }
}
.checkFont{
  font-family: YouSheBiaoTiHei;
  font-weight: normal;
  font-size: 44px;
  color: #FFFFFF !important;
  line-height: 70px;
  letter-spacing: 2px;
  text-align: left;
  font-style: normal;
}
</style>
