<!--
  数据趋势弹窗组件
  功能：
  1. 展示数据趋势的折线图
  2. 支持日、月、年数据切换
  3. 提供图表交互和提示
  4. 支持自定义标题和单位
  5. 支持图表自适应和响应式
-->
<template>
  <div v-if="visible" class="dialog-mask">
    <!-- 弹窗内容区域 -->
    <div class="dialog-content">
      <!-- 头部区域：标题和标签组 -->
      <div class="header-box">
        <div class="dialog-header">
          <div class="titleBox">
            <span class="title">{{ title }}</span>
          </div>
        </div>
        <!-- 标签切换组 -->
        <div class="tab-group">
          <div
            v-for="item in tabList"
            :key="item"
            :class="['tab-btn', { active: currentTab === item }]"
            @click="changeTab(item)"
          >
            {{ item }}
          </div>
        </div>
        <!-- 关闭按钮 -->
        <button class="dialog-close" @click="close">×</button>
      </div>

      <!-- 图表主体区域 -->
      <div class="dialog-body">
        <div ref="chartRef" class="chart"></div>
      </div>
      <!-- 底部插槽区域 -->
      <div class="dialog-footer">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "EchartsDialog",
  props: {
    // 控制弹窗显示
    visible: { type: Boolean, default: false },
    // 弹窗标题
    title: { type: String, default: "用户注册趋势" },
    // 图表数据
    chartData: {
      type: Object,
      default: () => ({
        xAxis: [
          "2025年",
          "2024年",
          "2023年",
        ],
        yAxis: [100, 200, 300, 400, 500],
        series: [
          320, 400, 350
        ],
      }),
    },
  },
  data() {
    return {
      chart: null, // ECharts实例
      tabList: ["日", "月", "年"], // 标签列表
      currentTab: "年", // 当前选中的标签
      // 日数据配置
      dayData: {
        xAxis: Array.from({ length: 30 }, (_, i) => `${i + 1}日`),
        yAxis: [100, 200, 300, 400, 500],
        series: Array.from({ length: 30 }, () =>
          Math.floor(Math.random() * 400 + 100)
        ),
      },
      // 月数据配置
      monthData: {
        xAxis: [
          "1月", "2月", "3月", "4月", "5月", "6月",
          "7月", "8月", "9月", "10月", "11月", "12月",
        ],
        yAxis: [100, 200, 300, 400, 500],
        series: [320, 400, 350, 238, 300, 330, 310, 370, 340, 420, 410, 400],
      },
      // 年数据配置
      yearData: {
        xAxis: [
          `${new Date().getFullYear() - 2}年`,
          `${new Date().getFullYear() - 1}年`,
          `${new Date().getFullYear()}年`,
        ],
        yAxis: [100, 200, 300, 400, 500],
        series: [1200, 1500, 1800],
      },
      chartNewData: {}, // 当前展示的数据
      activeIndex: null, // 当前高亮的x轴索引
      dayTooltip: "", // 日数据提示配置
      currentChartData: {}, // 用于展示的chart数据
    };
  },
  created() {
    // 默认显示年数据
    this.chartNewData = this.yearData;
    this.currentChartData = { ...this.yearData }
  },
  watch: {
    // 监听标签切换
    changeTab(tab) {
      this.currentTab = tab;
      // 切换数据
      if (tab === "日") {
        this.chartNewData = this.dayData;
      } else if (tab === "月") {
        this.chartNewData = this.monthData;
      } else if (tab === "年") {
        this.chartNewData = this.yearData;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    },
    // 监听弹窗显示状态
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.initChart();
        });
      } else {
        // 关闭时销毁实例，避免下次打开无数据
        if (this.chart) {
          this.chart.dispose();
          this.chart = null;
        }
      }
    },
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
      // 关闭时销毁实例，避免下次打开无数据
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
    },
    // 切换标签
    changeTab(tab) {
      this.currentTab = tab;
      // 切换到日视图时，生成过去30天日期
      if (tab === "日") {
        const days = [];
        const today = new Date();
        for (let i = 29; i >= 0; i--) {
          const d = new Date(today);
          d.setDate(today.getDate() - i);
          // 格式化为 MM-DD
          const mm = String(d.getMonth() + 1).padStart(2, "0");
          const dd = String(d.getDate()).padStart(2, "0");
          days.push(`${mm}-${dd}`);
        }
        this.dayData.xAxis = days;
        // 同步更新日数据系列
        this.dayData.series = Array.from({ length: 30 }, () =>
          Math.floor(Math.random() * 400 + 100)
        );
        // 配置日数据提示框
        this.dayTooltip = {
          trigger: "axis",
          axisPointer: {
            type: "line",
            lineStyle: { color: "#fff" },
          },
          backgroundColor: "rgba(0,0,0,0.7)",
          borderColor: "#fff",
          borderWidth: 1,
          padding: 10,
          formatter: function (params) {
            if (params && params.length) {
              return `${params[0].axisValue}<br/>数值：${params[0].data}`;
            }
            return "";
          },
        };
        this.currentChartData = { ...this.dayData };
      } else if (tab === "月") {
        this.chartData = this.monthData;
        this.currentChartData = { ...this.monthData }
      } else if (tab === "年") {
        // 切换到年视图时，动态生成近三年
        const now = new Date();
        const years = [
          `${now.getFullYear() - 2}年`,
          `${now.getFullYear() - 1}年`,
          `${now.getFullYear()}年`,
        ];
        this.yearData.xAxis = years;
        this.currentChartData = { ...this.yearData };
        this.chartNewData = this.yearData;
      }
      this.$nextTick(() => {
        this.initChart();
      });
    },
    // 初始化图表
    initChart() {
      if (!this.$refs.chartRef) return;
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef);
      }
      const that = this;
      const option = {
        // 图表网格配置
        grid: {
          top: "20%",
          bottom: "10%",
          left: "8%",
          right: "5%",
          borderColor: "#2A4B7C",
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: this.currentChartData.xAxis,
          axisLine: { lineStyle: { color: "#6EC6FF" } },
          axisTick: {
            show: false  // 隐藏刻度线
          },
          axisLabel: {
            color: function (value, idx) {
              // 高亮当前activeIndex的x轴标签为橙色
              return idx === that.activeIndex ? "#FF9C00" : "#fff";
            },
          },
        },
        // Y轴配置
        yAxis: {
          type: "value",
          data: this.currentChartData.yAxis,
          axisLine: { lineStyle: { color: "#6EC6FF" } },
          splitLine: { lineStyle: { color: "#2A4B7C" } },
          axisLabel: { color: "#fff" },
        },
        // 提示框配置
        tooltip: this.dayTooltip
          ? this.dayTooltip
          : {
              trigger: "axis",
              axisPointer: {
                type: "line",
                lineStyle: { color: "#fff" },
              },
              backgroundColor: "rgba(0,0,0,0.7)",
              borderColor: "#fff",
              borderWidth: 1,
              padding: 10,
              formatter: function () {
                return ""; // 不显示内容
              },
            },
        // 系列配置
        series: [
          {
            data: this.currentChartData.series,
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 0,
            // 线条样式配置
            lineStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "#ffffff" }, // 渐变起始色
                { offset: 0.3, color: "#3bbcff" }, // 渐变结束色
                { offset: 0.3, color: "#ffffff" }, // 渐变起始色
                { offset: 0.6, color: "#3bbcff" }, // 渐变结束色
                { offset: 0.6, color: "#ffffff" }, // 渐变起始色
                { offset: 1, color: "#3bbcff" }, // 渐变结束色
              ]),
              width: 2.5,
            },
            itemStyle: { color: "#6EC6FF" },
            // 标签配置
            label: {
              show: true,
              position: "top",
              color: "#fff",
              fontSize: 18,
              formatter: function (params) {
                // 只在高亮点显示数值
                if (
                  that.activeIndex !== null &&
                  params.dataIndex === that.activeIndex
                ) {
                  return params.data;
                }
                return "";
              },
            },
            // 高亮配置
            emphasis: {
              focus: "series",
              symbol: "circle",
              symbolSize: 12,
              itemStyle: {
                color: "#FF9C00", // 橙色小圆点
                borderColor: "#fff",
                borderWidth: 5,
              },
            },
          },
        ],
      };
      this.chart.setOption(option);
      this.chart.resize(); // 自适应容器

      // 监听提示框显示事件
      this.chart.on("showTip", (params) => {
        if (params.dataIndex !== undefined) {
          this.activeIndex = params.dataIndex;
          this.chart.setOption({
            xAxis: {
              axisLabel: {
                color: (value, idx) =>
                  idx === this.activeIndex ? "#FF9C00" : "#fff",
              },
            },
            series: [
              {
                label: {
                  show: true,
                  color: "#fff",
                  fontSize: 22,
                  formatter: (params) => {
                    if (params.dataIndex === this.activeIndex) {
                      return params.data;
                    }
                    return "";
                  },
                },
              },
            ],
          });
        }
      });

      // 监听提示框隐藏事件
      this.chart.on("hideTip", () => {
        this.activeIndex = null;
        this.chart.setOption({
          xAxis: {
            axisLabel: {
              color: "#fff",
            },
          },
          series: [
            {
              label: {
                show: false,
              },
            },
          ],
        });
      });
    },
  },
  mounted() {
    if (this.visible) {
      this.initChart();
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
};
</script>

<style scoped lang="scss">
/* 遮罩层样式 */
.dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 24, 48, 0.6);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 标题框样式 */
.titleBox {
  background: linear-gradient(
    270deg,
    rgba(6, 145, 255, 0) 0%,
    rgba(6, 145, 255, 0.34) 100%
  );
  width: 727px;
  height: 110px;
  .title {
    width: 314px;
    height: 52px;
    font-family: PangMenZhengDao;
    font-size: 52px;
    color: #ffffff;
    line-height: 110px;
    margin-left: 50px;
    letter-spacing: 4px;
    text-shadow: 0px 0px 4px rgba(65, 158, 255, 0.89);
    text-align: left;
    font-style: normal;
  }
}

/* 弹窗内容样式 */
.dialog-content {
  background: linear-gradient(180deg, #0a1a2a 0%, #173a5c 100%);
  border: 2px solid #6ec6ff;
  border-radius: 12px;
  min-width: 1600px;
  min-height: 900px;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
}

/* 头部区域样式 */
.header-box {
  display: flex;
}

/* 弹窗头部样式 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 24px 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 标签组样式 */
.tab-group {
  display: flex;
  margin-left: 300px;
  margin-right: 30px;
  margin-top: 30px;
  height: 36px;
  position: relative;
}

/* 标签按钮样式 */
.tab-btn {
  font-family: PingFangSC;
  font-weight: 400;
  font-size: 34px;
  line-height: 47px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  width: 140px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  color: #67a7ff;
  border: 1px solid #4ea6ff;
  cursor: pointer;
  margin-left: -1px;
  transition: background 0.2s, color 0.2s;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  );
  box-shadow: inset 0px 0px 18px 0px #168aff;
  border: 2px solid rgba(110, 160, 227, 0.8);
}

/* 激活状态标签按钮样式 */
.tab-btn.active {
  background: #2176c7;
  color: #fff;
  border: 1px solid #2176c7;
  z-index: 1;
}

/* 关闭按钮样式 */
.dialog-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 60px;
  position: absolute;
  top: -100px;
  right: 0;
  padding: 0;
  cursor: pointer;
}

/* 图表主体区域样式 */
.dialog-body {
  padding: 10px 24px 0 24px;
}

/* 图表容器样式 */
.chart {
  width: 100%;
  height: 800px;
}

/* 底部区域样式 */
.dialog-footer {
  padding: 10px 24px 18px 24px;
  text-align: right;
}
</style>
