<!--
  入驻金融机构组件
  功能：
  1. 展示金融机构总数和分布情况
  2. 使用饼图展示各类金融机构占比
  3. 支持点击查看详细数据
  4. 支持数据下钻和弹窗展示
-->
<template>
  <div class="fkyhlbClass">
    <!-- 标题区域 -->
    <div class="headerImg">入驻金融机构</div>
    <div class="bottomClass2">
      <div class="circleClass">
        <!-- 饼图容器 -->
        <div id="chart" style="width: 50%; height: 100%"></div>
        <!-- 总数展示区域 -->
        <div class="rzjgClass">
          <div class="numClass">{{ totol }}</div>
          <div class="rzjgName">入驻机构</div>
        </div>
        <!-- 图例区域 -->
        <div class="custom-legend">
          <!-- 图例项列表 -->
          <div
            v-for="(item, index) in listData"
            :key="item.financeType"
            class="legend-item"
            @click="showDetailDialog(item)"
            style="cursor: pointer"
          >
            <span
              class="legend-color"
              :style="{ background: getLegendColor(index) }"
            ></span>
            <span class="legend-name">{{ item.financeType }}</span>
            <span class="legend-value" :style="{ color: getLegendColor(index) }"
              >{{ item.financeNum }}<span class="legend-unit">家</span></span
            >
          </div>
          <!-- 下钻明细区域 -->
          <div
            v-if="
              activeIndex !== null &&
              listData[activeIndex] &&
              listData[activeIndex].children
            "
            class="legend-detail"
          >
            <div
              v-for="child in listData[activeIndex].children"
              :key="child.name"
              class="legend-detail-item"
            >
              <span class="detail-name">{{ child.name }}：</span>
              <span class="detail-value"
                >{{ child.value }}<span class="detail-unit">家</span></span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 详细数据弹窗 -->
    <el-dialog
      :visible.sync="detailDialogVisible"
      :title="detailDialogTitle"
      class="rzjrjg-dialog"
      :close-on-click-modal="true"
      :show-close="false"
      center
    >
      <div class="drill-dialog-content">
        <div
          v-for="child in detailDialogList"
          :key="child.name"
          class="drill-dialog-row"
        >
          {{ child.financeType }}： <span>{{ child.financeSum }}</span
          >家
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdate } from "@/api/article.js";

export default {
  name: "rzjrjg",
  // 组件数据
  data() {
    return {
      listData: [], // 金融机构列表数据
      totol: "", // 总数
      myChart: "", // ECharts实例
      activeIndex: null, // 当前展开的legend索引
      detailDialogVisible: false, // 详情弹窗显示状态
      detailDialogTitle: "", // 详情弹窗标题
      detailDialogList: [], // 详情弹窗数据列表
      visible: false, // 弹窗显示状态
      clickPosition: { x: 0, y: 0 }, // 点击位置
    };
  },
  // 组件属性
  props: {
    maxDt: {
      type: String,
      required: true, // 最大日期
    },
    isMaxDtReady: {
      type: Boolean,
      required: true, // 日期是否准备就绪
    },
    //区域
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  // 生命周期钩子
  mounted() {
    this.$nextTick(() => {
      this.init();
    });
  },
  beforeDestroy() {
    // 移除监听，避免内存泄漏
    window.removeEventListener("resize", this.handleResize);
  },
  // 监听器
  watch: {
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用其他接口
          this.getrzjrjg();
          window.addEventListener("resize", this.handleResize);
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.getrzjrjg(newVal);
      },
    },
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
    // 从字符串中提取数字
    extractNumber(str) {
      if (!str) return 0;
      const match = str.match(/\d+/);
      return match ? parseInt(match[0]) : 0;
    },
    // 获取入驻金融机构数据
    async getrzjrjg() {
      const res = await productUpdate({
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: this.selectedRegion || "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: "入驻金融机构数量",
      });
      // 防御性处理空数据 -------------------------------------------------
      const firstDataItem = res.data.data[0] || {};

      // 处理 bizContent
      const rawBizContent = firstDataItem.bizContent || "[]"; // 双重保险
      try {
        this.listData = JSON.parse(rawBizContent);
      } catch (e) {
        console.error("解析 bizContent 失败:", e);
        this.listData = [];
      }
      this.listData = this.listData.map((item) => {
        item.financeNum = this.extractNumber(item.financeSum);
        return item;
      });
      const rawIndexValue = firstDataItem.indexValue || "0"; // 双重保险
      // this.totol = JSON.parse(res.data.data[0].indexValue);
      try {
        this.totol = JSON.parse(rawIndexValue);
      } catch (e) {
        console.error("解析 indexValue 失败:", e);
        this.totol = 0;
      }
      this.$nextTick(() => {
        this.init();
      });
    },
    // 初始化图表
    init() {
      const dom = document.getElementById("chart");
      if (!dom || dom.offsetWidth === 0 || dom.offsetHeight === 0) {
        // 容器还没渲染或没宽高，延迟重试
        setTimeout(() => this.init(), 100);
        return;
      }
      this.myChart = echarts.init(dom);
      window.addEventListener("resize", () => {
        if (this.myChart) {
          this.myChart.resize();
        }
      });
      this.updateChart();
    },
    // 更新图表配置和数据
    updateChart() {
      // 准备图表数据
      const chartData = this.listData.map((item) => {
        return {
          value: item.financeNum,
          name: item.financeType,
        };
      });
      // 生成随机渐变色函数
      const getThemeGradientColor = (index) => {
        // 预定义的颜色数组
        const endColors = [
          "#00FDDA", // 青色
          "#FFD01D", // 黄色
          "#398FFF", // 蓝色
          "#00CDCD", // 浅青色
          "#4169E1", // 皇家蓝
          "#FFB90F", // 深黄色
          "#1E90FF", // 道奇蓝
          "#00CED1", // 深青色
          "#87CEFA", // 浅天蓝色
          "#F0E68C", // 卡其色
          "#48D1CC", // 中绿宝石
          "#6495ED", // 矢车菊蓝
          "#FFEC8B", // 浅黄色
          "#00BFFF", // 深天蓝色
        ];

        // 返回渐变色配置
        return {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: endColors[index % endColors.length] },
            { offset: 1, color: endColors[index % endColors.length] },
          ],
        };
      };
      // 为每个数据项生成主题渐变色
      const themeGradientColors = chartData.map((_, index) =>
        getThemeGradientColor(index)
      );
      // 提取每个数据项的纯色（用于图例）
      const legendColors = themeGradientColors.map(
        (gradient) => gradient.colorStops[1].color
      );
      // 创建富文本样式对象
      const rich = {};
      chartData.forEach((item, index) => {
        rich[`a${index}`] = {
          color: legendColors[index],
          fontFamily: "PingFangSC, PingFang SC",
        };
      });
      // 定义图表的配置项和数据
      const option = {
        legend: {
          show: false,
        },
        series: [
          {
            name: "金融机构类型",
            type: "pie",
            radius: ["85%", "100%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            color: themeGradientColors,
            data: chartData,
          },
        ],
      };

      // 使用配置项和数据绘制图表
      this.myChart.setOption(option, true);
    },
    // 获取图例颜色
    getLegendColor(index) {
      const endColors = [
        "#00FDDA",
        "#FFD01D",
        "#398FFF",
        "#00CDCD",
        "#4169E1",
        "#FFB90F",
        "#1E90FF",
        "#00CED1",
        "#87CEFA",
        "#F0E68C",
        "#48D1CC",
        "#6495ED",
        "#FFEC8B",
        "#00BFFF",
      ];
      return endColors[index % endColors.length];
    },
    // 切换详情显示
    toggleDetail(index) {
      this.activeIndex = this.activeIndex === index ? null : index;
    },
    // 显示详细数据弹窗
    async showDetailDialog(item) {
      let param = "";
      if (item.financeType === "地方金融组织") {
        param = "地方金融组织数量";
      } else {
        param = item.financeType + "数量";
      }
      const res = await productUpdate({
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: param,
      });

      this.detailDialogTitle = item.financeType;
      this.detailDialogList = JSON.parse(res.data.data[0].bizContent);
      this.detailDialogList.forEach((item) => {
        item.financeSum = this.extractNumber(item.financeSum);
      });
      this.detailDialogVisible = true;
    },
    // 显示弹窗
    showDialog(e) {
      this.clickPosition = { x: e.clientX, y: e.clientY };
      this.visible = true;
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 主容器样式 */
.fkyhlbClass {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 标题样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
  }

  /* 内容区域样式 */
  .bottomClass2 {
    box-sizing: border-box;
    width: 100%;
    padding: 20px 0 0 20px;
    flex: 1;
    display: flex;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    backdrop-filter: blur(7px);
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;

    /* 图表容器样式 */
    .circleClass {
      width: 100%;
      height: 354px;
      display: flex;
      align-items: center;
      justify-content: left;
      margin-right: 70px;

      /* 饼图容器 */
      #chart {
        width: 310px;
        height: 310px;
      }

      /* 总数展示区域样式 */
      .rzjgClass {
        width: 200px;
        height: 200px;
        margin-left: 97px;
        border-radius: 110px;
        background: #052a53;
        box-shadow: inset 0px 0px 36px 0px #009eff;
        border: 2px solid rgba(255, 255, 255, 0.3);
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        /* 数字样式 */
        .numClass {
          font-family: OPPOSans;
          font-weight: normal;
          font-size: 57px;
          color: #ffffff;
          letter-spacing: 2px;
          text-shadow: 0px 0px 21px rgba(27, 144, 255, 0),
            0px 3px 4px rgba(0, 0, 0, 0.5);
          text-align: right;
          font-style: normal;
        }

        /* 标题样式 */
        .rzjgName {
          font-family: PingFangSC;
          font-weight: 400;
          font-size: 28px;
          color: #ffffff;
          margin-top: 20px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}

/* 自定义图例样式 */
.custom-legend {
  display: flex;
  flex-direction: column;
  align-items: start;
  margin-left: 0;
  margin-top: 20px;
}

/* 图例项样式 */
.legend-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
  font-size: 22px;
  color: #fff;
  min-width: 220px;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.2s;

  &.active {
    background: rgba(59, 188, 255, 0.12);
  }
}

/* 图例颜色块样式 */
.legend-color {
  width: 25px;
  height: 25px;
  border-radius: 3px;
  margin-right: 10px;
  display: inline-block;
}

/* 图例名称样式 */
.legend-name {
  font-family: PingFangSC;
  font-size: 32px;
  margin-right: 20px;
}

/* 图例数值样式 */
.legend-value {
  font-family: OPPOSans, OPPOSans;
  font-size: 32px;
  letter-spacing: 1px;

  span {
    font-size: 32px;
    font-weight: 300;
    font-family: PingFangSC;
    background: #fff;
    background-clip: text;
    -webkit-background-clip: text;
    color: #fff;
  }
}

/* 下钻详情样式 */
.legend-detail {
  color: #333;
  border-radius: 6px;
  margin: 10px 0 0 0;
  padding: 12px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  min-width: 220px;
}

/* 下钻详情项样式 */
.legend-detail-item {
  font-size: 32px;
  margin-bottom: 6px;
  display: flex;
  justify-content: space-between;
}

/* 详情名称样式 */
.detail-name {
  color: #333;
}

/* 详情数值样式 */
.detail-value {
  color: #409eff;
  font-weight: bold;

  span {
    background: linear-gradient(180deg, #ffffff 0%, #f5873d 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

/* 弹窗样式 */
.rzjrjg-dialog .el-dialog {
  width: 940px;
  position: absolute !important;
  left: 1040px !important;
  top: 14%;
  margin: 0 !important;
  transform: none !important;
  background: url("~@/assets/dp/dialogMini.png");
  background-size: 100% 100%;
}

/* 弹窗内容样式 */
.drill-dialog-content {
  padding: 10px 20px;
}

/* 弹窗行样式 */
.drill-dialog-row {
  border-radius: 3px;
  padding: 8px 10px;
  font-size: 38px;
  color: #fff;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid;
  border-image: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 0)
    )
    1 1;

  span {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 40px;
    color: #20d2f3;
    letter-spacing: 2px;
    text-align: left;
    font-style: normal;
    padding-right: 20px;
  }
}

/* 弹窗标题样式 */
.rzjrjg-dialog .el-dialog__title {
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: cover;
  background-repeat: no-repeat;
  width: 747px;
  height: 89px;
  text-align: left;
  letter-spacing: 3px;
  text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
  font-family: YouSheBiaoTiHei;
  color: #fff !important;
  font-size: 42px;
  display: flex;
  align-items: center;
  justify-content: left;
  padding-left: 125px;
}
</style>
