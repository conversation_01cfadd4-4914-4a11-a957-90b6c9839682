<template>
  <div class="login-bg">
    <div class="login-bg-img">
      <div class="login-box">
        <div class="login-form">
          <div class="input-group">
            <span class="icon user"></span>
            <input v-model="username" placeholder="请输入账号" />
          </div>
          <div class="input-group">
            <span class="icon lock"></span>
            <input
              v-model="password"
              type="password"
              placeholder="请输入密码"
            />
          </div>
          <button class="login-btn" @click="handleLogin">登录</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { SelectuserInfoRequest } from "@/api/article";
import { Message } from "element-ui";
import crypto from "crypto-js";
import md5 from 'js-md5'
export default {
  name: "Login",
  data() {
    return {
      username: "",
      password: "",
    };
  },
  methods: {
    // 加密密码
/*     encryptPassword(password) {
      const key = "243e43b67895699290d162b37c82b5de";
      return crypto.AES.encrypt(password, key).toString();
    }, */
    handleLogin() {
      //加密密码
let password = this.$jse.encrypt(md5(this.password))
      // 登录请求
      SelectuserInfoRequest({
        account: this.username,
        password: password,
        grantType: "captcha",
        code: "rwhx2",
        key: "243e43b67895699290d162b37c82b5de",
        tenantId: "000000"
      }).then(res => {
        if (res.data && res.data.code === 200) {
          // 存储用户名
          Message.success('登录成功')
          sessionStorage.setItem('username', this.username);
          // 存储用户信息
          const userInfo = {
            username: this.username,
            loginTime: new Date().getTime()
          };
          // 使用Base64简单加密用户信息
          const encryptedUserInfo = btoa(JSON.stringify(userInfo));
          sessionStorage.setItem('userInfo', encryptedUserInfo);
          // 跳转到首页
          this.$router.push('/pageOne');
        } else {
       /*    Message.error('账号或密码不正确') */
        }
      }).catch(error => {
/*         Message.error('账号或密码不正确') */
      }); 
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style scoped lang="scss">
.login-bg {
  width: 100vw;
  height: 100vh;
  background: url("~@/assets/dp/indexBg.png") no-repeat center center;
  background-size: cover;
  position: relative;
  overflow: hidden;
  .login-bg-img {
    width: 100vw;
    height: 100vh;
    background: url("~@/assets/dp/bg.png") no-repeat center center;
    background-size: 100% 100%;
  }
  .login-title {
    position: absolute;
    top: 18px;
    left: 50%;
    transform: translateX(-50%);
    color: #b6e0ff;
    font-size: 28px;
    letter-spacing: 4px;
    font-weight: bold;
    text-shadow: 0 0 8px #0ff, 0 2px 4px #000c1a;
    z-index: 2;
  }
  .login-box {
    position: absolute;
    right: 12vw;
    top: 20vh;
    width: 1090.99px;
    height: 948.19px;
    background-image: url("~@/assets/dp/loginBg.png");
    background-size: 100% 100%;
    border-radius: 10px;
    box-shadow: 0 0 30px #0a1a2a;
    padding: 32px 32px 24px 32px;
    z-index: 2;
    .login-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 26px;
      color: #b6e0ff;
      font-weight: bold;
      margin-bottom: 30px;
      .login-close {
        font-size: 32px;
        color: #b6e0ff;
        cursor: pointer;
        transition: color 0.2s;
        &:hover {
          color: #ff6b81;
        }
      }
    }
    .login-form {
      margin-top: 326px;
      width: 90%;
      margin-left: 5%;
      .input-group {

        background: linear-gradient(
          180deg,
          rgba(0, 48, 100, 0.85) 0%,
          rgba(0, 46, 85, 0.87) 64%,
          #00203c 100%
        );
        box-shadow: inset 0px 0px 22px 9px rgba(43, 135, 255, 0.56);
        border: 2px solid #1b90ff;
        display: flex;
        align-items: center;
        margin-bottom: 54px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 6px;
        padding: 0 12px;
        .icon {
          width: 100.78px;
          height: 44px;
          margin-right: 10px;
          &.user {
            background: url("~@/assets/dp/account.png") no-repeat center/contain;
          }
          &.lock {
            background: url("~@/assets/dp/password.png") no-repeat
              center/contain;
          }
        }
        input {
          flex: 1;
          width: 100%;
          height: 107.37px;
          background: transparent;
          border: none;
          color: #fff;
          font-size: 38px;
          outline: none;
        }
      }
      .login-btn {
        width: 100%;
        height: 107px;
        background: linear-gradient(
          180deg,
          #005dc4 0%,
          #1b78dc 64%,
          #066cf6 100%
        );
        border: 1px solid #1790ff;
        border: none;
        border-radius: 6px;
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        margin-top: 84px;
        transition: background 0.2s;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 38px;
        &:hover {
          background: linear-gradient(90deg, #1de6d7 0%, #3a8bff 100%);
        }
      }
    }
  }
}
</style>

<style>
/* 覆盖 Element UI Message 组件样式 */
.el-message {
  font-size: 25px !important;
  padding: 15px 20px !important;
}

.el-message__content {
  font-size: 25px !important;
  line-height: 1.5 !important;
}

.el-message--success .el-message__content,
.el-message--error .el-message__content {
  font-size: 25px !important;
}
</style>
