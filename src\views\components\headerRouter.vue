<!--
  顶部导航组件
  功能：
  1. 显示天气信息（温度、天气类型）
  2. 显示当前时间和日期
  3. 提供导航菜单（全局概览、汇聚金融机构、服务实体经济、创新数智应用）
  4. 支持路由导航和高亮显示
-->
<template>
  <div class="headerRouterClass">
    <!-- 左侧区域：天气和导航菜单 -->
    <div class="headerLeft">
      <!-- 天气信息展示 -->
      <div class="leftOne">
        <div class="oneTop">
          {{ getWeather.minTemperature }}-{{ getWeather.maxTemperature }}°C
        </div>
        <div class="oneBottom">
          <div class="weatherImg"></div>
          <div class="weatherName">{{ getWeather.type }}</div>
        </div>
      </div>
      <!-- 全局概览导航 -->
      <div class="wz">
        <div
          :class="flag == 1 ? 'leftTwo' : 'leftTwo2'"
          @click="clickBigPicture"
        >
          全局概览
        </div>
      </div>
      <!-- 汇聚金融机构导航 -->
      <div>
        <div :class="flag == 2 ? 'leftTwo' : 'leftTwo2'" @click="FinanceClick">
          汇聚金融资源
        </div>
      </div>
    </div>
    <!-- 右侧区域：导航菜单和时间 -->
    <div class="headerRight">
      <!-- 服务实体经济导航 -->
      <div class="wz">
        <div :class="flag == 3 ? 'leftTwo' : 'leftTwo2'" @click="serveClick1">
          服务实体经济
        </div>
      </div>
      <!-- 创新数智应用导航 -->
      <div class="wz2">
        <div :class="flag == 4 ? 'leftTwo' : 'leftTwo2'" @click="serveClick">
          创新数智应用
        </div>
      </div>
      <!-- 时间日期显示 -->
      <div class="leftOne">
        <div class="oneTop">{{ currentTime }}</div>
        <div class="oneBottom">
          <div class="weatherName">{{ currentDate }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { queryWeatherCityCode } from "@/api/article.js";

export default {
  name: "headerRouter",
  // 组件数据
  data() {
    return {
      flag: 1, // 当前选中的导航项
      currentTime: "", // 当前时间
      currentDate: "", // 当前日期和星期
      timer: null, // 时间更新定时器
      getWeather: {}, // 天气信息
    };
  },
  // 生命周期钩子
  mounted() {
    // 获取天气信息
    queryWeatherCityCode("成都市").then((res) => {
      this.getWeather = res.data.data;
    });
    // 初始化时间显示并启动定时器
    this.updateDateTime();
    this.timer = setInterval(this.updateDateTime, 1000);

    // 根据当前路由设置高亮状态
    this.checkCurrentRoute();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    // 检查当前路由并设置高亮
    checkCurrentRoute() {
      const currentPath = this.$route.path;
      if (currentPath === "/pageThree") {
        this.flag = 3; // 服务实体经济高亮
      } else if (currentPath === "/pageOne") {
        this.flag = 1; // 全局概览高亮
      } else if (currentPath === "/pageTwo") {
        this.flag = 2; // 汇聚金融机构高亮
      } else if (currentPath === "/pageFour") {
        this.flag = 4; // 创新数智应用高亮
      }
    },

    // 更新时间和日期显示
    updateDateTime() {
      const now = new Date();
      // 格式化时间
      const h = String(now.getHours()).padStart(2, "0");
      const m = String(now.getMinutes()).padStart(2, "0");
      const s = String(now.getSeconds()).padStart(2, "0");
      this.currentTime = `${h}:${m}:${s}`;

      // 格式化日期和星期
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const date = now.getDate();
      const weekArr = [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
      ];
      const week = weekArr[now.getDay()];
      this.currentDate = `${year}年${month}月${date}日 ${week}`;
    },

    // 导航到创新数智应用页面
    serveClick() {
      if (this.flag !== 4) {
        this.flag = 4;
        this.$router.push("/pageFour");
      }
    },

    // 导航到服务实体经济页面
    serveClick1() {
      if (this.flag !== 3) {
        this.flag = 3;
        this.$router.push("/pageThree");
      }
    },

    // 导航到汇聚金融机构页面
    FinanceClick() {
      if (this.flag !== 2) {
        this.flag = 2;
        this.$router.push("/pageTwo");
      }
    },

    // 导航到全局概览页面
    clickBigPicture() {
      if (this.flag !== 1) {
        this.flag = 1;
        this.$router.push("/pageOne");
      }
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 顶部导航容器 */
.headerRouterClass {
  width: 100vw;
  height: 180px;
  box-sizing: border-box;
  padding: 84px 76px 0 109px;
  display: flex;
  justify-content: space-between;

  /* 左侧区域样式 */
  .headerLeft {
    display: flex;
    .wz {
      margin-right: 85px;
    }
    /* 天气信息样式 */
    .leftOne {
      margin-right: 130px;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      padding: 15px 0 8px 0;
      .oneTop {
        font-family: PangMenZhengDao;
        font-size: 36px;
        color: #cfe6ff;
        line-height: 41px;
        letter-spacing: 4px;
        text-align: left;
        font-style: normal;
      }
      .oneBottom {
        display: flex;
        align-items: center;
        .weatherImg {
          margin-right: 9px;
          width: 30px;
          height: 25px;
          background-image: url("~@/assets/weather/dy.png");
          background-repeat: no-repeat;
          background-size: 30px 25px;
        }
        .weatherName {
          font-family: PingFangSC;
          font-weight: 400;
          font-size: 30px;
          color: #cfe6ff;
          font-style: normal;
        }
      }
    }
    /* 导航按钮选中状态样式 */
    .leftTwo {
      display: flex;
      cursor: pointer;
      align-items: center;
      justify-content: center;
      width: 370px;
      height: 96px;
      background-image: url("~@/assets/dp/xz.png");
      background-repeat: no-repeat;
      background-size: 370px 96px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 34px;
      color: #ffffff;
      line-height: 48px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 16px rgba(66, 173, 255, 0.76),
        0px 2px 4px rgba(0, 12, 26, 0.77);
      text-align: right;
      font-style: normal;
    }
    /* 导航按钮未选中状态样式 */
    .leftTwo2 {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 370px;
      height: 96px;
      background-image: url("~@/assets/dp/wxz.png");
      background-repeat: no-repeat;
      background-size: 370px 96px;
      font-weight: 400;
      font-size: 34px;
      color: #a0cdff;
      line-height: 48px;
      letter-spacing: 1px;
      text-shadow: 0px 2px 4px #000c1a;
      text-align: right;
      font-style: normal;
    }
  }

  /* 右侧区域样式 */
  .headerRight {
    display: flex;
    /* 时间日期显示样式 */
    .leftOne {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      padding: 15px 0 8px 0;
      .oneTop {
        width: 100%;
        font-family: PangMenZhengDao;
        font-size: 36px;
        color: #cfe6ff;
        line-height: 41px;
        letter-spacing: 4px;
        text-align: right;
        font-style: normal;
      }
      .oneBottom {
        display: flex;
        align-items: center;
        .weatherImg {
          margin-right: 9px;
          width: 25px;
          height: 18px;
          background-image: url("~@/assets/weather/dy.png");
          background-repeat: no-repeat;
          background-size: 25px 18px;
        }
        .weatherName {
          font-family: PingFangSC;
          font-weight: 400;
          font-size: 19px;
          color: #cfe6ff;
          font-style: normal;
        }
      }
    }
    .wz {
      margin-right: 85px;
    }
    .wz2 {
      margin-right: 115px;
    }
    /* 导航按钮选中状态样式 */
    .leftTwo {
      display: flex;
      cursor: pointer;
      align-items: center;
      justify-content: center;
      width: 370px;
      height: 96px;
      background-image: url("~@/assets/dp/xz.png");
      background-repeat: no-repeat;
      background-size: 370px 96px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 34px;
      color: #ffffff;
      line-height: 48px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 16px rgba(66, 173, 255, 0.76),
        0px 2px 4px rgba(0, 12, 26, 0.77);
      text-align: right;
      font-style: normal;
    }
    /* 导航按钮未选中状态样式 */
    .leftTwo2 {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 370px;
      height: 96px;
      background-image: url("~@/assets/dp/wxz.png");
      background-repeat: no-repeat;
      background-size: 370px 96px;
      font-weight: 400;
      font-size: 34px;
      color: #a0cdff;
      line-height: 48px;
      letter-spacing: 1px;
      text-shadow: 0px 2px 4px #000c1a;
      text-align: right;
      font-style: normal;
    }
  }
}
</style>
