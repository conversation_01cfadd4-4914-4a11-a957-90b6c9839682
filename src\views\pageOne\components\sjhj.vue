<!--
  数据汇集组件
  功能：
  1. 展示累计汇集数据的统计信息
  2. 显示数据类别、数据项和数据量的统计
  3. 提供数据明细的滚动列表展示
  4. 支持查看更多详细数据
-->
<template>
  <div class="sjhjClassOne">
    <!-- 头部区域：标题和查看更多按钮 -->
    <div class="headerImg">
      <div class="lbClass">数据汇集</div>
      <div class="rightClass">
        <div class="djgdClass" @click="openMoreDialog">点击查看更多</div>
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>
    <!-- 底部内容区域 -->
    <div class="bottomClass">
      <!-- 累计数据统计区域 -->
      <div class="ljsjClass">
        <div class="oneClass">累计汇集数据：</div>
        <div class="fourClass">{{ parseInt(classNum) }}</div>
        <div class="fiveClass">大类</div>
        <div class="twoClass">{{ parseInt(itemNum) }}</div>
        <div class="threeClass">项</div>
        <div class="twoClass">{{ dataNum }}</div>
        <div class="threeClass">亿条</div>
      </div>
      <!-- 数据明细列表区域 -->
      <div class="box-con-list">
        <!-- 表头 -->
        <div class="tableHeader">
          <div class="table-name">
            <div>数据类型</div>
            <div>数据资源名称</div>
            <div>数据量(条)</div>
          </div>
        </div>
        <!-- 滚动列表容器 -->
        <div class="warp">
          <vue-seamless-scroll
            ref="vueSeamlessScroll1"
            :data="tableList"
            style="height: 100%; overflow: hidden"
            :class-option="classOption"
            @mousewheel.native="handleScroll"
          >
            <div v-for="(item, index) in tableList" :key="index">
              <div class="table-items">
                <div style="width: 30%">{{ item.category_cn }}</div>
                <div style="width: 35%">{{ item.category_item_cn }}</div>
                <div style="width: 17%">{{ item.data_num }}</div>
              </div>
            </div>
          </vue-seamless-scroll>
        </div>
        <!-- 详情弹窗组件 -->
        <dialogMore
          ref="dialogMoreRef"
          :tableData="tableList"
          :maxDt="maxDt"
          :isMaxDtReady="isMaxDtReady"
          :selectedRegion="selectedRegion"
        />
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { productUpdateList } from "@/api/article.js";
import dialogMore from "./dialogMore.vue";

export default {
  name: "sjhj",
  components: {
    vueSeamlessScroll,
    dialogMore,
  },
  props: {
    // 最大日期
    maxDt: {
      type: String,
      required: true,
    },
    // 最大日期是否准备就绪
    isMaxDtReady: {
      type: Boolean,
      required: true,
    },
    //区域
    selectedRegion: {
      type: String,
      default: "ALL",
    },
  },
  data() {
    return {
      // 滚动配置选项
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 50, // 单步运动停止的高度
        singleWidth: 0, // 单步运动停止的宽度
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      },
      dataNum: "", // 数据量（亿条）
      classNum: "", // 数据大类数量
      itemNum: "", // 数据项数量
      tableList: [], // 数据明细列表
      carList: [], // 预留的列表数据
    };
  },
  watch: {
    // 监听最大日期准备状态
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          // 只有当 maxDt 准备好后才调用其他接口
          this.getsjhj();
        }
      },
      immediate: true,
    },
    // 监听区域变化
    selectedRegion: {
      immediate: true,
      handler(newVal) {
        this.getsjhj(newVal);
      },
    },
  },
  methods: {
    // 打开更多详情弹窗
    openMoreDialog() {
      this.$refs.dialogMoreRef.openDialog();
    },
    // 获取数据汇集信息
    async getsjhj() {
      try {
        let obj = [
          {
            indexName: "累计汇集数据量",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimName: 1,
            dimIndustryChain: "ALL",
          },
          {
            indexName: "累计汇聚数据类别",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimName: 1,
            dimIndustryChain: "ALL",
          },
          {
            indexName: "数据明细",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimName: 1,
            dimIndustryChain: "ALL",
          },
          {
            indexName: "累计汇集数据项",
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            // dimCounty: "ALL",
            dimCounty: this.selectedRegion || "ALL",
            dimPark: "ALL",
            dimName: 1,
            dimIndustryChain: "ALL",
          },
        ];
        const res = await productUpdateList(obj);
        if (res.data.code == 200) {
          // 添加数据有效性检查
          if (!Array.isArray(res.data.data) || res.data.data.length < 4) {
            console.error("接口返回数据格式不正确", res.data);
            // 设置默认值防止页面崩溃
            this.dataNum = 0;
            this.classNum = 0;
            this.itemNum = 0;
            this.tableList = [];
            return;
          }

          // 安全访问对象属性
          this.dataNum = res.data.data[0]?.indexValue || 0;
          this.classNum = res.data.data[1]?.indexValue || 0;
          this.itemNum = res.data.data[3]?.indexValue || 0;

          // 安全处理JSON解析
          const bizContent = res.data.data[2]?.bizContent;
          try {
            this.tableList = bizContent ? JSON.parse(bizContent) : [];
          } catch (e) {
            console.error("JSON解析失败", e);
            this.tableList = [];
          }
        } else {
          console.error("接口返回非200状态码", res.data);
        }
      } catch (error) {
        console.error("请求失败", error);
        // 可选：添加用户提示
        // this.$message.error('数据加载失败，请稍后重试');
      }
    },
    // 显示内容模态框（预留方法）
    showConModal(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /picShow/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      if (data) {
        //点击事件处理逻辑
      }
    },
    // 处理滚动事件
    handleScroll(e) {
      this.$refs.vueSeamlessScroll1.yPos =
        this.$refs.vueSeamlessScroll1.yPos - e.deltaY + 60;
      // 如果是正数 说明是往上滚
      if (this.$refs.vueSeamlessScroll1.yPos > 0) {
        this.$refs.vueSeamlessScroll1.yPos = 0;
        return;
      }
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 主容器样式 */
.sjhjClassOne {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部区域样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    /* 标题样式 */
    .lbClass {
      height: 80px;
      line-height: 80px;
    }

    /* 右侧按钮区域样式 */
    .rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      /* 查看更多按钮样式 */
      .djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      /* 箭头图标样式 */
      .imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 底部内容区域样式 */
  .bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);

    /* 累计数据统计区域样式 */
    .ljsjClass {
      display: flex;
      align-items: flex-end;
      height: 60px;
      padding-left: 40px;
      padding-top: 6px;
      margin-bottom: 26px;

      /* 标题文字样式 */
      .oneClass {
        font-family: PingFangSC;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        line-height: 40px;
        letter-spacing: 1px;
      }

      /* 数字样式 */
      .twoClass {
        font-family: OPPOSans, OPPOSans;
        font-weight: normal;
        font-size: 46px;
        letter-spacing: 2px;
        font-weight: 600;
        background: linear-gradient(90deg, #ffffff 0%, #ffce7c 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /* 单位文字样式 */
      .threeClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28px;
        margin-right: 12px;
        color: #ffffff;
        line-height: 40px;
        letter-spacing: 1px;
        margin-left: 6px;
      }

      /* 大类数字样式 */
      .fourClass {
        font-family: OPPOSans, OPPOSans;
        font-weight: normal;
        font-size: 44px;
        letter-spacing: 2px;
        font-weight: 600;
        text-align: center;
        font-style: normal;
        -webkit-text-fill-color: transparent;
        background: linear-gradient(180deg, #ffffff 0%, #7cebff 100%);
        background-clip: text;
        margin-left: 20px;
      }

      /* 大类单位样式 */
      .fiveClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28px;
        margin-right: 12px;
        color: #ffffff;
        letter-spacing: 1px;
      }
    }

    /* 数据明细列表区域样式 */
    .box-con-list {
      width: 100%;

      /* 表头样式 */
      .tableHeader {
        width: 100%;
        box-sizing: border-box;
        padding: 0 35px;

        .table-name {
          padding: 0 64px;
          box-sizing: border-box;
          display: flex;
          height: 80px;
          background: rgba(255, 255, 255, 0.09);
          font-weight: 500;
          font-size: 26px;
          color: #41ccff;
          letter-spacing: 1px;
          align-items: center;
          justify-content: space-between;
        }
      }
    }

    /* 滚动列表容器样式 */
    .warp {
      height: 14vh;
      padding: 0 35px;

      /* 列表项样式 */
      .table-items {
        display: flex;
        padding: 0 64px;
        box-sizing: border-box;
        height: 80px;
        align-items: center;
        text-align: left;
        justify-content: space-between;
        font-weight: 400;
        font-size: 26px;
        color: #ffffff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.22);
      }
    }
  }
}
</style>
