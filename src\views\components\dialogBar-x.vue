<!--
  通用柱状图弹窗组件
  功能：
  1. 展示数据的柱状图
  2. 支持自动轮播展示
  3. 支持自定义图表尺寸
  4. 支持自适应字体大小
  5. 支持自定义渐变色
  6. 支持数据循环展示
-->
<template>
  <!-- 弹窗遮罩层 -->
  <div v-if="visible" class="dialogBar-model" @click="handleMaskClick">
    <!-- 弹窗内容区 -->
    <div class="dialogBar-content" @click.stop>
      <!-- 弹窗头部 -->
      <div class="dialogBar-header-box">
        <div class="dialogBar-header">
          <div class="dialogBar-titleBox">
            <span class="dialogBar-title">{{ title }}</span>
          </div>
        </div>
      </div>
      <!-- 弹窗主体 -->
      <div class="dialogBar-body">
        <!-- 图表容器，支持自定义尺寸 -->
        <div class="dialogBar-chart" ref="chartRef"     
          :style="{
            width: this.$autoFontSize(chartWidth),
            height: this.$autoFontSize(chartHeight),
            background: 'rgba(0, 24, 48, 0.1)'
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "DialogBar",
  // 组件数据
  data() {
    return {
      chart: null, // echarts实例
      autoPlayTimer: null, // 自动播放定时器
      currentIndex: 0, // 当前显示的数据索引
      displayCount: 15 // 显示数据条数
    };
  },
  // 组件属性定义
  props: {
    visible: { 
      type: Boolean, 
      default: false // 控制弹窗显示
    },
    title: {
      type: String,
      default: '模型应用' // 弹窗标题
    },
    chartData: {
      type: Array,
      default: () => [] // 图表数据
    },
    chartHeight: {
      type: [String, Number],
      default: 868 // 图表高度
    },
    chartWidth: {
      type: [String, Number],
      default: 1271 // 图表宽度
    },
    colorStops:{
      type:Array,
      default:()=>[ // 柱状图渐变色配置
        { offset: 0, color: "#57BDFF" },
        { offset: 1, color: "#0C438C" },
      ]
    },
    backgroundData: {
      type: Array,
      default: () => [4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4] // 背景数据
    }
  },
  // 监听器
  watch: {
    // 监听弹窗显示状态
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.initChart();
          }, 100);
        });
      }
    },
    // 监听数据变化
    chartData: {
      handler(val) {
        if (this.chart && val.length) {
          this.updateChart();
        }
      },
      deep: true
    }
  },
  methods: {
    // 关闭弹窗
    close() {
      // 清除定时器
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer);
        this.autoPlayTimer = null;
      }
      
      // 清除图表实例
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
      
      // 重置索引
      this.currentIndex = 0;
      
      // 触发关闭事件
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    // 处理遮罩层点击
    handleMaskClick() {
      this.close();
    },
    // 更新图表数据
    updateChart() {
      if (!this.chart) return;
      // 对数据进行排序
      const data = [...this.chartData].sort((a, b) => b.value - a.value);
      
      // 初始只显示指定条数的数据
      const initialData = data.slice(0, this.displayCount);
      
      // 配置图表选项
      this.chart.setOption({
        // 图表网格配置
        grid: {
          left: this.$autoFontSize(20),
          right: this.$autoFontSize(0),
          bottom: this.$autoFontSize(6),
          top: this.$autoFontSize(20),
          containLabel: true
        },
        // 提示框配置
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>
                    <span style="color:#ffce7c">${data.value}亿元</span><br/>
                    <span style="color:#23eaff">${data.data.count}笔</span>`;
          }
        },
        // X轴配置
        xAxis: {
          type: 'category',
          data: initialData.map(item => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(14),
            width: 300,
            overflow: 'break',
            formatter: function(value) {
              return value.replace(/(.{6})/g, '$1\n');
            },
            margin: 30
          },        
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        // Y轴配置
        yAxis: {
          show: false,
          type: 'value',
          name: '金额（亿）',
          nameTextStyle: {
            color: '#fff',
            fontSize: this.$autoFontSize(12)
          },
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(12)
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        // 数据系列配置
        series: [
          // 背景柱状图
          {
            name: "全量背景图",
            type: "bar",
            barGap: "-100%",
            data: this.backgroundData,
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              normal: {
                color: "rgba(63, 169, 245, 0.2)",
              },
            },
            z: 0,
          },
          // 主数据柱状图
          {
            data: initialData.map(item => ({
              value: parseFloat(item.amount),
              count: item.count
            })),
            type: 'bar',
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: this.colorStops
              },
              borderRadius: [0, 0, 0, 0]
            },
            // 标签配置
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return `{amount|${params.value}亿元}\n{count|${params.data.count}笔}`;
              },
              rich: {
                amount: {
                  color: '#ffce7c',
                  fontSize: this.$autoFontSize(14),
                  textAlign: 'center',
                  align: 'center',
                  verticalAlign: 'middle'
                },
                count: {
                  color: '#23eaff',
                  fontSize: this.$autoFontSize(14),
                  textAlign: 'center',
                  align: 'center',
                  verticalAlign: 'middle'
                }
              }
            }
          },
          // 散点图（用于装饰）
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "circle",
            symbolSize: [this.$autoFontSize(12), this.$autoFontSize(6)],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: initialData.map((item, idx) => [
              idx,
              parseFloat(item.amount),
            ]),
          }
        ]
      });

      // 启动自动播放
      this.startAutoPlay();
    },
    // 开始自动播放
    startAutoPlay() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer);
      }

      // 确保数据按金额从大到小排序
      const sortedData = [...this.chartData].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));
      const totalItems = sortedData.length;

      if (totalItems <= this.displayCount) {
        return; // 如果数据量小于等于显示数量，不需要滚动
      }

      this.autoPlayTimer = setInterval(() => {
        // 计算当前显示的数据范围
        let start = this.currentIndex;
        let end = start + this.displayCount;

        // 如果超出数据范围，需要循环显示
        if (end > totalItems) {
          // 计算需要从开头补充的数据量
          const remainingCount = end - totalItems;
          // 组合数据：当前数据 + 开头补充的数据
          const displayData = [
            ...sortedData.slice(start, totalItems),
            ...sortedData.slice(0, remainingCount)
          ];

          this.chart.setOption({
            xAxis: {
              data: displayData.map(item => item.name)
            },
            series: [
              {
                name: "全量背景图",
                data: this.backgroundData.slice(0, this.displayCount)
              },
              {
                data: displayData.map(item => ({
                  value: parseFloat(item.amount),
                  count: item.count
                }))
              },
              {
                data: displayData.map((item, idx) => [
                  idx,
                  parseFloat(item.amount)
                ])
              }
            ]
          });
        } else {
          // 正常显示范围内的数据
          this.chart.setOption({
            xAxis: {
              data: sortedData.slice(start, end).map(item => item.name)
            },
            series: [
              {
                name: "全量背景图",
                data: this.backgroundData.slice(0, this.displayCount)
              },
              {
                data: sortedData.slice(start, end).map(item => ({
                  value: parseFloat(item.amount),
                  count: item.count
                }))
              },
              {
                data: sortedData.slice(start, end).map((item, idx) => [
                  idx,
                  parseFloat(item.amount)
                ])
              }
            ]
          });
        }

        // 更新索引，当到达末尾时重新开始
        this.currentIndex = (this.currentIndex + 1) % totalItems;
      }, 3000); // 每3秒滚动一次
    },
    // 初始化图表
    initChart() {
      if (!this.$refs.chartRef) return;
      
      const container = this.$refs.chartRef;
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        console.warn('Chart container has no size, retrying...');
        setTimeout(() => this.initChart(), 100);
        return;
      }

      if (this.chart) {
        this.chart.dispose();
      }

      this.chart = echarts.init(container);
      
      // 确保数据按金额从大到小排序
      const sortedData = [...this.chartData].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));
      const initialData = sortedData.slice(0, this.displayCount);
      
      // 配置图表选项
      this.chart.setOption({
        // 图表网格配置
        grid: {
          left: this.$autoFontSize(0),
          right: this.$autoFontSize(0),
          bottom: this.$autoFontSize(6),
          top: this.$autoFontSize(20),
          containLabel: true
        },
        // 提示框配置
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>
                    <span style="color:#ffce7c">${data.value}亿元</span><br/>
                    <span style="color:#23eaff">${data.data.count}笔</span>`;
          }
        },
        // X轴配置
        xAxis: {
          type: 'category',
          data: initialData.map(item => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(13),
            interval: 0,
            width: 300,
            overflow: 'break',
            formatter: function(value) {
              return value.replace(/(.{6})/g, '$1\n');
            },
            margin: 20
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        // Y轴配置
        yAxis: {
          show: false,
          type: 'value',
          name: '金额（亿）',
          nameTextStyle: {
            color: '#fff',
            fontSize: this.$autoFontSize(12)
          },
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(12)
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        // 数据系列配置
        series: [
          // 背景柱状图
          {
            name: "全量背景图",
            type: "bar",
            barGap: "-100%",
            data: this.backgroundData.slice(0, this.displayCount),
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              normal: {
                color: "rgba(63, 169, 245, 0.2)",
              },
            },
            z: 0,
          },
          // 主数据柱状图
          {
            data: initialData.map(item => ({
              value: parseFloat(item.amount),
              count: item.count
            })),
            type: 'bar',
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: this.colorStops
              },
              borderRadius: [0, 0, 0, 0]
            },
            // 标签配置
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return `{amount|${params.value}亿元}\n{count|${params.data.count}笔}`;
              },
              rich: {
                amount: {
                  color: '#ffce7c',
                  fontSize: this.$autoFontSize(14),
                  textAlign: 'center',
                  align: 'center',
                  verticalAlign: 'middle'
                },
                count: {
                  color: '#23eaff',
                  fontSize: this.$autoFontSize(14),
                  textAlign: 'center',
                  align: 'center',
                  verticalAlign: 'middle'
                }
              }
            }
          },
          // 散点图（用于装饰）
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "circle",
            symbolSize: [this.$autoFontSize(12), this.$autoFontSize(6)],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: initialData.map((item, idx) => [
              idx,
              parseFloat(item.amount),
            ]),
          }
        ]
      });

      // 启动自动滚动
      this.startAutoPlay();
      window.addEventListener("resize", this.handleResize);
    },
    // 处理窗口大小变化
    handleResize() {
      this.chart && this.chart.resize();
    }
  },
  // 生命周期钩子
  mounted() {
    if (this.visible) {
      this.$nextTick(() => {
        setTimeout(() => {
          this.initChart();
        }, 100);
      });
    }
  },
  beforeDestroy() {
    // 清理资源
    if (this.autoPlayTimer) {
      clearInterval(this.autoPlayTimer);
    }
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.handleResize);
  },
};
</script>

<style scoped lang="scss">
/* 弹窗遮罩层样式 */
.dialogBar-model {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0,0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 标题样式 */
.dialogBar-titleBox {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  .dialogBar-title {
    padding-left: 50px;
    width: 314px;
    height: 89px;
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    margin-left: 50px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
  }
}

/* 弹窗内容区样式 */
.dialogBar-content {
  background: url("~@/assets/dp/dialog.png");
  background-size: 100% 100%;
  border-radius: 12px;
  width: 1930.79px;
  height: 1152.11px;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
}

/* 弹窗头部样式 */
.dialogBar-header-box {
  display: flex;
}

.dialogBar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 24px 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 弹窗主体样式 */
.dialogBar-body {
  padding: 37px 48px 0 48px;
}

/* 图表容器样式 */
.dialogBar-chart {
  width: 100%;
  min-height: 900px;
  background: rgba(0, 24, 48, 0.1);
}
</style>
