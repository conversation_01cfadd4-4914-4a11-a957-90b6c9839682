<template>
  <div class="bgblackClass3">
    <div class="dpClass">
      <HeaderRouter @hideCenterBcg="hideCenterBcg" />
      <div class="pageContent">
        <div class="pageLeft3">
          <div>
            <LoanStatus   :selected-region="selectedRegion" style="margin-top: 9%" />
          </div>
          <div>
            <LendingIndustry   :selected-region="selectedRegion"  style="margin-top: 5%" />
          </div>
          <div>
            <ChainDistribution  :selected-region="selectedRegion" style="margin-top: 5%" />
          </div>
        </div>
        <div class="pageCenter1">
          <map_industry  @region-click="handleRegionClick"  style="margin-top: 5%" />
        </div>
        <div class="pageRight3">
          <div>
            <IndustrialPark  :selected-region="selectedRegion" style="margin-top: 9%" />
          </div>
          <div>
            <Distribution  :selected-region="selectedRegion" style="margin-top: 5%" />
          </div>
          <div>
            <HighTechnology  :selected-region="selectedRegion" style="margin-top: 5%" />
          </div>
        </div>
      </div>
      <div class="buttomClass">
        <ChangeButtom  title="行业区域放款情况"/>
      </div>
    </div>
  </div>
</template>

<script>
import LoanStatus from "./components/LoanStatus.vue";
import HeaderRouter from "../components/headerRouter";
import LendingIndustry from "./components/LendingIndustry";
import HighTechnology from "./components/HighTechnology";
import IndustrialPark from "./components/IndustrialPark.vue";
import ChangeButtom from "../components/changeButtom";
import ChainDistribution from "./components/ChainDistribution.vue";
import Distribution from "./components/Distribution.vue";
import map_industry from "./components/map_industry.vue";
export default {
  name: "pageThree",
  components: {
    HeaderRouter,
    LendingIndustry,
    HighTechnology,
    IndustrialPark,
    ChangeButtom,
    LoanStatus,
    ChainDistribution,
    Distribution,
    map_industry
  },
  data() {
    return {
      showDialog: false,
      flagFont: true,
      flag: true,
      showBackground: true, // 控制背景图片显示
      institutionsHovered: false,
      enterpriseHovered: false,
      governmentHovered: false,
      modelHovered: false,
      baseHovered: false,
      opsHovered: false,
      // 控制弹框显示
      showEnterprisePopup: false,
      showGovernmentPopup: false,
      showInstitutionsPopup: false,
      showModelPopup: false,
      showBasePopup: false,
      showOpsPopup: false,
      labelData: [
        "投融资线上撮合",
        "数据增信能力",
        "财金互动",
        "财政兑现能力",
        "联合建模能力",
        "线上价值诉讼",
      ],
       selectedRegion: "ALL", // 默认值
    };
  },
  mounted() {
    //只有在路由在pageThree服务实体经济才显示pageCenter
    this.flag = this.$route.name === "pageThree";
    this.showBackground = this.$route.name === "pageThree";
  },
  methods: {
     handleRegionClick(regionName) {
      this.selectedRegion = regionName;
      console.log("值", this.selectedRegion);
    },
    goPage(data) {
      if (!this.$refs.mapOne.flag) {
        this.$refs.mapOne.flag = data;
      }
    },
    //改变颜色
    changeCity(data) {
      this.flagFont = data;
    },
    // 隐藏centerBcg背景图片
    hideCenterBcg() {
      this.flag = false; // 将flag设置为false，隐藏pageCenter组件
      this.showBackground = false; // 禁用背景图片
    },
  },
  // 使用CSS类来控制背景图片而不是计算属性
};
</script>
<style scope="scoped" lang="scss">
.bgblackClass3 {
  width: 100vw;
  height: 100vh;
  background-image: url("~@/assets/dp/bgblack.jpg");
  background-repeat: no-repeat;
  background-size: 100vw 100vh;
  position: relative;
  .dpClass {
    width: 100vw;
    height: 100vh;
    background-image: url("~@/assets/dp/bg.png");
    background-repeat: no-repeat;
    background-size: 100vw 100vh;
    display: flex;
    flex-direction: column;
    .pageContent {
      display: flex;
      margin: 0 100px;
      height: 100%;
      justify-content: space-between;
      .pageLeft3 {
        height: 97%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
      }
      .pageRight3 {
        height: 97%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
      }
    }
    .buttomClass {
      position: absolute;
      width: 50%;
      margin-left: 0;
      bottom: 0;
    }
  }
  .pageCenter1{
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 130% 100%;
    background-position: center top;
    z-index: 99;
    position: relative;

    /* 让内容整体上移并缩小 */
    justify-content: flex-start;
    .cube-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 46px;
      color: #d3f1ff;
      line-height: 65px;
      letter-spacing: 2px;
      text-align: right;
      font-style: normal;
      background: linear-gradient(180deg, #ffffff 0%, #b7deff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: -90px;
      position: absolute;
      top: 100px;
      left: 740px;
    }
    .cube-section {
      .cube-3d {
        position: absolute;
        top: 0px;
        left: 640px;
        height: 102px;
        width: 450px; // 缩小立方体
        height: 450px;
        margin: 80 auto;
        background: url("~@/assets/serve/cube.png") no-repeat center/contain;
      }
      .cube-labels {
        position: relative;
        width: 100%;
        height: 100%;

        .cube-label-item {
          padding: 20px 0;
          width: 71px;
          position: absolute;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #ffffff;
          letter-spacing: 1px;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-shadow: 0px 0px 10px rgba(0, 204, 255, 0.5),
            0px 1px 3px rgba(0, 0, 0, 0.32);
          white-space: nowrap;
          z-index: 10;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.33) 0%,
            rgba(255, 255, 255, 0.14) 28%,
            rgba(255, 255, 255, 0.14) 69%,
            rgba(255, 255, 255, 0.33) 100%
          );

          span {
            color: #ffffff;
            display: block;
            line-height: 30px;
            text-align: center;
          }
        }

        // 左上标签
        .cube-label-top-left {
          top: 100px;
          left: 190px;
        }

        // 左中标签
        .cube-label-middle-left {
          top: 110px;
          left: 400px;
        }

        // 左下标签
        .cube-label-bottom-left {
          top: 190px;
          left: 300px;
        }

        // 右上标签
        .cube-label-top-right {
          top: 100px;
          right: 300px;
        }

        // 右中标签
        .cube-label-middle-right {
          top: 270px;
          right: 400px;
        }

        // 右下标签
        .cube-label-bottom-right {
          top: 60px;
          right: 180px;
        }

        // 右下标签（第七个）
        .cube-label-bottom-right-2 {
          top: 20px;
          right: 400px;
        }
      }
      .cube-middle-label {
        position: absolute;
        top: 400px;
        left: 650px;
        width: 472px; // 缩小立方体
        height: 102px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 40px;
        color: #ffffff;
        line-height: 102px;
        letter-spacing: 2px;
        text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
          0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
        background: #00183b;
        text-align: center;
        font-style: normal;
      }
    }
    .circle-section {
      width: 370px;
      height: 370px;
      margin-bottom: 18px;
      .circle-row {
        display: flex;
        justify-content: space-between;
        .circle-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .circle-icon-enterprise {
            position: absolute;
            left: 220px;
            top: 590px;
            width: 200px; // 缩小圆形icon
            height: 200px;
            border-radius: 50%;
            margin-bottom: 6px;
            background: linear-gradient(180deg, #398fff 0%, #0ff 100%);
            background-image: url("~@/assets/serve/enterprise.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          .circle-icon-enterprise:hover {
            background-image: url("~@/assets/serve/enterpriseHover.png");
          }
          .circle-icon-government {
            position: absolute;
            left: 780px;
            top: 700px;
            width: 200px; // 缩小圆形icon
            height: 200px;
            border-radius: 50%;
            margin-bottom: 6px;
            background: linear-gradient(180deg, #398fff 0%, #0ff 100%);
            background-image: url("~@/assets/serve/government.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          .circle-icon-government:hover {
            background-image: url("~@/assets/serve/governmentHover.png");
          }
          .circle-icon-org {
            position: absolute;
            left: 1350px;
            top: 590px;
            width: 200px; // 缩小圆形icon
            height: 200px;
            border-radius: 50%;
            margin-bottom: 6px;
            background: linear-gradient(180deg, #398fff 0%, #0ff 100%);
            background-image: url("~@/assets/serve/institutions.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            cursor: pointer;
            transition: all 0.3s ease;
          }
          .circle-icon-org:hover {
            background-image: url("~@/assets/serve/institutionsHover.png");
          }
          .circle-title-enterprise {
            position: absolute;
            left: 255px;
            top: 690px;
            font-weight: 600;
            font-size: 40px;
            color: #d3f1ff;
            line-height: 56px;
            letter-spacing: 2px;
            text-align: right;
            font-style: normal;
            background: linear-gradient(90deg, #ffffff 0%, #b7deff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .circle-title-government {
            position: absolute;
            left: 815px;
            top: 800px;
            font-weight: 600;
            font-size: 40px;
            color: #d3f1ff;
            line-height: 56px;
            letter-spacing: 2px;
            text-align: right;
            font-style: normal;
            background: linear-gradient(90deg, #ffffff 0%, #b7deff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .circle-title-org {
            position: absolute;
            left: 1390px;
            top: 690px;
            font-weight: 600;
            font-size: 40px;
            color: #d3f1ff;
            line-height: 56px;
            letter-spacing: 2px;
            text-align: right;
            font-style: normal;
            background: linear-gradient(90deg, #ffffff 0%, #b7deff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .circle-desc-enterprise {
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            width: 370px;
            height: 80px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 130px;
            top: 820px;
          }
          .circle-desc-government {
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            width: 370px;
            height: 147px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 700px;
            top: 920px;
          }
          .circle-desc-org {
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            width: 370px;
            height: 80px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 1270px;
            top: 820px;
          }
        }
      }
    }
    .platform-section {
      display: flex;
      justify-content: center;
      gap: 36px; // 缩小间距
      margin-top: 10px;
      .platform-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        .platform-icon {
          width: 276.78px; // 缩小icon
          height: 220.13px;
          margin-bottom: 4px;
          &.model {
            background-image: url("~@/assets/serve/model.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            position: absolute;
            left: 100px;
            top: 1140px;
            cursor: pointer;
            transition: all 0.3s ease;
            &:hover {
              background-image: url("~@/assets/serve/modelHover.png");
            }
          }
          &.base {
            background-image: url("~@/assets/serve/base.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            position: absolute;
            left: 737px;
            top: 65vh;
            cursor: pointer;
            transition: all 0.3s ease;
            &:hover {
              background-image: url("~@/assets/serve/baseHover.png");
            }
          }
          &.ops {
            background-image: url("~@/assets/serve/ops.png");
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
            position: absolute;
            left: 1390px;
            top: 1140px;
            cursor: pointer;
            transition: all 0.3s ease;
            &:hover {
              background-image: url("~@/assets/serve/opsHover.png");
            }
          }
        }
        .platform-title {
          color: #b6d6ff;
          font-size: 12px; // 缩小
          margin-top: 1px;
          &.model {
            width: 310px;
            height: 80px;
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
              0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 80px;
            top: 1340px;
          }
          &.base {
            width: 310px;
            height: 80px;
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
              0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 730px;
            top: 76vh;
          }
          &.ops {
            width: 310px;
            height: 80px;
            background: rgba(12, 70, 153, 0.54);
            border: 1px solid;
            border-image: linear-gradient(
                135deg,
                rgba(140, 218, 255, 1),
                rgba(255, 255, 255, 1),
                rgba(84, 179, 255, 1)
              )
              1 1;
            backdrop-filter: blur(10px);
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 40px;
            color: #ffffff;
            line-height: 80px;
            letter-spacing: 2px;
            text-shadow: 0px 0px 12px rgba(0, 134, 255, 0.68),
              0px 0px 8px rgba(0, 116, 255, 0.5), 0px 2px 4px #000c1a;
            text-align: center;
            font-style: normal;
            position: absolute;
            left: 1380px;
            top: 1340px;
          }
        }
      }
    }
  }
}

/* 弹框样式 */
.popup-container {
  position: absolute;
  z-index: 2000;
  width: 423.42px;
  height: 423.42px;
  background-image: url("~@/assets/serve/serveHover.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 4px;
}
.enterprise-popup {
  top: 140px;
  left: 100px;
}

.government-popup {
  top: 240px;
  left: 660px;
}

.institutions-popup {
  top: 140px;
  left: 1230px;
}

.popup-content {
  margin-top: 70px;
  padding: 15px;
}

.popup-item {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28px;
  color: #ffffff;
  line-height: 40px;
  letter-spacing: 1px;
  text-shadow: 0px 0px 10px rgba(0, 204, 255, 0.5),
    0px 1px 3px rgba(0, 0, 0, 0.32);
  text-align: center;
  font-style: normal;
}
.model-popup {
  top: 740px;
  left: 30px;
}
.base-popup {
  top: 45vh;
  left: 660px;
}
.ops-popup {
  top: 740px;
  left: 1320px;
}
.popup-item:last-child {
  border-bottom: none;
}
</style>
