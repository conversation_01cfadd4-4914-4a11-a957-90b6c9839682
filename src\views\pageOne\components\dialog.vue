<!--
  数据趋势图表弹窗组件
  功能：
  1. 展示数据趋势折线图
  2. 支持日/月/年数据切换
  3. 提供图表交互和提示
  4. 支持自定义标题和单位
-->
<template>
  <div v-if="visible" class="dialog-mask" @click="handleMaskClick">
    <!-- 弹窗内容区域 -->
    <div class="dialog-content" @click.stop :class="customClass">
      <!-- 头部区域 -->
      <div class="header-box">
        <!-- 标题区域 -->
        <div class="dialog-header">
          <div class="titleBox">
            <span class="title">{{ title }}</span>
          </div>
        </div>
        <!-- 时间维度切换标签 -->
        <div class="tab-group">
          <div
            v-for="item in tabList"
            :key="item"
            :class="['tab-btn', { active: currentTab === item }]"
            @click="changeTab(item)"
          >
            {{ item }}
          </div>
        </div>
      </div>

      <!-- 图表主体区域 -->
      <div class="dialog-body">
        <div ref="chartRef" class="chart"></div>
      </div>
      <!-- 底部插槽区域 -->
      <div class="dialog-footer">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { queryLineChartData } from "@/api/article.js";

export default {
  name: "EchartsDialog",
  props: {
    // 指标名称
    indexName: { type: String, default: "累计汇集数据量" },
    // 控制弹窗显示
    visible: { type: Boolean, default: false },
    // 弹窗标题
    title: { type: String, default: "用户注册趋势" },
    // Y轴单位
    yLable: { type: String, default: "单位" },
    // 图表数据
    chartData: {
      type: Object,
      default: () => ({
        xAxis: ["2025年", "2024年", "2023年"],
        yAxis: [100, 200, 300, 400, 500],
        series: [320, 400, 350],
      }),
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null, // ECharts实例
      tabList: ["日", "月", "年"], // 时间维度选项
      currentTab: "年", // 当前选中的时间维度
      // 日维度数据
      dayData: {
        xAxis: Array.from({ length: 30 }, (_, i) => `${i + 1}日`),
        yAxis: [100, 200, 300, 400, 500],
        series: Array.from({ length: 30 }, () =>
          Math.floor(Math.random() * 400 + 100)
        ),
      },
      // 月维度数据
      monthData: {
        xAxis: [
          "202411",
          "202412",
          "202501",
          "202502",
          "202503",
          "202504",
          "202505",
          "202506",
          "202507",
          "202508",
          "202509",
          "202510",
        ],
        yAxis: [100, 200, 300, 400, 500],
        series: [320, 400, 350, 238, 300, 330, 310, 370, 340, 420, 410, 400],
      },
      // 年维度数据
      yearData: {
        xAxis: [
          `${new Date().getFullYear() - 2}年`,
          `${new Date().getFullYear() - 1}年`,
          `${new Date().getFullYear()}年`,
        ],
        yAxis: [100, 200, 300, 400, 500],
        series: [10743, 10521, 10967],
      },
      chartNewData: {}, // 当前展示的数据
      activeIndex: null, // 当前高亮的x轴索引
      dayTooltip: "", // 日维度提示
      currentChartData: {}, // 用于展示的chart数据
    };
  },
  created() {
    // 默认显示年数据
    this.chartNewData = this.yearData;
    this.currentChartData = { ...this.yearData };
  },
  watch: {
    // 监听时间维度切换
    async changeTab(tab) {
      this.currentTab = tab;
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, "0");
      const day = String(today.getDate()).padStart(2, "0");
      let bizDate = '';
      
      // 根据时间维度设置日期格式
      if (tab === "日") {
        bizDate = day;
      } else if (tab === "月") {
        bizDate = month;
      } else if (tab === "年") {
        bizDate = year;
      }

      // 获取对应时间维度的数据
      const res = await queryLineChartData({
        bizDate: `${bizDate}`,
        type: tab,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: this.indexName,
      }); 

      // 更新图表数据
      this.$nextTick(() => {
        this.chartData.xAxis = res.data.data.dataList;
        this.chartData.series = res.data.data.countList;
        this.initChart();
      });
    },
    // 监听弹窗显示状态
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.initChart();
        });
      } else {
        // 关闭时销毁实例，避免下次打开无数据
        if (this.chart) {
          this.chart.dispose();
          this.chart = null;
        }
      }
    },
  },
  methods: {
    // 切换时间维度
    async changeTab(tab) {
      this.currentTab = tab;
      const today = new Date();
      const year = today.getFullYear();
      // 获取当前月份202504
      let month = String(today.getMonth() + 1).padStart(2, "0");
      month = `${year}${month}`;
      // 获取今天的日期20250428
      let day = String(today.getDate()).padStart(2, "0");
      day = `${year}${month}${day}`;
      let bizDate = '';

      // 根据时间维度设置日期格式
      if (tab === "日") {
        bizDate = day;
      } else if (tab === "月") {
        bizDate = month;
      } else if (tab === "年") {
        bizDate = year;
      }

      // 获取对应时间维度的数据
      const res = await queryLineChartData({
        bizDate: `${bizDate}`,
        type: tab,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: this.indexName,
      }); 

      // 更新图表数据
      this.$nextTick(() => {
        this.chartData.xAxis = res.data.data.dataList;
        this.chartData.series = res.data.data.countList;
        this.initChart();
      });
    },

    // 关闭弹窗
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
      // 关闭时销毁实例，避免下次打开无数据
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
    },

    // 处理遮罩层点击
    handleMaskClick() {
      this.close();
    },

    // 初始化图表
    initChart() {
      if (!this.$refs.chartRef) return;
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartRef);
      }
      const that = this;
      const option = {
        // 图表网格配置
        grid: {
          top: "20%",
          bottom: "10%",
          left: "8%",
          right: "5%",
          borderColor: "#2A4B7C",
        },
        // X轴配置
        xAxis: {
          type: "category",
          data: this.chartData.xAxis,
          axisLine: { lineStyle: { color: "#6EC6FF" } },
          axisTick: {
            show: false  // 隐藏刻度线
          },
          axisLabel: {
            color: function (value, idx) {
              // 高亮当前activeIndex的x轴标签为橙色
              return idx === that.activeIndex ? "#FF9C00" : "#fff";
            },
            fontSize: that.$autoFontSize(14),
          },
        },
        // Y轴配置
        yAxis: {
          type: "value",
          data: this.chartData.yAxis,
          axisLine: { lineStyle: { color: "#6EC6FF" } },
          splitLine: { lineStyle: { color: "#2A4B7C" } },
          axisLabel: { color: "#fff", fontSize: that.$autoFontSize(14) },
          name: `${this.yLable}`, // y轴单位
          nameTextStyle: {
            color: "#fff",
            fontSize: that.$autoFontSize(14),
            padding: [0, that.$autoFontSize(40), that.$autoFontSize(30), 0],
          },
          nameLocation: "end", // 单位显示在顶部
        },
        // 提示框配置
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
            lineStyle: { color: "#fff" }
          },
          backgroundColor: "rgba(59, 188, 255, 0.2)",
          borderColor: "#3bbcff",
          borderWidth: 1,
          padding: 10,
          textStyle: {
            color: "#fff",
            fontSize: 16
          },
          formatter: function(params) {
            const param = params[0];
            return `${param.name}<br/>${that.indexName}：${param.value}`;
          }
        },
        // 数据系列配置
        series: [
          {
            data: this.chartData.series,
            type: "line",
            smooth: false,
            symbol: "circle",
            symbolSize: 0,
            // 线条样式
            lineStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "#3bbcff" }, // 渐变起始色
                { offset: 1, color: "#3bbcff" }, // 渐变结束色
              ]),
              width: 2.5,
            },
            // 区域填充样式
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(59, 188, 255, 0.5)" }, // 蓝色
                { offset: 1, color: "rgba(59, 188, 255, 0.8)" }, // 橙色
              ]),
            },
            itemStyle: { color: "#6EC6FF" },
            // 数据标签配置
            label: {
              show: true,
              position: "top",
              color: "#fff",
              fontSize: that.$autoFontSize(14),
              formatter: function (params) {
                // 根据时间维度控制标签显示
                if (that.currentTab === "日" && params.dataIndex % 7 === 0) {
                  return params.data;
                }
                if (that.currentTab === "月" && params.dataIndex % 2 === 0) {
                  return params.data;
                }
                if (that.currentTab === "年") {
                  return params.data;
                }
                return "";
              },
            },
            // 高亮配置
            emphasis: {
              focus: "series",
              symbol: "circle",
              symbolSize: this.$autoFontSize(14),
              itemStyle: {
                color: "#FF9C00", // 橙色小圆点
                borderColor: "#fff",
                borderWidth: 5,
              },
            },
          },
        ],
      };
      this.chart.setOption(option);
      this.chart.resize(); // 自适应容器
    },
  },
  mounted() {
    if (this.visible) {
      this.initChart();
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
};
</script>

<style scoped lang="scss">
/* 遮罩层 */
.dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 标题区域 */
.titleBox {
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  width: 940px;
  height: 89px;
  .title {
    padding-left: 50px;
    height: 89px;
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    margin-left: 50px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
  }
}

/* 弹窗内容区域 */
.dialog-content {
  background: url("~@/assets/dp/dialog.png");
  background-size: 100% 100%;
  border-radius: 12px;
  min-width: 1600px;
  min-height: 900px;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
}

/* 头部区域 */
.header-box {
  display: flex;
}

/* 弹窗头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 24px 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 标签组 */
.tab-group {
  display: flex;
  margin-left: 300px;
  margin-right: 30px;
  margin-top: 30px;
  height: 36px;
  position: relative;
}

/* 标签按钮 */
.tab-btn {
  font-family: PingFangSC;
  font-weight: 400;
  font-size: 34px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
  width: 140px;
  height: 65px;
  line-height: 65px;
  text-align: center;
  color: #67a7ff;
  border: 1px solid #4ea6ff;
  cursor: pointer;
  margin-left: -1px;
  transition: background 0.2s, color 0.2s;
  background: linear-gradient(
    180deg,
    rgba(5, 38, 83, 0) 0%,
    rgba(12, 74, 139, 0.49) 100%
  );
  box-shadow: inset 0px 0px 18px 0px #168aff;
  border: 2px solid rgba(110, 160, 227, 0.8);
}

/* 激活状态的标签按钮 */
.tab-btn.active {
  background: #2176c7;
  color: #fff;
  border: 1px solid #2176c7;
  z-index: 1;
}

/* 关闭按钮 */
.dialog-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 60px;
  position: absolute;
  top: -100px;
  right: 0;
  padding: 0;
  cursor: pointer;
}

/* 图表主体区域 */
.dialog-body {
  padding: 10px 24px 0 24px;
}

/* 图表容器 */
.chart {
  width: 100%;
  height: 800px;
}

/* 底部区域 */
.dialog-footer {
  padding: 10px 24px 18px 24px;
  text-align: right;
}
</style>
