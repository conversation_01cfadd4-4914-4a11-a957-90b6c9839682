<!-- 
  数据汇聚组件
  功能：展示各类数据汇聚情况，包括数据类型、数据名称、数据量和应用领域
  特点：
  1. 使用无缝滚动展示数据列表
  2. 支持鼠标悬停暂停滚动
  3. 支持鼠标滚轮控制滚动
  4. 响应式设计，自适应布局
-->
<template>
  <div class="fkyhlbClass">
    <!-- 头部区域：标题展示 -->
    <div class="headerImg">
      <div class="lbClass">数据汇聚</div>
      <div class="rightClass">
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>
    <!-- 主体内容区域：数据列表展示 -->
    <div class="bottomClass">
      <div class="box-con-list">
        <!-- 表头：定义数据展示的列名 -->
        <div class="tableHeader">
          <div class="table-name">
            <div>融合数据类型</div>
            <div>融合数据名称</div>
            <div>数据量</div>
            <div>数据应用领域</div>
          </div>
        </div>
        <!-- 数据列表：使用vue-seamless-scroll实现无缝滚动 -->
        <div v-if="carList.length > 0" class="warp">
          <vue-seamless-scroll
            ref="vueSeamlessScroll1"
            :data="carList"
            style="height: 100%; overflow: hidden"
            :class-option="classOption"
            @mousewheel.native="handleScroll"
          >
            <!-- 循环渲染数据行 -->
            <div v-for="(item, index) in carList" :key="index">
              <div class="table-items">
                <div style="width: 30%">{{ item.name }}</div>
                <div style="width: 30%">{{ item.name2 }}</div>
                <div style="width: 18%">{{ item.num }}</div>
                <div style="width: 22%">{{ item.field }}</div>
              </div>
            </div>
          </vue-seamless-scroll>
        </div>
        <!-- 无数据时显示提示信息 -->
        <div class="empty" v-else>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";

export default {
  name: "fkyhlb",
  components: {
    vueSeamlessScroll, // 注册无缝滚动组件
  },
  data() {
    return {
      // 无缝滚动配置选项
      classOption: {
        step: 0.2, // 滚动速度，数值越大滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量阈值
        hoverStop: true, // 是否开启鼠标悬停暂停滚动
        direction: 1, // 滚动方向：0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      },
      // 模拟数据列表
      carList: [
        {
          name: "政务类",
          name2: "企业工商注册信息",
          num: 10294,
        },
        {
          name: "金融类",
          name2: "银行信贷记录",
          num: 4598,
        },
        {
          name: "生活类",
          name2: "居民用电数据",
          num: 3891,
        },
        {
          name: "信用类",
          name2: "法院诉讼公告",
          num: 3124,
        },
        {
          name: "税务类",
          name2: "企业纳税信息",
          num: 2789,
        },
        {
          name: "社保类",
          name2: "社保缴纳明细",
          num: 2345,
        },
        {
          name: "知识产权类",
          name2: "专利信息",
          num: 18976,
        },
        {
          name: "交通类",
          name2: "车辆登记数据",
          num: 1543,
        },
      ],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    // 初始化方法
    init() {},
    
    // 显示内容模态框
    showConModal(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /picShow/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      if (data) {
        //点击事件处理逻辑
      }
    },
    
    // 处理鼠标滚轮事件
    handleScroll(e) {
      // 更新滚动位置
      this.$refs.vueSeamlessScroll1.yPos =
        this.$refs.vueSeamlessScroll1.yPos - e.deltaY + 60;
      // 限制向上滚动的位置
      if (this.$refs.vueSeamlessScroll1.yPos > 0) {
        this.$refs.vueSeamlessScroll1.yPos = 0;
        return;
      }
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 组件主容器样式 */
.fkyhlbClass {
  width: 940px;
  height: 26vh;
  display: flex;
  flex-direction: column;

  /* 头部区域样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    // 添加文字阴影效果
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    /* 标题样式 */
    .lbClass {
    }

    /* 右侧区域样式 */
    .rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      /* 查看更多按钮样式 */
      .djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      /* 箭头图标样式 */
      .imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  /* 底部内容区域样式 */
  .bottomClass {
    width: 100%;
    flex: 1;
    // 设置渐变背景
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);

    /* 列表容器样式 */
    .box-con-list {
      width: 100%;

      /* 表头样式 */
      .tableHeader {
        width: 100%;
        box-sizing: border-box;
        padding: 0 35px;
        padding-top: 30px;

        /* 表头名称样式 */
        .table-name {
          padding: 0 64px;
          box-sizing: border-box;
          display: flex;
          height: 80px;
          background: rgba(255, 255, 255, 0.09);
          font-weight: 500;
          font-size: 26px;
          color: #41ccff;
          letter-spacing: 1px;
          align-items: center;
          justify-content: space-between;
        }
      }
    }

    /* 数据列表区域样式 */
    .warp {
      height: 280px;
      padding: 0 35px;

      /* 数据行样式 */
      .table-items {
        display: flex;
        padding: 0 64px;
        box-sizing: border-box;
        height: 80px;
        align-items: center;
        font-weight: 400;
        font-size: 26px;
        color: #ffffff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.22);
      }
    }
  }
}
</style>
