# 大屏展示系统交接文档

## 项目概述
本项目是一个基于 Vue.js 的大屏展示系统，用于数据可视化展示。项目采用 Vue 2.x 版本开发，使用 Element UI 作为 UI 组件库，ECharts 作为图表库。所用的echars图表有柱状图，饼图，地图，需要兼容1080,2k，4k分辨率的内容
uat地址：https://ryd-uat.cdrongyidai.cn/dataView/index.html#/
prod地址：https://www.cdrongyidai.cn/dataView/index.html#/
登录账号：1487987555
登录密码：Cdjz.123
## 技术栈
- 前端框架：Vue 2.6.14
- UI 框架：Element UI 2.15.14
- 图表库：ECharts 5.6.0
- 状态管理：Vuex 3.6.2
- 路由管理：Vue Router 3.5.1
- 构建工具：Vue CLI 5.0.0
- CSS 预处理器：Sass
- HTTP 客户端：Axios 0.18.0

## 开发环境配置
1. Node.js 环境要求：建议使用 Node.js 14.x 或以上版本
2. 安装依赖：
```bash
npm install
```

## 项目结构
```
├── public/                 # 静态资源目录
├── src/                    # 源代码目录
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── router/            # 路由配置
│   ├── store/             # Vuex 状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── vue.config.js          # Vue 项目配置文件
└── package.json           # 项目依赖配置
```

## 开发命令
- 开发环境运行：`npm run serve`
- 生产环境构建：`npm run build`
- UAT环境构建：`npm run build-uat`
- 生产环境构建：`npm run build-prod`
-分析打包大小：‘’
- 代码检查：`npm run lint`
环境变量配置文件.env.production，.env.uat//分别配置生产和开发环境的环境变量

## 项目配置说明
详情见vue.config.js//配置项目
### 代理配置
项目配置了两个代理：
1. `/api` 接口代理到 `https://ryd-uat.cdrongyidai.cn`
### 生产环境优化
项目在生产环境下进行了以下优化：
1. 启用 Gzip 压缩
2. 代码分割和 Tree Shaking
3. CSS 压缩和优化
4. 移除 console 和 debugger
5. 代码混淆和压缩

### 代码分割策略
项目配置了多个代码分割组：
- vendors：第三方库
- elementUI：Element UI 组件库
- echarts：ECharts 相关代码
- common：公共模块
- styles：样式文件
- utils：工具库
- components：组件
- routes：路由
- vuex：状态管理

## 部署说明
1. 生产环境构建：
```bash
npm run build-prod
```

2. 构建产物位于 `dist` 目录，包含：
   - 压缩后的 JS 文件
   - 压缩后的 CSS 文件
   - 静态资源文件

3. 部署注意事项：
   - 确保服务器配置了正确的 MIME 类型
   - 配置适当的缓存策略
   - 确保服务器支持 Gzip 压缩

## 常见问题处理
1. 如果遇到依赖安装问题，可以尝试：
```bash
rm -rf node_modules
npm cache clean --force
npm install
```
2. 如果遇到构建失败，检查：
   - Node.js 版本是否符合要求
   - 是否所有依赖都正确安装
   - 是否有足够的磁盘空间






src下目录重点文件解释
### main.js
项目的入口文件，主要功能包括：
1. 基础配置
   - Vue 核心库引入
   - 路由配置引入
   - Vuex 状态管理引入
   - Element UI 按需引入（Button, Table, Pagination, Dialog, Tooltip）
2. 安全配置
   - 使用 JSEncrypt 实现 RSA 加密
   - 配置了固定的 RSA 公钥
   - 将加密实例挂载到 Vue 原型上，方便全局调用
3. 全局功能
   - 配置了 ECharts 字体自动缩放功能
   - 提供了全局错误处理机制（当前已注释）
### app.vue//加载字体和浏览器文件名
### router.index.js//路由引入，路由守卫
### article.js//api封装
### router.axios.js//项目的 HTTP 请求配置文件，主要功能包括：
1. 基础配置
   - 设置默认超时时间（60秒）
   - 配置状态码验证（200-500）
   - 配置跨域请求
   - 配置基础 URL
   - 配置 NProgress 进度条

2. 请求拦截器
   - 添加认证信息（Basic Auth）
   - 处理第三方登录认证
   - 添加 token 认证
   - 添加请求签名（timestamp + nonce + param）
   - 设置请求头信息
   - 处理序列化请求

3. 响应拦截器
   - 处理 blob 类型响应
   - 处理错误状态码
   - 处理 401 未授权（自动登出）
   - 统一错误提示
   - 特定接口错误处理

4. 安全特性
   - 请求签名机制
   - Token 认证
   - Basic Auth 认证
   - 参数加密传输

5. 特殊处理
   - FormData 处理
   - Blob 响应处理
   - 特定接口错误消息隐藏
   - 参数排序和签名
//component下的公共组件
changeButtom除首页其他三个下面的标题
dialogBar-park//放款企业产业园区分布用于第一屏和第三屏
dialogBar-x//echars图横向滚动
dialogBar//柱状图竖向滚动
headerRouter//四个路由跳转
login//登录页面
pageOne/components//首页的组件
changeButtom//下面的切换地图，走访轨迹和总地图
pageOne/pageOne//第一个页面的主页面
pageTwo/pageTwo//第二个页面的主页面
pageThree/pageThree//第三个页面的主页面
pageFour/pageFour//第四个页面的主页面
//1、注意，px转vh，vw的时候element-ui的内联样式不会发生改变，和echarts图表里的单位不会发生改变，需要用this.$autoFontSize(14)，去适配不同分辨率下的宽高和字体大小。
2、前端代码写完后要在1080,2k，4k的分辨率去适配一下，要求写的css样式兼容其他的样式
3、css样式命名不要重复，不然会样式冲突，或者不生效







###后端新增账号和密码
CREATE TABLE `blade_screen_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账号',
  `password` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '昵称',
  `phone` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机',
  `first_login` int DEFAULT '0' COMMENT '首次登陆',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` bigint DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT NULL COMMENT '状态',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除',
  `version` int DEFAULT NULL COMMENT '版本号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `account_index` (`account`,`password`) USING BTREE,
  KEY `phone_index` (`phone`,`password`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1929802450029584387 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='大屏用户表';


生成密码规则：
蓉易贷项目中起一个main方法执行System.out.println(Digestutil.encrypt(Digestutil.hex("你的密码明文")));
生成的密文对应表中password字段。