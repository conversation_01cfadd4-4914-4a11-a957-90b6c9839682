<!--
  金融生态数据展示组件
  功能：
  1. 展示入驻金融机构和上架金融产品的数据
  2. 提供环形图可视化展示
  3. 支持数据切换和详情查看
  4. 显示各类金融机构/产品的数量和占比
-->
<template>
<!--   备选方案废弃 -->
  <div class="fkyhlbClass">
    <!-- 头部区域 -->
    <div class="headerImg">
      <div>搭建金融生态</div>
      <!-- 切换按钮组 -->
      <div class="rightClass">
        <!-- 入驻金融机构按钮 -->
        <div 
          :class="[activeTab === 'institution' ? 'djgdClassOne1' : 'djgdClassOne2']" 
          @click="switchTab('institution')"
        >
          入驻金融机构
          <div class="guangClass" v-if="activeTab === 'institution'"></div>
        </div>
        <!-- 上架金融产品按钮 -->
        <div 
          :class="[activeTab === 'product' ? 'djgdClassOne1' : 'djgdClassOne2', 'djgdClassOne']" 
          @click="switchTab('product')"
        >
          上架金融产品
          <div class="guangClass" v-if="activeTab === 'product'"></div>
        </div>
      </div>
    </div>
    <div class="bottomClass2">
      <div class="circleClass">
        <!-- 图表容器 -->
        <div id="chart" style="width: 50%; height: 100%"></div>
        <!-- 中心数据展示 -->
        <div class="rzjgClass">
          <div class="numClass">{{ totol }}</div>
          <div class="rzjgName">{{ activeTab === 'institution' ? '入驻机构' : '上架产品' }}</div>
        </div>
        <!-- 自定义图例 -->
        <div class="custom-legend">
          <div
            v-for="(item, index) in listData"
            :key="activeTab === 'institution' ? item.financeType : item.datasSource"
            class="legend-item"
            @click="showDetailDialog(item)"
            style="cursor:pointer;"
          >
            <span
              class="legend-color"
              :style="{ background: getLegendColor(index) }"
            ></span>
            <span class="legend-name">{{ activeTab === 'institution' ? item.financeType : item.datasSource }}</span>
            <span
              class="legend-value"
              :style="{ color: getLegendColor(index) ,fontSize: '16px',fontFamily: 'PingFangSC'}"
            >{{ item.financeNum }}家</span>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="detailDialogVisible"
      :title="detailDialogTitle"
      width="400px"
      class="rzjrjg-dialog"
      :close-on-click-modal="true"
      :show-close="false"
      center>
      <div class="drill-dialog-content">
        <div v-for="child in detailDialogList" :key="child.name" class="drill-dialog-row">
          {{ child.name }}：{{ child.value }}家
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { productUpdate } from "@/api/article.js";

export default {
  name: "financialEcology",
  data() {
    return {
      listData: [], // 列表数据
      totol: "", // 总数
      myChart: "", // ECharts实例
      activeTab: 'institution', // 当前激活的标签
      detailDialogVisible: false, // 详情弹窗显示状态
      detailDialogTitle: '', // 详情弹窗标题
      detailDialogList: [], // 详情弹窗数据列表
    };
  },
  props:{
    maxDt: {
      type: String,
      required: true
    },
    isMaxDtReady: {
      type: Boolean,
      required: true
    }
  },
  watch: {
    // 监听最大日期准备状态
    isMaxDtReady: {
      handler(newVal) {
        if (newVal) {
          this.getData();
          window.addEventListener('resize', this.handleResize);
        }
      },
      immediate: true
    },
    // 监听标签切换
    activeTab: {
      handler() {
        this.getData();
      }
    }
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },
    // 切换标签
    switchTab(tab) {
      if(tab !== this.activeTab) {
        this.listData = [];
        this.activeTab = tab;
      }
    },
    // 从字符串中提取数字
    extractNumber(str) {
      if (!str) return 0;
      const match = str.match(/\d+/);
      return match ? parseInt(match[0]) : 0;
    },
    // 获取数据
    async getData() {
      const res = await productUpdate({
        bizDate: this.maxDt,
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimIndustryChain: "ALL",
        indexName: this.activeTab === 'institution' ? "入驻金融机构数量" : "上架金融产品数量",
      });
      
      // 处理返回的数据
      this.listData = JSON.parse(res.data.data[0].bizContent);
      this.listData = this.listData.map((item) => {
        item.financeNum = this.extractNumber(
          this.activeTab === 'institution' ? item.financeSum : item.productSum
        );
        
        // 为金融机构类型添加子类数据
        if (this.activeTab === 'institution') {
          if (item.financeType === "银行行业金融机构") {
            item.children = [
              { name: "本地法人银行机构", value: 123 },
              { name: "银行分支机构", value: 12 },
              { name: "村镇银行", value: 12 },
              { name: "外资银行", value: 12 }
            ];
          } else if (item.financeType === "地方金融组织") {
            item.children = [
              { name: "小额贷款公司", value: 8 },
              { name: "融资租赁公司", value: 5 },
              { name: "商业保理公司", value: 3 },
              { name: "典当行", value: 2 }
            ];
          } else if (item.financeType === "股权融资机构") {
            item.children = [
              { name: "证券机构", value: 6 },
              { name: "基金业机构", value: 4 },
              { name: "期货业机构", value: 2 }
            ];
          } else if (item.financeType === "保险业机构") {
            item.children = [
              { name: "财产保险机构", value: 10 },
              { name: "人身保险机构", value: 8 },
              { name: "再保险机构", value: 1 }
            ];
          } else {
            item.children = [
              { name: "其他子类1", value: 1 },
              { name: "其他子类2", value: 2 }
            ];
          }
        }
        return item;
      });
      
      this.totol = JSON.parse(res.data.data[0].indexValue);
      this.init();
    },
    // 初始化图表
    init() {
      this.myChart = echarts.init(document.getElementById("chart"));
      this.updateChart();
    },
    // 更新图表配置和数据
    updateChart() {
      const chartData = this.listData.map((item) => {
        return {
          value: item.financeNum,
          name: this.activeTab === 'institution' ? item.financeType : item.datasSource,
        };
      });

      // 生成渐变色
      const getThemeGradientColor = (index) => {
        const endColors = [
          '#00FDDA', '#FFD01D', '#398FFF', '#00CDCD', '#4169E1', '#FFB90F',
          '#1E90FF', '#00CED1', '#87CEFA', '#F0E68C', '#48D1CC', '#6495ED',
          '#FFEC8B', '#F5873D'
        ];
        
        return {
          type: "linear",
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: endColors[index % endColors.length] },
            { offset: 1, color: endColors[index % endColors.length] },
          ],
        };
      };

      const themeGradientColors = chartData.map((_, index) => getThemeGradientColor(index));
      const legendColors = themeGradientColors.map(gradient => gradient.colorStops[1].color);

      // 图表配置项
      const option = {
        legend: {
          show: false
        },
        series: [
          {
            name: this.activeTab === 'institution' ? "金融机构类型" : "产品类型",
            type: "pie",
            radius: ["80%", "95%"],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            color: themeGradientColors,
            data: chartData,
          },
        ],
      };

      this.myChart.setOption(option, true);
    },
    // 获取图例颜色
    getLegendColor(index) {
      const endColors = [
        '#00FDDA', '#FFD01D', '#398FFF', '#00CDCD', '#4169E1', '#FFB90F',
        '#1E90FF', '#00CED1', '#87CEFA', '#F0E68C', '#48D1CC', '#6495ED',
        '#FFEC8B', '#F5873D'
      ];
      return endColors[index % endColors.length];
    },
    // 显示详情弹窗
    showDetailDialog(item) {
      if (this.activeTab === 'institution' && item.children) {
        this.detailDialogTitle = item.financeType;
        this.detailDialogList = item.children;
        this.detailDialogVisible = true;
      }
    },
  },
};
</script>

<style scoped lang="scss">
/* 主容器样式 */
.fkyhlbClass {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;
  
  /* 头部区域样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    
    /* 右侧按钮组样式 */
    .rightClass {
      display: flex;
      .djgdClassOne {
        position: relative;
        right: -1px;
      }
      /* 激活状态按钮样式 */
      .djgdClassOne1 {
        width: 200px;
        height: 56px;
        background: linear-gradient(180deg, #052A53 0%, #033D7B 63%, #0047A8 100%);
        border: 1px solid #1790FF;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #FFFFFF;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
      }
      /* 未激活状态按钮样式 */
      .djgdClassOne2 {
        width: 200px;
        height: 56px;
        background: linear-gradient(180deg, rgba(5,38,83,0) 0%, rgba(12,74,139,0.49) 100%);
        box-shadow: inset 0px 0px 14px 0px #168AFF;
        border: 2px solid rgba(110,160,227,0.8);
        position: relative;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #67A7FF;
        letter-spacing: 1px;
        text-shadow: 0px 1px 1px rgba(0,0,0,0.5);
      }
    }
  }

  /* 光效样式 */
  .guangClass {
    width: 220px;
    height: 12px;
    background: radial-gradient(397% 76% at 50% 50%, #97CAEB 0%, #048EE6 100%);
    filter: blur(4.338461538461543px);
    position: absolute;
    bottom: 0;
  }

  /* 主体内容区域样式 */
  .bottomClass2 {
    box-sizing: border-box;
    width: 100%;
    padding: 20px 0 0 20px;
    flex: 1;
    display: flex;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);

    /* 环形图容器样式 */
    .circleClass {
      width: 100%;
      height: 354px;
      display: flex;
      align-items: center;
      justify-content: left;
      margin-right: 70px;
      #chart {
        width: 310px;
        height: 310px;
      }
      /* 中心数据展示样式 */
      .rzjgClass {
        width: 200px;
        height: 200px;
        margin-left: 108px;
        border-radius: 110px;
        background: #052a53;
        box-shadow: inset 0px 0px 36px 0px #009eff;
        border: 2px solid rgba(255, 255, 255, 0.3);
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        /* 数字样式 */
        .numClass {
          font-family: OPPOSans;
          font-weight: normal;
          font-size: 57px;
          color: #ffffff;
          letter-spacing: 2px;
          text-shadow: 0px 0px 21px rgba(27, 144, 255, 0),
            0px 3px 4px rgba(0, 0, 0, 0.5);
          text-align: right;
          font-style: normal;
        }

        /* 名称样式 */
        .rzjgName {
          font-family: PingFangSC;
          font-weight: 400;
          font-size: 28px;
          color: #ffffff;
          margin-top: 20px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}

/* 自定义图例样式 */
.custom-legend {
  display: flex;
  flex-direction: column;
  align-items: start;
  margin-left: 30px;
  margin-top: 10px;
}

/* 图例项样式 */
.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 20px;
  color: #fff;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.2s;
  padding: 5px 10px;

  &:hover {
    background: rgba(59, 188, 255, 0.12);
  }
}

/* 图例颜色块样式 */
.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  margin-right: 8px;
  display: inline-block;
}

/* 图例名称样式 */
.legend-name {
  margin-right: 8px;
}

/* 图例数值样式 */
.legend-value {
  font-weight: bold;
}

/* 弹窗样式 */
.rzjrjg-dialog .el-dialog {
  background: #0a1d38;
}

/* 弹窗内容样式 */
.drill-dialog-content {
  padding: 10px 0;
}

/* 弹窗行样式 */
.drill-dialog-row {
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 8px 20px;
  margin-bottom: 10px;
  font-size: 22px;
  color: #fff;
}

/* 弹窗头部样式 */
::v-deep .el-dialog__header {
  background: linear-gradient(
    270deg,
    rgba(6, 145, 255, 0) 0%,
    rgba(6, 145, 255, 0.34) 100%
  );
  text-align: left;
}

/* 弹窗标题样式 */
.el-dialog__title {
  font-family: PangMenZhengDao;
  color: #fff !important;
  font-weight: bold;
  font-size: 42px;
}
</style> 